#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
因子映射表制造脚本
根据factorDescNew.csv中的因子编号，从Alpha514.py中找到对应的前缀，
然后从factorCard文件夹中提取相应的公式内容，并更新到CSV文件中。
"""

import pandas as pd
import re
import os
from pathlib import Path

def smart_replace_newlines(text):
    """
    智能替换换行符，保护数学公式中的换行符
    
    Args:
        text: 输入文本
    
    Returns:
        str: 处理后的文本
    """
    if not text:
        return text
    
    # 找到所有数学公式的位置（$$...$$和$...$）
    math_ranges = []
    
    # 查找$$...$$
    double_dollar_pattern = r'\$\$.*?\$\$'
    for match in re.finditer(double_dollar_pattern, text, re.DOTALL):
        math_ranges.append((match.start(), match.end()))
    
    # 查找$...$（但排除已经在$$...$$中的）
    single_dollar_pattern = r'\$[^$]*?\$'
    for match in re.finditer(single_dollar_pattern, text):
        # 检查是否在$$...$$范围内
        in_double_dollar = False
        for start, end in math_ranges:
            if match.start() >= start and match.end() <= end:
                in_double_dollar = True
                break
        if not in_double_dollar:
            math_ranges.append((match.start(), match.end()))
    
    # 按位置排序
    math_ranges.sort()
    
    # 分段处理文本
    result = ""
    last_pos = 0
    
    for start, end in math_ranges:
        # 处理数学公式前的文本
        before_math = text[last_pos:start]
        result += before_math.replace('\n', '<br/>')
        
        # 保持数学公式中的换行符不变
        math_content = text[start:end]
        result += math_content
        
        last_pos = end
    
    # 处理最后一段文本
    after_math = text[last_pos:]
    result += after_math.replace('\n', '<br/>')
    
    return result

def extract_prefix_from_alpha514(factor_name):
    """
    从Alpha514.py文件中提取指定因子的前缀
    
    Args:
        factor_name: 因子名称，如 "factor_1"
    
    Returns:
        tuple: (因子集名称, 序号) 或 (None, None) 如果未找到
    """
    try:
        with open('Alpha514.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找因子函数定义
        pattern = rf'def {factor_name}\('
        match = re.search(pattern, content)
        
        if not match:
            print(f"未找到因子函数: {factor_name}")
            return None, None
        
        # 从匹配位置向前查找前缀行
        start_pos = match.start()
        lines_before = content[:start_pos].split('\n')
        
        # 查找前缀行（格式：# 前缀: Alpha191_64 或 Alpha101_factor_101）
        for line in reversed(lines_before[-10:]):  # 只查看前10行
            if line.strip().startswith('# 前缀:'):
                prefix = line.strip().replace('# 前缀:', '').strip()
                # 解析前缀
                if '_' in prefix:
                    # 处理Alpha101_factor_101这种格式
                    if prefix.startswith('Alpha101_factor_'):
                        factor_set = "Alpha101"
                        factor_num = prefix.replace('Alpha101_factor_', '')
                    else:
                        # 处理Alpha191_64这种格式
                        factor_set, factor_num = prefix.rsplit('_', 1)
                    return factor_set, factor_num
                else:
                    print(f"前缀格式不正确: {prefix}")
                    return None, None
        
        print(f"未找到因子 {factor_name} 的前缀")
        return None, None
        
    except Exception as e:
        print(f"读取Alpha514.py文件时出错: {e}")
        return None, None

def extract_formula_from_factorcard(factor_set, factor_num):
    """
    从factorCard文件夹中提取因子公式
    
    Args:
        factor_set: 因子集名称，如 "Alpha191"
        factor_num: 因子序号，如 "64"
    
    Returns:
        str: 提取的公式内容，如果未找到则返回None
    """
    try:
        # 构建文件路径
        if factor_set == "Alpha101":
            file_path = f"factorCard/Alpha101/factor_{factor_num}.md"
        else:
            file_path = f"factorCard/{factor_set}/{factor_num}.md"
        
        if not os.path.exists(file_path):
            print(f"因子卡片文件不存在: {file_path}")
            return None
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找【2. 核心公式】和【3. 变量定义】之间的内容
        # 对于Alpha101格式
        if factor_set == "Alpha101":
            # 查找### 核心公式 到 ### 变量定义之间的内容
            pattern = r'### 核心公式(.*?)### 变量定义'
            match = re.search(pattern, content, re.DOTALL)
            if match:
                formula_content = match.group(1).strip()
                # 清理内容，移除markdown格式
                formula_content = re.sub(r'- \*\*说明\*\*：', '', formula_content)
                formula_content = formula_content.strip()
                return formula_content
        else:
            # 对于Alpha191等格式
            pattern = r'【2\. 核心公式】(.*?)【3\. 变量定义】'
            match = re.search(pattern, content, re.DOTALL)
            if match:
                formula_content = match.group(1).strip()
                return formula_content
        
        print(f"未找到公式内容: {file_path}")
        return None
        
    except Exception as e:
        print(f"读取因子卡片文件时出错: {e}")
        return None

def process_factor_mapping():
    """
    处理因子映射表
    """
    try:
        # 读取factorDescNew.csv
        df = pd.read_csv('factorDescNew.csv', encoding='utf-8')
        
        # 添加公式列（如果不存在）
        if '公式' not in df.columns:
            df['公式'] = ''
        
        # 处理每一行
        for index, row in df.iterrows():
            factor_name = row['因子编号']
            i = row['i']
            
            print(f"处理因子 {factor_name} (i={i})...")
            
            # 通过i值构造factor_名称，然后从Alpha514.py中提取前缀
            factor_function_name = f"factor_{i}"
            factor_set, factor_num = extract_prefix_from_alpha514(factor_function_name)
            
            if factor_set and factor_num:
                print(f"  找到前缀: {factor_set}_{factor_num}")
                
                # 从factorCard中提取公式
                formula = extract_formula_from_factorcard(factor_set, factor_num)
                
                if formula:
                    # 智能替换换行符，保护数学公式中的换行
                    formula_cleaned = smart_replace_newlines(formula)
                    df.at[index, '公式'] = formula_cleaned
                    print(f"  成功提取公式")
                else:
                    print(f"  未找到公式")
            else:
                print(f"  未找到前缀")
        
        # 保存更新后的CSV文件
        df.to_csv('factorDescNew.csv', index=False, encoding='utf-8')
        print("映射表更新完成！")
        
    except Exception as e:
        print(f"处理过程中出错: {e}")

if __name__ == "__main__":
    process_factor_mapping()
