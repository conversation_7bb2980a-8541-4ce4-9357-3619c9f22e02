【因子信息开始】===============================================================

【因子编号和名称】

因子152: Alpha 152 (Alpha 152, A152)

【1. 因子名称详情】

因子152: Alpha 152 (Alpha 152, A152)

【2. 核心公式】

$$\text{Alpha152} = \text{ts_corr}(6, \text{delta}(\text{low}, 4), \text{neg}(\text{volume}))$$

【3. 变量定义】

* `low`: 最低价。
* `volume`: 成交量。

【4. 函数与方法说明】

* `delta(X, n)`: 计算 X 在当前周期的值与 n 个周期前的值的差。公式：$X_t - X_{t-n}$。
* `neg(X)`: 对输入数据 X 逐元素取负。公式：$-X$。
* `ts_corr(n, X, Y)`: 计算 X 和 Y 在过去 n 个周期内的滚动相关系数。公式：$\text{Corr}_n(X, Y)_t = \frac{\text{Cov}_n(X, Y)_t}{\text{StdDev}_n(X)_t \times \text{StdDev}_n(Y)_t}$。计算时 `min_periods=1`。

【5. 计算步骤】

1.  计算最低价 (`low`) 在过去4个周期内的差值： $X_1 = \text{delta}(\text{low}, 4)$。
2.  计算成交量 (`volume`) 的相反数： $X_2 = \text{neg}(\text{volume})$。
3.  计算 $X_1$ 和 $X_2$ 在过去6个周期内的滚动相关系数得到 Alpha152： $\text{Alpha152} = \text{ts_corr}(6, X_1, X_2)$。
4.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：4, 6。
* 滚动相关系数计算设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】