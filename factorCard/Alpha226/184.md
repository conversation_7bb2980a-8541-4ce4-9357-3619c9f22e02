【因子信息开始】===============================================================
【因子编号和名称】
因子编号: Alpha_184 (Alpha_184)
【1. 因子名称详情】
因子全称: Alpha_184
【2. 核心公式】
$$Alpha_{184} = \text{add}(\text{delta}(\text{rank}(\text{arctan}(\text{volume})), 5), \text{ts_std}(\text{log}(\text{volume}), 10))$$
【3. 变量定义】
\begin{itemize}
    \item $volume_t$: t时刻的成交量
\end{itemize}
【4. 函数与方法说明】
\begin{itemize}
    \item $\text{arctan}(data)$: 计算data的反正切值。
    \item $\text{rank}(data)$: 计算data在截面上的排名（axis=1）。即对每一天，所有股票的因子值进行排序并赋予排名。
    \item $\text{delta}(data, window)$: 计算当前值与window期前值的差，即 $data_t - data_{t-window}$。
    \item $\text{delay}(data, window)$: 获取data在window期前的值，即 $data_{t-window}$。
    \item $\text{log}(data)$: 计算data的自然对数，对于非正数则取绝对值后再取对数，即 $\ln(|data_t|)$。
    \item $\text{ts_std}(data, n)$: 计算data在过去n期（包括当期）的滚动标准差。计算时，当期之前的可用数据不足n期时，则使用实际可用期数计算（min\_periods=1）。公式为：$\sqrt{\frac{1}{N-1} \sum_{i=1}^{N} (x_i - \bar{x})^2}$，其中N为窗口期n内实际观测值数量。
    \item $\text{add}(x, y)$: 元素级别的加法，即 $x_t + y_t$。
\end{itemize}
【5. 计算步骤】
\begin{enumerate}
    \item 对成交量 ($volume_t$) 应用反正切函数：$I_1 = \text{arctan}(volume_t)$。
    \item 对 $I_1$ 进行截面排名：$I_2 = \text{rank}(I_1)$。
    \item 计算 $I_2$ 的5期差值：$I_3 = \text{delta}(I_2, 5)$。
    \item 对成交量 ($volume_t$) 取自然对数：$I_4 = \text{log}(volume_t)$。
    \item 计算 $I_4$ 在过去10期的滚动标准差：$I_5 = \text{ts_std}(I_4, 10)$。
    \item 计算 $I_3$ 与 $I_5$ 的和，得到因子值：$Alpha_{184} = \text{add}(I_3, I_5)$。
    \item 将结果中的正无穷 ($\text{np.inf}$) 和负无穷 ($-\text{np.inf}$) 替换为NaN。
\end{enumerate}
【6. 备注与参数说明】
\begin{itemize}
    \item 窗口期参数：delta为5，ts\_std为10。
    \item 数据预处理：原始数据加载时已去除填充值。因子计算结果中的无穷值被替换为NaN。
\end{itemize}
【因子信息结束】