【因子信息开始】===============================================================
【因子编号和名称】
因子编号: Alpha_173 (Alpha_173)
【1. 因子名称详情】
因子全称: Alpha_173
【2. 核心公式】
$$Alpha_{173} = \text{rank}(\text{ts_cov}(5, \text{volume}, \text{high}))$$
【3. 变量定义】
\begin{itemize}
    \item $volume_t$: t时刻的成交量
    \item $high_t$: t时刻的最高价
\end{itemize}
【4. 函数与方法说明】
\begin{itemize}
    \item $\text{ts_cov}(window, x, y)$: 计算x和y在过去window期（包括当期）的滚动协方差。计算时，当期之前的可用数据不足window期时，则使用实际可用期数计算（min\_periods=1）。公式为：$\frac{1}{N-1} \sum_{i=1}^{N} (x_i - \bar{x})(y_i - \bar{y})$，其中N为窗口期window内实际观测值数量。
    \item $\text{rank}(data)$: 计算data在截面上的排名（axis=1）。即对每一天，所有股票的因子值进行排序并赋予排名。
\end{itemize}
【5. 计算步骤】
\begin{enumerate}
    \item 计算成交量 ($volume_t$) 和最高价 ($high_t$) 在过去5期的滚动协方差：$I_1 = \text{ts_cov}(5, volume_t, high_t)$。
    \item 对 $I_1$ 在每个时间截面上进行排名，得到因子值：$Alpha_{173} = \text{rank}(I_1)$。
\end{enumerate}
【6. 备注与参数说明】
\begin{itemize}
    \item 窗口期参数：ts\_cov为5。
    \item 数据预处理：原始数据加载时已去除填充值。
    \item 此因子最后一步为截面rank，没有对无穷值进行替换的步骤。
\end{itemize}
【因子信息结束】