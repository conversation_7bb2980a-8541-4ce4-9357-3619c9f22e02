【因子信息开始】===============================================================

【因子编号和名称】

因子2: Alpha 2 (Alpha 2, A2)

【1. 因子名称详情】

因子2: Alpha 2 (Alpha 2, A2)

【2. 核心公式】

$$\text{Alpha2} = \text{sub} \left( \text{ts_corr} \left( 11, \text{sigmoid}(\text{close}), \text{ts_corr} \left( 10, \text{delta}(\text{open_price}, 2), \text{delta}(\text{amount}, 2) \right) \right), \text{ts_corr}(12, \text{add}(\text{high}, \text{volume}), \text{high}) \right)$$

【3. 变量定义】

* `close`: 收盘价，指交易日结束时的证券价格。
* `open_price`: 开盘价，指交易日开始时的证券价格。
* `amount`: 成交额，指在一定时间内特定证券交易的总金额。
* `high`: 最高价，指在一定时间内的最高成交价格。
* `volume`: 成交量，指在一定时间内特定证券总共交易的股数。

【4. 函数与方法说明】

* `sigmoid(X)`: 计算输入数据 X 逐元素的 Sigmoid 函数值。公式：$\sigma(X) = \frac{1}{1 + e^{-X}}$。
* `delta(X, n)`: 计算 X 在当前周期的值与 n 个周期前的值的差。公式：$X_t - X_{t-n}$。
* `ts_corr(n, X, Y)`: 计算 X 和 Y 在过去 n 个周期内的滚动相关系数。公式：$\text{Corr}_n(X, Y)_t = \frac{\text{Cov}_n(X, Y)_t}{\text{StdDev}_n(X)_t \times \text{StdDev}_n(Y)_t}$。计算时 `min_periods=1`。
* `add(A, B)`: 对两个输入数据 A 和 B 进行逐元素加法操作。公式：$A + B$。
* `sub(A, B)`: 对输入数据 A 减去 B 进行逐元素减法操作。公式：$A - B$。

【5. 计算步骤】

1.  计算收盘价 (`close`) 的 Sigmoid 值： $X_1 = \text{sigmoid}(\text{close})$。
2.  计算开盘价 (`open_price`) 在过去2个周期内的差值： $X_2 = \text{delta}(\text{open_price}, 2)$。
3.  计算成交额 (`amount`) 在过去2个周期内的差值： $X_3 = \text{delta}(\text{amount}, 2)$。
4.  计算 $X_2$ 和 $X_3$ 在过去10个周期内的滚动相关系数： $X_4 = \text{ts_corr}(10, X_2, X_3)$。
5.  计算 $X_1$ 和 $X_4$ 在过去11个周期内的滚动相关系数： $X_5 = \text{ts_corr}(11, X_1, X_4)$。
6.  计算最高价 (`high`) 与成交量 (`volume`) 的和： $X_6 = \text{add}(\text{high}, \text{volume})$。
7.  计算 $X_6$ 和最高价 (`high`) 在过去12个周期内的滚动相关系数： $X_7 = \text{ts_corr}(12, X_6, \text{high})$。
8.  计算 $X_5$ 与 $X_7$ 的差值得到 Alpha2： $\text{Alpha2} = \text{sub}(X_5, X_7)$。
9.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：2, 2, 10, 11, 12。
* 所有滚动计算均设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】