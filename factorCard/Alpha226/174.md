【因子信息开始】===============================================================
【因子编号和名称】
因子编号: Alpha_174 (Alpha_174)
【1. 因子名称详情】
因子全称: Alpha_174
【2. 核心公式】
$$Alpha_{174} = \text{ts_corr}(12, \text{log}(\text{volume}), \text{gp_max}(\text{open_price}, \text{close}))$$
【3. 变量定义】
\begin{itemize}
    \item $volume_t$: t时刻的成交量
    \item $open\_price_t$: t时刻的开盘价
    \item $close_t$: t时刻的收盘价
\end{itemize}
【4. 函数与方法说明】
\begin{itemize}
    \item $\text{log}(data)$: 计算data的自然对数，对于非正数则取绝对值后再取对数，即 $\ln(|data_t|)$。
    \item $\text{gp_max}(data1, data2)$: 元素级别的较大值，即 $\max(data1_t, data2_t)$。
    \item $\text{ts_corr}(window, x, y)$: 计算x和y在过去window期（包括当期）的滚动相关系数。计算时，当期之前的可用数据不足window期时，则使用实际可用期数计算（min\_periods=1）。公式为：$\frac{\text{Cov}(x, y, \text{window})}{\text{StdDev}(x, \text{window}) \times \text{StdDev}(y, \text{window})}$。
\end{itemize}
【5. 计算步骤】
\begin{enumerate}
    \item 对成交量 ($volume_t$) 取自然对数：$I_1 = \text{log}(volume_t)$。
    \item 取开盘价 ($open\_price_t$) 和收盘价 ($close_t$) 中元素级别的较大值：$I_2 = \text{gp_max}(open\_price_t, close_t)$。
    \item 计算 $I_1$ 和 $I_2$ 在过去12期的滚动相关系数，得到因子值：$Alpha_{174} = \text{ts_corr}(12, I_1, I_2)$。
    \item 将结果中的正无穷 ($\text{np.inf}$) 和负无穷 ($-\text{np.inf}$) 替换为NaN。
\end{enumerate}
【6. 备注与参数说明】
\begin{itemize}
    \item 窗口期参数：ts\_corr为12。
    \item 数据预处理：原始数据加载时已去除填充值。因子计算结果中的无穷值被替换为NaN。
\end{itemize}
【因子信息结束】