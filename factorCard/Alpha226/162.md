【因子信息开始】===============================================================
【因子编号和名称】
因子编号: Alpha_162 (Alpha_162)
【1. 因子名称详情】
因子全称: Alpha_162
【2. 核心公式】
$$Alpha_{162} = \text{sub}(\text{ts_corr}(11, \text{sigmoid}(\text{close}), \text{ts_corr}(10, \text{delta}(\text{open_price}, 2), \text{delta}(\text{amount}, 2))), \text{ts_corr}(12, \text{add}(\text{high}, \text{volume}), \text{high}))$$
【3. 变量定义】
\begin{itemize}
    \item $close_t$: t时刻的收盘价
    \item $open\_price_t$: t时刻的开盘价
    \item $amount_t$: t时刻的成交额
    \item $high_t$: t时刻的最高价
    \item $volume_t$: t时刻的成交量
\end{itemize}
【4. 函数与方法说明】
\begin{itemize}
    \item $\text{sigmoid}(x)$: Sigmoid函数，定义为 $1 / (1 + e^{-x})$。
    \item $\text{delta}(data, window)$: 计算当前值与window期前值的差，即 $data_t - data_{t-window}$。
    \item $\text{delay}(data, window)$: 获取data在window期前的值，即 $data_{t-window}$。
    \item $\text{ts_corr}(window, x, y)$: 计算x和y在过去window期（包括当期）的滚动相关系数。计算时，当期之前的可用数据不足window期时，则使用实际可用期数计算（min\_periods=1）。公式为：$\frac{\text{Cov}(x, y, \text{window})}{\text{StdDev}(x, \text{window}) \times \text{StdDev}(y, \text{window})}$。
    \item $\text{add}(x, y)$: 元素级别的加法，即 $x_t + y_t$。
    \item $\text{sub}(x, y)$: 元素级别的减法，即 $x_t - y_t$。
\end{itemize}
【5. 计算步骤】
\begin{enumerate}
    \item 对收盘价 ($close_t$) 应用Sigmoid函数：$I_1 = \text{sigmoid}(close_t)$。
    \item 计算开盘价 ($open\_price_t$) 的2期差值：$I_2 = \text{delta}(open\_price_t, 2)$。
    \item 计算成交额 ($amount_t$) 的2期差值：$I_3 = \text{delta}(amount_t, 2)$。
    \item 计算 $I_2$ 和 $I_3$ 在过去10期的滚动相关系数：$I_4 = \text{ts_corr}(10, I_2, I_3)$。
    \item 计算 $I_1$ 和 $I_4$ 在过去11期的滚动相关系数：$I_5 = \text{ts_corr}(11, I_1, I_4)$。
    \item 计算最高价 ($high_t$) 与成交量 ($volume_t$) 的和：$I_6 = \text{add}(high_t, volume_t)$。
    \item 计算 $I_6$ 和最高价 ($high_t$) 在过去12期的滚动相关系数：$I_7 = \text{ts_corr}(12, I_6, high_t)$。
    \item 计算 $I_5$ 与 $I_7$ 的差值，得到因子值：$Alpha_{162} = \text{sub}(I_5, I_7)$。
    \item 将结果中的正无穷 ($\text{np.inf}$) 和负无穷 ($-\text{np.inf}$) 替换为NaN。
\end{enumerate}
【6. 备注与参数说明】
\begin{itemize}
    \item 窗口期参数：delta为2，第一个ts\_corr为10，第二个ts\_corr为11，第三个ts\_corr为12。
    \item 数据预处理：原始数据加载时已去除填充值。因子计算结果中的无穷值被替换为NaN。
\end{itemize}
【因子信息结束】