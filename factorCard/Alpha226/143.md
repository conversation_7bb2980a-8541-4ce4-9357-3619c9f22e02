【因子信息开始】===============================================================

【因子编号和名称】

因子143: Alpha 143 (Alpha 143, A143)

【1. 因子名称详情】

因子143: Alpha 143 (Alpha 143, A143)

【2. 核心公式】

$$\text{Alpha143} = \text{ts_regres} \left( \text{div} \left( \text{close}, \text{ts_regbeta}(\text{high}, \text{low}, 18) \right), \text{open_price}, 16 \right)$$

【3. 变量定义】

* `close`: 收盘价。
* `high`: 最高价。
* `low`: 最低价。
* `open_price`: 开盘价。

【4. 函数与方法说明】

* `ts_regbeta(X, Y, n)`: 计算 Y 对 X 在过去 n 个周期内的滚动回归的贝塔系数。公式：$\beta_{Y,X,n} = \frac{\text{Cov}_n(Y, X)}{\text{Var}_n(X)}$。
* `div(A, B)`: 对输入数据 A 除以 B 进行逐元素除法操作。公式：$A / B$。
* `ts_regres(X, Y, n)`: 计算 Y 对 X 在过去 n 个周期内的滚动回归的残差。公式：$\text{Resid}_{Y,X,n} = Y - \beta_{Y,X,n} \times X$。

【5. 计算步骤】

1.  计算最低价 (`low`) 对最高价 (`high`) 在过去18个周期内的滚动回归贝塔系数： $T_1 = \text{ts_regbeta}(\text{high}, \text{low}, 18)$。
2.  计算收盘价 (`close`) 除以 $T_1$： $X_1 = \text{div}(\text{close}, T_1)$。
3.  计算开盘价 (`open_price`) 对 $X_1$ 在过去16个周期内的滚动回归残差得到 Alpha143： $\text{Alpha143} = \text{ts_regres}(X_1, \text{open_price}, 16)$。
4.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：18, 16。
* 最终结果会进行无穷大值处理。

【因子信息结束】