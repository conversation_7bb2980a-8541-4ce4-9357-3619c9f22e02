【因子信息开始】===============================================================

【因子编号和名称】

因子148: Alpha 148 (Alpha 148, A148)

【1. 因子名称详情】

因子148: Alpha 148 (Alpha 148, A148)

【2. 核心公式】

$$\text{Alpha148} = \text{gp_max} \left( \text{ts_regbeta} \left( \text{add}(\text{amount}, \text{vwap}), \text{rank}(\text{open_price}), 18 \right), \text{ts_regbeta}(\text{volume}, \text{vwap}, 12) \right)$$

【3. 变量定义】

* `amount`: 成交额。
* `vwap`: 成交量加权平均价。
* `open_price`: 开盘价。
* `volume`: 成交量。

【4. 函数与方法说明】

* `add(A, B)`: 对两个输入数据 A 和 B 进行逐元素加法操作。公式：$A + B$。
* `rank(X)`: 计算 X 在截面上的排名。Pandas `rank(axis=1)` 默认使用升序排名。
* `ts_regbeta(X, Y, n)`: 计算 Y 对 X 在过去 n 个周期内的滚动回归的贝塔系数。公式：$\beta_{Y,X,n} = \frac{\text{Cov}_n(Y, X)}{\text{Var}_n(X)}$。
* `gp_max(A, B)`: 对两个输入数据 A 和 B 逐元素比较，取较大者。公式：$\max(A, B)$。

【5. 计算步骤】

1.  计算成交额 (`amount`) 与 `vwap` 的和： $T_1 = \text{add}(\text{amount}, \text{vwap})$。
2.  计算开盘价 (`open_price`) 的截面排名： $T_2 = \text{rank}(\text{open_price})$。
3.  计算 $T_2$ 对 $T_1$ 在过去18个周期内的滚动回归贝塔系数： $X_1 = \text{ts_regbeta}(T_1, T_2, 18)$。
4.  计算 `vwap` 对成交量 (`volume`) 在过去12个周期内的滚动回归贝塔系数： $X_2 = \text{ts_regbeta}(\text{volume}, \text{vwap}, 12)$。
5.  取 $X_1$ 和 $X_2$ 中逐元素的较大值得到 Alpha148： $\text{Alpha148} = \text{gp_max}(X_1, X_2)$。
6.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：18, 12。
* 最终结果会进行无穷大值处理。

【因子信息结束】