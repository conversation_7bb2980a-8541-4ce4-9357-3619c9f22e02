【因子信息开始】===============================================================

【因子编号和名称】

因子157: Alpha 157 (Alpha 157, A157)

【1. 因子名称详情】

因子157: Alpha 157 (Alpha 157, A157)

【2. 核心公式】

$$\text{Alpha157} = \text{gp_min} \left( \text{ts_pctchg}(\text{vwap}, 4), \text{ts_pctchg}(\text{high}, 8) \right)$$

【3. 变量定义】

* `vwap`: 成交量加权平均价。
* `high`: 最高价。

【4. 函数与方法说明】

* `ts_pctchg(X, n)`: 计算 X 在 n 个周期内的百分比变化率。公式：$(X_t - X_{t-n}) / X_{t-n}$。
* `gp_min(A, B)`: 对两个输入数据 A 和 B 逐元素比较，取较小者。公式：$\min(A, B)$。

【5. 计算步骤】

1.  计算 `vwap` 在过去4个周期内的百分比变化率： $X_1 = \text{ts_pctchg}(\text{vwap}, 4)$。
2.  计算最高价 (`high`) 在过去8个周期内的百分比变化率： $X_2 = \text{ts_pctchg}(\text{high}, 8)$。
3.  取 $X_1$ 和 $X_2$ 中逐元素的较小值得到 Alpha157： $\text{Alpha157} = \text{gp_min}(X_1, X_2)$。
4.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：4, 8。
* 最终结果会进行无穷大值处理。

【因子信息结束】