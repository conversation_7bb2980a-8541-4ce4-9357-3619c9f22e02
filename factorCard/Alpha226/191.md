【因子信息开始】===============================================================
【因子编号和名称】
因子编号: Alpha191
【1. 因子名称详情】
因子191: Alpha191
【2. 核心公式】
$$\text{Alpha191} = \text{PCT\_CHG}(\text{LOW} + \text{HIGH} - (\text{ABS}(\text{HIGH}) - \text{TS\_REGRES}(\text{OPEN}, \text{CLOSE}, 16)), 6)$$
【3. 变量定义】
* $\text{LOW}$: 每日最低价。
* $\text{HIGH}$: 每日最高价。
* $\text{OPEN}$: 每日开盘价。
* $\text{CLOSE}$: 每日收盘价。
* $\text{ABS}(x)$: 绝对值函数。
【4. 函数与方法说明】
* $\text{PCT\_CHG}(\text{df}, n)$: 计算DataFrame `df` 在 $n$ 周期内的百分比变化。
    $$
    \text{PCT\_CHG}(\text{df}, n)_t = \frac{\text{df}_t - \text{df}_{t-n}}{\text{df}_{t-n}}
    $$
* $\text{TS\_REGRES}(x, y, n)$: 计算 $y$ 对 $x$ 在 $n$ 周期内的回归残差。
    $$
    \text{TS\_REGRES}(x, y, n)_t = y_t - \text{TS\_REGBETA}(x, y, n)_t \cdot x_t
    $$
    其中，$\text{TS\_REGBETA}(x, y, n)$ 为 $y$ 对 $x$ 在 $n$ 周期内的回归beta系数。
    $$
    \text{TS\_REGBETA}(x, y, n)_t = \frac{\text{COV}(x, y, n)_t}{\text{VAR}(x, n)_t}
    $$
    $\text{COV}(x, y, n)$: $x$ 和 $y$ 在 $n$ 周期内的协方差。
    $\text{VAR}(x, n)$: $x$ 在 $n$ 周期内的方差。
【5. 计算步骤】
1.  计算 $\text{TS\_REGRES}(\text{OPEN}, \text{CLOSE}, 16)$：
    1.  计算 $\text{OPEN}$ 在16周期内的方差 $\text{VAR}(\text{OPEN}, 16)$。
    2.  计算 $\text{OPEN}$ 和 $\text{CLOSE}$ 在16周期内的协方差 $\text{COV}(\text{OPEN}, \text{CLOSE}, 16)$。
    3.  计算 $\text{TS\_REGBETA}(\text{OPEN}, \text{CLOSE}, 16) = \frac{\text{COV}(\text{OPEN}, \text{CLOSE}, 16)}{\text{VAR}(\text{OPEN}, 16)}$。
    4.  计算 $\text{TS\_REGRES}(\text{OPEN}, \text{CLOSE}, 16) = \text{CLOSE} - \text{TS\_REGBETA}(\text{OPEN}, \text{CLOSE}, 16) \cdot \text{OPEN}$。
2.  计算 $\text{ABS}(\text{HIGH})$。
3.  计算 $\text{ABS}(\text{HIGH}) - \text{TS\_REGRES}(\text{OPEN}, \text{CLOSE}, 16)$。
4.  计算 $\text{LOW} + \text{HIGH}$。
5.  计算中间结果：$(\text{LOW} + \text{HIGH}) - (\text{ABS}(\text{HIGH}) - \text{TS\_REGRES}(\text{OPEN}, \text{CLOSE}, 16))$。
6.  对步骤5的结果计算6周期内的百分比变化 $\text{PCT\_CHG}(\text{结果}, 6)$，得到 $\text{Alpha191}$。
7.  将结果中的无穷大和负无穷大值替换为 $\text{NaN}$。
【6. 备注与参数说明】
该因子结合了价格数据和回归残差的百分比变化，用于捕捉市场异常波动。窗口期参数分别为16和6。

【因子信息结束】