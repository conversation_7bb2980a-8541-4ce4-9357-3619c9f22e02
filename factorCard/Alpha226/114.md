【因子信息开始】===============================================================

【因子编号和名称】

因子114: Alpha 114 (Alpha 114, A114)

【1. 因子名称详情】

因子114: Alpha 114 (Alpha 114, A114)

【2. 核心公式】

$$\text{Alpha114} = \text{ts_regres} \left( \text{gp_max} \left( \text{ts_rank}(\text{close}, 6), \text{log}(\text{vwap}) \right), \text{vwap}, 8 \right)$$

【3. 变量定义】

* `close`: 收盘价。
* `vwap`: 成交量加权平均价。

【4. 函数与方法说明】

* `ts_rank(X, n)`: 计算 X 在过去 n 个周期内的滚动排名，并将结果归一化到 (0, 1] 区间。公式：$\text{Rank}_n^{\text{desc}}(X_t) / n$。计算时 `min_periods=1`。
* `log(X)`: 计算输入数据 X 逐元素的绝对值的自然对数。公式：$\ln(|X|)$。
* `gp_max(A, B)`: 对两个输入数据 A 和 B 逐元素比较，取较大者。公式：$\max(A, B)$。
* `ts_regres(X, Y, n)`: 计算 Y 对 X 在过去 n 个周期内的滚动回归的残差。公式：$\text{Resid}_{Y,X,n} = Y - \beta_{Y,X,n} \times X$。

【5. 计算步骤】

1.  计算收盘价 (`close`) 在过去6个周期内的滚动排名并归一化： $T_1 = \text{ts_rank}(\text{close}, 6)$。
2.  计算 `vwap` 的绝对值的自然对数： $T_2 = \text{log}(\text{vwap})$。
3.  取 $T_1$ 和 $T_2$ 中逐元素的较大值： $X_1 = \text{gp_max}(T_1, T_2)$。
4.  计算 `vwap` 对 $X_1$ 在过去8个周期内的滚动回归残差得到 Alpha114： $\text{Alpha114} = \text{ts_regres}(X_1, \text{vwap}, 8)$。
5.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：6, 8。
* 滚动排名计算设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】