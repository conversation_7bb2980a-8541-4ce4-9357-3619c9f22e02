【因子信息开始】===============================================================
【因子编号和名称】
因子编号: Alpha_164 (Alpha_164)
【1. 因子名称详情】
因子全称: Alpha_164
【2. 核心公式】
$$Alpha_{164} = \text{ts_max}(\text{ts_cov}(10, \text{ts_max}(\text{low}, 7), \text{ts_mean}(\text{amount}, 20)), 16)$$
【3. 变量定义】
\begin{itemize}
    \item $low_t$: t时刻的最低价
    \item $amount_t$: t时刻的成交额
\end{itemize}
【4. 函数与方法说明】
\begin{itemize}
    \item $\text{ts_max}(data, n)$: 计算data在过去n期（包括当期）的滚动最大值。计算时，当期之前的可用数据不足n期时，则使用实际可用期数计算（min\_periods=1）。
    \item $\text{ts_mean}(data, n)$: 计算data在过去n期（包括当期）的滚动平均值。计算时，当期之前的可用数据不足n期时，则使用实际可用期数计算（min\_periods=1）。公式为：$\frac{1}{N} \sum_{i=1}^{N} x_i$，其中N为窗口期n内实际观测值数量。
    \item $\text{ts_cov}(window, x, y)$: 计算x和y在过去window期（包括当期）的滚动协方差。计算时，当期之前的可用数据不足window期时，则使用实际可用期数计算（min\_periods=1）。公式为：$\frac{1}{N-1} \sum_{i=1}^{N} (x_i - \bar{x})(y_i - \bar{y})$，其中N为窗口期window内实际观测值数量。
\end{itemize}
【5. 计算步骤】
\begin{enumerate}
    \item 计算最低价 ($low_t$) 在过去7期的滚动最大值：$I_1 = \text{ts_max}(low_t, 7)$。
    \item 计算成交额 ($amount_t$) 在过去20期的滚动平均值：$I_2 = \text{ts_mean}(amount_t, 20)$。
    \item 计算 $I_1$ 和 $I_2$ 在过去10期的滚动协方差：$I_3 = \text{ts_cov}(10, I_1, I_2)$。
    \item 计算 $I_3$ 在过去16期的滚动最大值，得到因子值：$Alpha_{164} = \text{ts_max}(I_3, 16)$。
    \item 将结果中的正无穷 ($\text{np.inf}$) 和负无穷 ($-\text{np.inf}$) 替换为NaN。
\end{enumerate}
【6. 备注与参数说明】
\begin{itemize}
    \item 窗口期参数：第一个ts\_max为7，ts\_mean为20，ts\_cov为10，第二个ts\_max为16。
    \item 数据预处理：原始数据加载时已去除填充值。因子计算结果中的无穷值被替换为NaN。
\end{itemize}
【因子信息结束】