【因子信息开始】===============================================================

【因子编号和名称】

因子151: Alpha 151 (Alpha 151, A151)

【1. 因子名称详情】

因子151: Alpha 151 (Alpha 151, A151)

【2. 核心公式】

$$\text{Alpha151} = \text{ts_corr} \left( 12, \text{arctan}(\text{high}), \text{gp_max} \left( \text{ts_pctchg}(\text{open_price}, 14), \text{delta}(\text{volume}, 12) \right) \right)$$

【3. 变量定义】

* `high`: 最高价。
* `open_price`: 开盘价。
* `volume`: 成交量。

【4. 函数与方法说明】

* `arctan(X)`: 计算输入数据 X 逐元素的反正切值。公式：$\arctan(X)$。
* `ts_pctchg(X, n)`: 计算 X 在 n 个周期内的百分比变化率。公式：$(X_t - X_{t-n}) / X_{t-n}$。
* `delta(X, n)`: 计算 X 在当前周期的值与 n 个周期前的值的差。公式：$X_t - X_{t-n}$。
* `gp_max(A, B)`: 对两个输入数据 A 和 B 逐元素比较，取较大者。公式：$\max(A, B)$。
* `ts_corr(n, X, Y)`: 计算 X 和 Y 在过去 n 个周期内的滚动相关系数。公式：$\text{Corr}_n(X, Y)_t = \frac{\text{Cov}_n(X, Y)_t}{\text{StdDev}_n(X)_t \times \text{StdDev}_n(Y)_t}$。计算时 `min_periods=1`。

【5. 计算步骤】

1.  计算最高价 (`high`) 的反正切值： $X_1 = \text{arctan}(\text{high})$。
2.  计算开盘价 (`open_price`) 在过去14个周期内的百分比变化率： $T_1 = \text{ts_pctchg}(\text{open_price}, 14)$。
3.  计算成交量 (`volume`) 在过去12个周期内的差值： $T_2 = \text{delta}(\text{volume}, 12)$。
4.  取 $T_1$ 和 $T_2$ 中逐元素的较大值： $X_2 = \text{gp_max}(T_1, T_2)$。
5.  计算 $X_1$ 和 $X_2$ 在过去12个周期内的滚动相关系数得到 Alpha151： $\text{Alpha151} = \text{ts_corr}(12, X_1, X_2)$。
6.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：14, 12, 12。
* 滚动相关系数计算设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】