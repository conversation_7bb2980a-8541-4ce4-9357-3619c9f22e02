【因子信息开始】===============================================================
【因子编号和名称】
因子编号: Alpha_181 (Alpha_181)
【1. 因子名称详情】
因子全称: Alpha_181
【2. 核心公式】
$$Alpha_{181} = \text{div}(\text{gp_min}(\text{vwap}, \text{ts_std}(\text{volume}, 10)), \text{ts_max}(\text{low}, 10))$$
【3. 变量定义】
\begin{itemize}
    \item $vwap_t$: t时刻的成交量加权平均价
    \item $volume_t$: t时刻的成交量
    \item $low_t$: t时刻的最低价
\end{itemize}
【4. 函数与方法说明】
\begin{itemize}
    \item $\text{ts_std}(data, n)$: 计算data在过去n期（包括当期）的滚动标准差。计算时，当期之前的可用数据不足n期时，则使用实际可用期数计算（min\_periods=1）。公式为：$\sqrt{\frac{1}{N-1} \sum_{i=1}^{N} (x_i - \bar{x})^2}$，其中N为窗口期n内实际观测值数量。
    \item $\text{gp_min}(data1, data2)$: 元素级别的较小值，即 $\min(data1_t, data2_t)$。
    \item $\text{ts_max}(data, n)$: 计算data在过去n期（包括当期）的滚动最大值。计算时，当期之前的可用数据不足n期时，则使用实际可用期数计算（min\_periods=1）。
    \item $\text{div}(x, y)$: 元素级别的除法，即 $x_t / y_t$。
\end{itemize}
【5. 计算步骤】
\begin{enumerate}
    \item 计算成交量 ($volume_t$) 在过去10期的滚动标准差：$I_1 = \text{ts_std}(volume_t, 10)$。
    \item 取成交量加权平均价 ($vwap_t$) 和 $I_1$ 中元素级别的较小值：$I_2 = \text{gp_min}(vwap_t, I_1)$。
    \item 计算最低价 ($low_t$) 在过去10期的滚动最大值：$I_3 = \text{ts_max}(low_t, 10)$。
    \item 计算 $I_2$ 除以 $I_3$ 的结果，得到因子值：$Alpha_{181} = \text{div}(I_2, I_3)$。
    \item 将结果中的正无穷 ($\text{np.inf}$) 和负无穷 ($-\text{np.inf}$) 替换为NaN。
\end{enumerate}
【6. 备注与参数说明】
\begin{itemize}
    \item 窗口期参数：ts\_std为10，ts\_max为10。
    \item 数据预处理：原始数据加载时已去除填充值。因子计算结果中的无穷值被替换为NaN。
\end{itemize}
【因子信息结束】