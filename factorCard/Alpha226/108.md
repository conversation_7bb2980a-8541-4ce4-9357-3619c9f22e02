【因子信息开始】===============================================================

【因子编号和名称】

因子108: Alpha 108 (Alpha 108, A108)

【1. 因子名称详情】

因子108: Alpha 108 (Alpha 108, A108)

【2. 核心公式】

$$\text{Alpha108} = \text{gp_min} \left( \text{log}(\text{delay}(\text{amount}, 3)), \text{ts_regres} \left( \text{rank}(\text{amount}), \text{add}(\text{high}, \text{close}), 10 \right) \right)$$

【3. 变量定义】

* `amount`: 成交额。
* `high`: 最高价。
* `close`: 收盘价。

【4. 函数与方法说明】

* `delay(X, n)`: 获取 X 在 n 个周期前的值。公式：$X_{t-n}$。
* `log(X)`: 计算输入数据 X 逐元素的绝对值的自然对数。公式：$\ln(|X|)$。
* `rank(X)`: 计算 X 在截面上的排名。Pandas `rank(axis=1)` 默认使用升序排名。
* `add(A, B)`: 对两个输入数据 A 和 B 进行逐元素加法操作。公式：$A + B$。
* `ts_regres(X, Y, n)`: 计算 Y 对 X 在过去 n 个周期内的滚动回归的残差。公式：$\text{Resid}_{Y,X,n} = Y - \beta_{Y,X,n} \times X$。
* `gp_min(A, B)`: 对两个输入数据 A 和 B 逐元素比较，取较小者。公式：$\min(A, B)$。

【5. 计算步骤】

1.  获取成交额 (`amount`) 在3个周期前的值： $T_1 = \text{delay}(\text{amount}, 3)$。
2.  计算 $T_1$ 的绝对值的自然对数： $X_1 = \text{log}(T_1)$。
3.  计算成交额 (`amount`) 的截面排名： $T_2 = \text{rank}(\text{amount})$。
4.  计算最高价 (`high`) 与收盘价 (`close`) 的和： $T_3 = \text{add}(\text{high}, \text{close})$。
5.  计算 $T_3$ 对 $T_2$ 在过去10个周期内的滚动回归残差： $X_2 = \text{ts_regres}(T_2, T_3, 10)$。
6.  取 $X_1$ 和 $X_2$ 中逐元素的较小值得到 Alpha108： $\text{Alpha108} = \text{gp_min}(X_1, X_2)$。
7.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：3, 10。
* 最终结果会进行无穷大值处理。

【因子信息结束】