【因子信息开始】===============================================================
【因子编号和名称】
因子编号: Alpha194
【1. 因子名称详情】
因子194: Alpha194
【2. 核心公式】
$$\text{Alpha194} = -\text{TS\_REGRES}(\text{AMOUNT}, \text{LOW}, 10)$$
【3. 变量定义】
* $\text{AMOUNT}$: 每日成交金额。
* $\text{LOW}$: 每日最低价。
【4. 函数与方法说明】
* $\text{NEG}(x)$: 取 $x$ 的负值。
* $\text{TS\_REGRES}(x, y, n)$: 计算 $y$ 对 $x$ 在 $n$ 周期内的回归残差。
    $$
    \text{TS\_REGRES}(x, y, n)_t = y_t - \text{TS\_REGBETA}(x, y, n)_t \cdot x_t
    $$
    其中，$\text{TS\_REGBETA}(x, y, n)$ 为 $y$ 对 $x$ 在 $n$ 周期内的回归beta系数。
    $$
    \text{TS\_REGBETA}(x, y, n)_t = \frac{\text{COV}(x, y, n)_t}{\text{VAR}(x, n)_t}
    $$
    $\text{COV}(x, y, n)$: $x$ 和 $y$ 在 $n$ 周期内的协方差。
    $\text{VAR}(x, n)$: $x$ 在 $n$ 周期内的方差。
【5. 计算步骤】
1.  计算 $\text{TS\_REGRES}(\text{AMOUNT}, \text{LOW}, 10)$：
    1.  计算 $\text{AMOUNT}$ 在10周期内的方差 $\text{VAR}(\text{AMOUNT}, 10)$。
    2.  计算 $\text{AMOUNT}$ 和 $\text{LOW}$ 在10周期内的协方差 $\text{COV}(\text{AMOUNT}, \text{LOW}, 10)$。
    3.  计算 $\text{TS\_REGBETA}(\text{AMOUNT}, \text{LOW}, 10) = \frac{\text{COV}(\text{AMOUNT}, \text{LOW}, 10)}{\text{VAR}(\text{AMOUNT}, 10)}$。
    4.  计算 $\text{TS\_REGRES}(\text{AMOUNT}, \text{LOW}, 10) = \text{LOW} - \text{TS\_REGBETA}(\text{AMOUNT}, \text{LOW}, 10) \cdot \text{AMOUNT}$。
2.  对步骤1的结果取负值，得到 $\text{Alpha194}$。
3.  将结果中的无穷大和负无穷大值替换为 $\text{NaN}$。
【6. 备注与参数说明】
该因子衡量了成交金额对最低价的回归残差的负值。窗口期参数为10。

【因子信息结束】