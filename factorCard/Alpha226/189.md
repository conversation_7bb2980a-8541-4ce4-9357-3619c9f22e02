【因子信息开始】===============================================================
【因子编号和名称】
因子编号: Alpha_189 (Alpha_189)
【1. 因子名称详情】
因子全称: Alpha_189
【2. 核心公式】
$$Alpha_{189} = \text{ts_max}(\text{ts_regbeta}(\text{vwap}, \text{amount}, 6), 12)$$
【3. 变量定义】
\begin{itemize}
    \item $vwap_t$: t时刻的成交量加权平均价
    \item $amount_t$: t时刻的成交额
\end{itemize}
【4. 函数与方法说明】
\begin{itemize}
    \item $\text{ts_regbeta}(x, y, window)$: 计算y对x在过去window期的滚动回归beta值。公式为：$\text{ts_regbeta}(x, y, window) = \frac{\text{Cov}(x, y, \text{window})}{\text{Var}(x, \text{window})}$。其中Cov和Var均为滚动计算。
    \item $\text{ts_max}(data, n)$: 计算data在过去n期（包括当期）的滚动最大值。计算时，当期之前的可用数据不足n期时，则使用实际可用期数计算（min\_periods=1）。
\end{itemize}
【5. 计算步骤】
\begin{enumerate}
    \item 计算成交额 ($amount_t$) 对成交量加权平均价 ($vwap_t$) 在过去6期的滚动回归beta值：$I_1 = \text{ts_regbeta}(vwap_t, amount_t, 6)$。
    \item 计算 $I_1$ 在过去12期的滚动最大值，得到因子值：$Alpha_{189} = \text{ts_max}(I_1, 12)$。
    \item 将结果中的正无穷 ($\text{np.inf}$) 和负无穷 ($-\text{np.inf}$) 替换为NaN。
\end{enumerate}
【6. 备注与参数说明】
\begin{itemize}
    \item 窗口期参数：ts\_regbeta为6，ts\_max为12。
    \item 数据预处理：原始数据加载时已去除填充值。因子计算结果中的无穷值被替换为NaN。
\end{itemize}
【因子信息结束】