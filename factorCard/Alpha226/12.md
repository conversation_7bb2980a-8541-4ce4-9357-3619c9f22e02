【因子信息开始】===============================================================

【因子编号和名称】

因子12: Alpha 12 (Alpha 12, A12)

【1. 因子名称详情】

因子12: Alpha 12 (Alpha 12, A12)

【2. 核心公式】

$$\text{Alpha12} = \text{gp_min}(\text{delta}(\text{low}, 3), \text{delta}(\text{close}, 6))$$

【3. 变量定义】

* `low`: 最低价。
* `close`: 收盘价。

【4. 函数与方法说明】

* `delta(X, n)`: 计算 X 在当前周期的值与 n 个周期前的值的差。公式：$X_t - X_{t-n}$。
* `gp_min(A, B)`: 对两个输入数据 A 和 B 逐元素比较，取较小者。公式：$\min(A, B)$。

【5. 计算步骤】

1.  计算最低价 (`low`) 在过去3个周期内的差值： $X_1 = \text{delta}(\text{low}, 3)$。
2.  计算收盘价 (`close`) 在过去6个周期内的差值： $X_2 = \text{delta}(\text{close}, 6)$。
3.  取 $X_1$ 和 $X_2$ 中逐元素的较小值得到 Alpha12： $\text{Alpha12} = \text{gp_min}(X_1, X_2)$。
4.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：3, 6。
* 最终结果会进行无穷大值处理。

【因子信息结束】