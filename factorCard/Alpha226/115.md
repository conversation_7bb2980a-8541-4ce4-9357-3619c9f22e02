【因子信息开始】===============================================================

【因子编号和名称】

因子115: Alpha 115 (Alpha 115, A115)

【1. 因子名称详情】

因子115: Alpha 115 (Alpha 115, A115)

【2. 核心公式】

$$\text{Alpha115} = \text{ts_corr} \left( 9, \text{add}(\text{volume}, \text{close}), \text{sqrt} \left( \text{ts_pctchg}(\text{close}, 9) \right) \right)$$

【3. 变量定义】

* `volume`: 成交量。
* `close`: 收盘价。

【4. 函数与方法说明】

* `add(A, B)`: 对两个输入数据 A 和 B 进行逐元素加法操作。公式：$A + B$。
* `ts_pctchg(X, n)`: 计算 X 在 n 个周期内的百分比变化率。公式：$(X_t - X_{t-n}) / X_{t-n}$。
* `sqrt(X)`: 计算输入数据 X 逐元素的绝对值的平方根。公式：$\sqrt{|X|}$。
* `ts_corr(n, X, Y)`: 计算 X 和 Y 在过去 n 个周期内的滚动相关系数。公式：$\text{Corr}_n(X, Y)_t = \frac{\text{Cov}_n(X, Y)_t}{\text{StdDev}_n(X)_t \times \text{StdDev}_n(Y)_t}$。计算时 `min_periods=1`。

【5. 计算步骤】

1.  计算成交量 (`volume`) 与收盘价 (`close`) 的和： $X_1 = \text{add}(\text{volume}, \text{close})$。
2.  计算收盘价 (`close`) 在过去9个周期内的百分比变化率： $T_1 = \text{ts_pctchg}(\text{close}, 9)$。
3.  计算 $T_1$ 的绝对值的平方根： $X_2 = \text{sqrt}(T_1)$。
4.  计算 $X_1$ 和 $X_2$ 在过去9个周期内的滚动相关系数得到 Alpha115： $\text{Alpha115} = \text{ts_corr}(9, X_1, X_2)$。
5.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数为：9。
* 滚动相关系数计算设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】