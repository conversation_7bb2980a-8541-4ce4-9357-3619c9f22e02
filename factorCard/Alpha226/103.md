【因子信息开始】===============================================================

【因子编号和名称】

因子103: Alpha 103 (Alpha 103, A103)

【1. 因子名称详情】

因子103: Alpha 103 (Alpha 103, A103)

【2. 核心公式】

$$\text{Alpha103} = \text{gp_min} \left( \text{ts_corr}(16, \text{vwap}, \text{volume}), \text{ts_cov} \left( 18, \text{gp_min}(\text{vwap}, \text{amount}), \text{ts_std}(\text{low}, 10) \right) \right)$$

【3. 变量定义】

* `vwap`: 成交量加权平均价。
* `volume`: 成交量。
* `amount`: 成交额。
* `low`: 最低价。

【4. 函数与方法说明】

* `ts_corr(n, X, Y)`: 计算 X 和 Y 在过去 n 个周期内的滚动相关系数。公式：$\text{Corr}_n(X, Y)_t = \frac{\text{Cov}_n(X, Y)_t}{\text{StdDev}_n(X)_t \times \text{StdDev}_n(Y)_t}$。计算时 `min_periods=1`。
* `gp_min(A, B)`: 对两个输入数据 A 和 B 逐元素比较，取较小者。公式：$\min(A, B)$。
* `ts_std(X, n)`: 计算 X 在过去 n 个周期内的滚动标准差。公式：$\text{StdDev}_n(X)_t = \sqrt{\frac{1}{n-1} \sum_{i=0}^{n-1} (X_{t-i} - \bar{X}_n)^2}$。计算时 `min_periods=1`。
* `ts_cov(n, X, Y)`: 计算 X 和 Y 在过去 n 个周期内的滚动协方差。公式：$\text{Cov}_n(X, Y)_t = \frac{1}{n-1} \sum_{i=0}^{n-1} (X_{t-i} - \bar{X}_n)(Y_{t-i} - \bar{Y}_n)$。计算时 `min_periods=1`。

【5. 计算步骤】

1.  计算 `vwap` 和成交量 (`volume`) 在过去16个周期内的滚动相关系数： $X_1 = \text{ts_corr}(16, \text{vwap}, \text{volume})$。
2.  取 `vwap` 和成交额 (`amount`) 中逐元素的较小值： $T_1 = \text{gp_min}(\text{vwap}, \text{amount})$。
3.  计算最低价 (`low`) 在过去10个周期内的滚动标准差： $T_2 = \text{ts_std}(\text{low}, 10)$。
4.  计算 $T_1$ 和 $T_2$ 在过去18个周期内的滚动协方差： $X_2 = \text{ts_cov}(18, T_1, T_2)$。
5.  取 $X_1$ 和 $X_2$ 中逐元素的较小值得到 Alpha103： $\text{Alpha103} = \text{gp_min}(X_1, X_2)$。
6.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：16, 10, 18。
* 所有滚动计算均设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】