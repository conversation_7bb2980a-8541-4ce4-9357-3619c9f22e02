【因子信息开始】===============================================================

【因子编号和名称】

因子18: Alpha 18 (Alpha 18, A18)

【1. 因子名称详情】

因子18: Alpha 18 (Alpha 18, A18)

【2. 核心公式】

$$\text{Alpha18} = \text{ts_regres}(\text{delay}(\text{open_price}, 8), \text{close}, 7)$$

【3. 变量定义】

* `open_price`: 开盘价。
* `close`: 收盘价。

【4. 函数与方法说明】

* `delay(X, n)`: 获取 X 在 n 个周期前的值。公式：$X_{t-n}$。
* `ts_regres(X, Y, n)`: 计算 Y 对 X 在过去 n 个周期内的滚动回归的残差。公式：$\text{Resid}_{Y,X,n} = Y - \beta_{Y,X,n} \times X$，其中 $\beta_{Y,X,n} = \frac{\text{Cov}_n(Y, X)}{\text{Var}_n(X)}$。

【5. 计算步骤】

1.  获取开盘价 (`open_price`) 在8个周期前的值： $X_1 = \text{delay}(\text{open_price}, 8)$。
2.  计算收盘价 (`close`) 对 $X_1$ 在过去7个周期内的滚动回归残差得到 Alpha18： $\text{Alpha18} = \text{ts_regres}(X_1, \text{close}, 7)$。
3.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：8, 7。
* 最终结果会进行无穷大值处理。

【因子信息结束】