【因子信息开始】===============================================================

【因子编号和名称】

因子111: Alpha 111 (Alpha 111, A111)

【1. 因子名称详情】

因子111: Alpha 111 (Alpha 111, A111)

【2. 核心公式】

$$\text{Alpha111} = \text{mul} \left( \text{ts_max} \left( \text{ts_corr}(12, \text{volume}, \text{open_price}), 18 \right), \text{delay} \left( \text{gp_max} \left( \text{sqrt}(\text{amount}), \text{ts_corr}(12, \text{volume}, \text{close}) \right), 10 \right) \right)$$

【3. 变量定义】

* `volume`: 成交量。
* `open_price`: 开盘价。
* `amount`: 成交额。
* `close`: 收盘价。

【4. 函数与方法说明】

* `ts_corr(n, X, Y)`: 计算 X 和 Y 在过去 n 个周期内的滚动相关系数。公式：$\text{Corr}_n(X, Y)_t = \frac{\text{Cov}_n(X, Y)_t}{\text{StdDev}_n(X)_t \times \text{StdDev}_n(Y)_t}$。计算时 `min_periods=1`。
* `ts_max(X, n)`: 计算 X 在过去 n 个周期内的滚动最大值。公式：$\text{Max}_n(X)_t = \max(X_{t-n+1}, ..., X_t)$。计算时 `min_periods=1`。
* `sqrt(X)`: 计算输入数据 X 逐元素的绝对值的平方根。公式：$\sqrt{|X|}$。
* `gp_max(A, B)`: 对两个输入数据 A 和 B 逐元素比较，取较大者。公式：$\max(A, B)$。
* `delay(X, n)`: 获取 X 在 n 个周期前的值。公式：$X_{t-n}$。
* `mul(A, B)`: 对两个输入数据 A 和 B 进行逐元素乘法操作。公式：$A \times B$。

【5. 计算步骤】

1.  计算成交量 (`volume`) 和开盘价 (`open_price`) 在过去12个周期内的滚动相关系数： $C_1 = \text{ts_corr}(12, \text{volume}, \text{open_price})$。
2.  计算 $C_1$ 在过去18个周期内的滚动最大值： $X_1 = \text{ts_max}(C_1, 18)$。
3.  计算成交额 (`amount`) 的绝对值的平方根： $T_1 = \text{sqrt}(\text{amount})$。
4.  计算成交量 (`volume`) 和收盘价 (`close`) 在过去12个周期内的滚动相关系数： $C_2 = \text{ts_corr}(12, \text{volume}, \text{close})$。
5.  取 $T_1$ 和 $C_2$ 中逐元素的较大值： $T_2 = \text{gp_max}(T_1, C_2)$。
6.  获取 $T_2$ 在10个周期前的值： $X_2 = \text{delay}(T_2, 10)$。
7.  计算 $X_1$ 与 $X_2$ 的乘积得到 Alpha111： $\text{Alpha111} = \text{mul}(X_1, X_2)$。
8.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：12, 18, 12, 10。
* 所有滚动计算均设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】