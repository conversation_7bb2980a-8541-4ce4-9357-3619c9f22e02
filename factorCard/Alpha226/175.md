【因子信息开始】===============================================================
【因子编号和名称】
因子编号: Alpha_175 (Alpha_175)
【1. 因子名称详情】
因子全称: Alpha_175
【2. 核心公式】
$$Alpha_{175} = \text{ts_std}(\text{gp_max}(\text{ts_max}(\text{gp_max}(\text{delta}(\text{vwap}, 20), \text{rank}(\text{amount})), 5), \text{ts_cov}(5, \text{vwap}, \text{amount})), 10)$$
【3. 变量定义】
\begin{itemize}
    \item $vwap_t$: t时刻的成交量加权平均价
    \item $amount_t$: t时刻的成交额
\end{itemize}
【4. 函数与方法说明】
\begin{itemize}
    \item $\text{delta}(data, window)$: 计算当前值与window期前值的差，即 $data_t - data_{t-window}$。
    \item $\text{delay}(data, window)$: 获取data在window期前的值，即 $data_{t-window}$。
    \item $\text{rank}(data)$: 计算data在截面上的排名（axis=1）。即对每一天，所有股票的因子值进行排序并赋予排名。
    \item $\text{gp_max}(data1, data2)$: 元素级别的较大值，即 $\max(data1_t, data2_t)$。
    \item $\text{ts_max}(data, n)$: 计算data在过去n期（包括当期）的滚动最大值。计算时，当期之前的可用数据不足n期时，则使用实际可用期数计算（min\_periods=1）。
    \item $\text{ts_cov}(window, x, y)$: 计算x和y在过去window期（包括当期）的滚动协方差。计算时，当期之前的可用数据不足window期时，则使用实际可用期数计算（min\_periods=1）。公式为：$\frac{1}{N-1} \sum_{i=1}^{N} (x_i - \bar{x})(y_i - \bar{y})$，其中N为窗口期window内实际观测值数量。
    \item $\text{ts_std}(data, n)$: 计算data在过去n期（包括当期）的滚动标准差。计算时，当期之前的可用数据不足n期时，则使用实际可用期数计算（min\_periods=1）。公式为：$\sqrt{\frac{1}{N-1} \sum_{i=1}^{N} (x_i - \bar{x})^2}$，其中N为窗口期n内实际观测值数量。
\end{itemize}
【5. 计算步骤】
\begin{enumerate}
    \item 计算成交量加权平均价 ($vwap_t$) 的20期差值：$I_1 = \text{delta}(vwap_t, 20)$。
    \item 对成交额 ($amount_t$) 进行截面排名：$I_2 = \text{rank}(amount_t)$。
    \item 取 $I_1$ 和 $I_2$ 中元素级别的较大值：$I_3 = \text{gp_max}(I_1, I_2)$。
    \item 计算 $I_3$ 在过去5期的滚动最大值：$I_4 = \text{ts_max}(I_3, 5)$。
    \item 计算成交量加权平均价 ($vwap_t$) 和成交额 ($amount_t$) 在过去5期的滚动协方差：$I_5 = \text{ts_cov}(5, vwap_t, amount_t)$。
    \item 取 $I_4$ 和 $I_5$ 中元素级别的较大值：$I_6 = \text{gp_max}(I_4, I_5)$。
    \item 计算 $I_6$ 在过去10期的滚动标准差，得到因子值：$Alpha_{175} = \text{ts_std}(I_6, 10)$。
    \item 将结果中的正无穷 ($\text{np.inf}$) 和负无穷 ($-\text{np.inf}$) 替换为NaN。
\end{enumerate}
【6. 备注与参数说明】
\begin{itemize}
    \item 窗口期参数：delta为20，ts\_max为5，ts\_cov为5，ts\_std为10。
    \item 数据预处理：原始数据加载时已去除填充值。因子计算结果中的无穷值被替换为NaN。
\end{itemize}
【因子信息结束】