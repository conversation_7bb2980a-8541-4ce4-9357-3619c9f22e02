【因子信息开始】===============================================================
【因子编号和名称】
因子编号: Alpha_180 (Alpha_180)
【1. 因子名称详情】
因子全称: Alpha_180
【2. 核心公式】
$$Alpha_{180} = \text{add}(\text{mul}(\text{neg}(\text{low}), \text{gp_min}(\text{ts_max}(\text{open_price}, 11), \text{delta}(\text{close}, 5))), \text{div}(\text{ts_rank}(\text{vwap}, 9), \text{gp_min}(\text{low}, \text{low})))$$
【3. 变量定义】
\begin{itemize}
    \item $low_t$: t时刻的最低价
    \item $open\_price_t$: t时刻的开盘价
    \item $close_t$: t时刻的收盘价
    \item $vwap_t$: t时刻的成交量加权平均价
\end{itemize}
【4. 函数与方法说明】
\begin{itemize}
    \item $\text{neg}(data)$: 对data取负，即 $-data_t$。
    \item $\text{ts_max}(data, n)$: 计算data在过去n期（包括当期）的滚动最大值。计算时，当期之前的可用数据不足n期时，则使用实际可用期数计算（min\_periods=1）。
    \item $\text{delta}(data, window)$: 计算当前值与window期前值的差，即 $data_t - data_{t-window}$。
    \item $\text{delay}(data, window)$: 获取data在window期前的值，即 $data_{t-window}$。
    \item $\text{gp_min}(data1, data2)$: 元素级别的较小值，即 $\min(data1_t, data2_t)$。
    \item $\text{mul}(x, y)$: 元素级别的乘法，即 $x_t \times y_t$。
    \item $\text{arrayRank}(array)$: 辅助函数，计算输入一维数组（Series的一个窗口）中最后一个元素的排名。排名方法为：对于升序排列的数组，元素的排名是比它大的元素个数加1。具体实现为 `array.size + 1 - bk.rankdata(array)[-1]`。最终结果除以窗口大小 `n` 进行归一化。
    \item $\text{ts_rank}(data, n)$: 计算data在过去n期（包括当期）的滚动排名。对每个滚动窗口，应用 `arrayRank` 函数计算当前值的排名，并除以n进行归一化。计算时，当期之前的可用数据不足n期时，则使用实际可用期数计算（min\_periods=1）。
    \item $\text{div}(x, y)$: 元素级别的除法，即 $x_t / y_t$。
    \item $\text{add}(x, y)$: 元素级别的加法，即 $x_t + y_t$。
\end{itemize}
【5. 计算步骤】
\begin{enumerate}
    \item 对最低价 ($low_t$) 取负：$I_1 = \text{neg}(low_t)$。
    \item 计算开盘价 ($open\_price_t$) 在过去11期的滚动最大值：$I_2 = \text{ts_max}(open\_price_t, 11)$。
    \item 计算收盘价 ($close_t$) 的5期差值：$I_3 = \text{delta}(close_t, 5)$。
    \item 取 $I_2$ 和 $I_3$ 中元素级别的较小值：$I_4 = \text{gp_min}(I_2, I_3)$。
    \item 计算 $I_1$ 与 $I_4$ 的乘积：$I_5 = \text{mul}(I_1, I_4)$。
    \item 计算成交量加权平均价 ($vwap_t$) 在过去9期的滚动排名：$I_6 = \text{ts_rank}(vwap_t, 9)$。
    \item 取最低价 ($low_t$) 和最低价 ($low_t$) 中元素级别的较小值 (即 $low_t$ 本身)：$I_7 = \text{gp_min}(low_t, low_t)$。
    \item 计算 $I_6$ 除以 $I_7$ 的结果：$I_8 = \text{div}(I_6, I_7)$。
    \item 计算 $I_5$ 与 $I_8$ 的和，得到因子值：$Alpha_{180} = \text{add}(I_5, I_8)$。
    \item 将结果中的正无穷 ($\text{np.inf}$) 和负无穷 ($-\text{np.inf}$) 替换为NaN。
\end{enumerate}
【6. 备注与参数说明】
\begin{itemize}
    \item 窗口期参数：ts\_max为11，delta为5，ts\_rank为9。
    \item 数据预处理：原始数据加载时已去除填充值。因子计算结果中的无穷值被替换为NaN。
    \item ts\_rank的实现依赖于 `arrayRank`。
    \item $gp\_min(low, low)$ 等价于 $low$。
\end{itemize}
【因子信息结束】