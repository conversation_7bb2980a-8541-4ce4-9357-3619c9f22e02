【因子信息开始】===============================================================

【因子编号和名称】

因子136: Alpha 136 (Alpha 136, A136)

【1. 因子名称详情】

因子136: Alpha 136 (Alpha 136, A136)

【2. 核心公式】

$$\text{Alpha136} = \text{ts_mean} \left( \text{sub}(\text{vwap}, \text{close}), 5 \right)$$

【3. 变量定义】

* `vwap`: 成交量加权平均价。
* `close`: 收盘价。

【4. 函数与方法说明】

* `sub(A, B)`: 对输入数据 A 减去 B 进行逐元素减法操作。公式：$A - B$。
* `ts_mean(X, n)`: 计算 X 在过去 n 个周期内的滚动均值。公式：$\text{Mean}_n(X)_t = \frac{1}{n} \sum_{i=0}^{n-1} X_{t-i}$。计算时 `min_periods=1`。

【5. 计算步骤】

1.  计算 `vwap` 与收盘价 (`close`) 的差值： $X_1 = \text{sub}(\text{vwap}, \text{close})$。
2.  计算 $X_1$ 在过去5个周期内的滚动均值得到 Alpha136： $\text{Alpha136} = \text{ts_mean}(X_1, 5)$。
3.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数为：5。
* 滚动均值计算设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】