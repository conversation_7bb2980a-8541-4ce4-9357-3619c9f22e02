【因子信息开始】===============================================================
【因子编号和名称】
因子编号: Alpha_187 (Alpha_187)
【1. 因子名称详情】
因子全称: Alpha_187
【2. 核心公式】
$$Alpha_{187} = \text{ts_regbeta}(\text{log}(\text{mul}(\text{add}(\text{volume}, \text{high}), \text{arctan}(\text{amount}))), \text{sub}(\text{ts_std}(\text{div}(\text{low}, \text{open_price}), 10), \text{low}), 5)$$
【3. 变量定义】
\begin{itemize}
    \item $volume_t$: t时刻的成交量
    \item $high_t$: t时刻的最高价
    \item $amount_t$: t时刻的成交额
    \item $low_t$: t时刻的最低价
    \item $open\_price_t$: t时刻的开盘价
\end{itemize}
【4. 函数与方法说明】
\begin{itemize}
    \item $\text{add}(x, y)$: 元素级别的加法，即 $x_t + y_t$。
    \item $\text{arctan}(data)$: 计算data的反正切值。
    \item $\text{mul}(x, y)$: 元素级别的乘法，即 $x_t \times y_t$。
    \item $\text{log}(data)$: 计算data的自然对数，对于非正数则取绝对值后再取对数，即 $\ln(|data_t|)$。
    \item $\text{div}(x, y)$: 元素级别的除法，即 $x_t / y_t$。
    \item $\text{ts_std}(data, n)$: 计算data在过去n期（包括当期）的滚动标准差。计算时，当期之前的可用数据不足n期时，则使用实际可用期数计算（min\_periods=1）。公式为：$\sqrt{\frac{1}{N-1} \sum_{i=1}^{N} (x_i - \bar{x})^2}$，其中N为窗口期n内实际观测值数量。
    \item $\text{sub}(x, y)$: 元素级别的减法，即 $x_t - y_t$。
    \item $\text{ts_regbeta}(x, y, window)$: 计算y对x在过去window期的滚动回归beta值。公式为：$\text{ts_regbeta}(x, y, window) = \frac{\text{Cov}(x, y, \text{window})}{\text{Var}(x, \text{window})}$。其中Cov和Var均为滚动计算。
\end{itemize}
【5. 计算步骤】
\begin{enumerate}
    \item 计算成交量 ($volume_t$) 与最高价 ($high_t$) 的和：$I_1 = \text{add}(volume_t, high_t)$。
    \item 对成交额 ($amount_t$) 应用反正切函数：$I_2 = \text{arctan}(amount_t)$。
    \item 计算 $I_1$ 与 $I_2$ 的乘积：$I_3 = \text{mul}(I_1, I_2)$。
    \item 对 $I_3$ 取自然对数：$X = \text{log}(I_3)$ (作为回归自变量)。
    \item 计算最低价 ($low_t$) 除以开盘价 ($open\_price_t$)：$I_4 = \text{div}(low_t, open\_price_t)$。
    \item 计算 $I_4$ 在过去10期的滚动标准差：$I_5 = \text{ts_std}(I_4, 10)$。
    \item 计算 $I_5$ 与最低价 ($low_t$) 的差值：$Y = \text{sub}(I_5, low_t)$ (作为回归因变量)。
    \item 计算 $Y$ 对 $X$ 在过去5期的滚动回归beta值，得到因子值：$Alpha_{187} = \text{ts_regbeta}(X, Y, 5)$。
    \item 将结果中的正无穷 ($\text{np.inf}$) 和负无穷 ($-\text{np.inf}$) 替换为NaN。
\end{enumerate}
【6. 备注与参数说明】
\begin{itemize}
    \item 窗口期参数：ts\_std为10，ts\_regbeta为5。
    \item 数据预处理：原始数据加载时已去除填充值。因子计算结果中的无穷值被替换为NaN。
\end{itemize}
【因子信息结束】