【因子信息开始】===============================================================
【因子编号和名称】
因子编号: Alpha_177 (Alpha_177)
【1. 因子名称详情】
因子全称: Alpha_177
【2. 核心公式】
$$Alpha_{177} = \text{ts_regres}(\text{delay}(\text{mul}(\text{volume}, \text{volume}), 9), \text{close}, 6)$$
【3. 变量定义】
\begin{itemize}
    \item $volume_t$: t时刻的成交量
    \item $close_t$: t时刻的收盘价
\end{itemize}
【4. 函数与方法说明】
\begin{itemize}
    \item $\text{mul}(x, y)$: 元素级别的乘法，即 $x_t \times y_t$。
    \item $\text{delay}(data, window)$: 获取data在window期前的值，即 $data_{t-window}$。
    \item $\text{ts_regbeta}(x, y, window)$: 计算y对x在过去window期的滚动回归beta值。公式为：$\text{ts_regbeta}(x, y, window) = \frac{\text{Cov}(x, y, \text{window})}{\text{Var}(x, \text{window})}$。其中Cov和Var均为滚动计算。
    \item $\text{ts_regres}(x, y, window)$: 计算y对x在过去window期的滚动回归残差。公式为：$y_t - \text{ts_regbeta}(x, y, window) \times x_t$。
\end{itemize}
【5. 计算步骤】
\begin{enumerate}
    \item 计算成交量 ($volume_t$) 的平方：$I_1 = \text{mul}(volume_t, volume_t)$。
    \item 将 $I_1$ 延迟9期：$I_2 = \text{delay}(I_1, 9)$。
    \item 计算收盘价 ($close_t$) 对 $I_2$ 在过去6期的滚动回归残差，得到因子值：$Alpha_{177} = \text{ts_regres}(I_2, close_t, 6)$。
    \item 将结果中的正无穷 ($\text{np.inf}$) 和负无穷 ($-\text{np.inf}$) 替换为NaN。
\end{enumerate}
【6. 备注与参数说明】
\begin{itemize}
    \item 窗口期参数：delay为9，ts\_regres为6。
    \item 数据预处理：原始数据加载时已去除填充值。因子计算结果中的无穷值被替换为NaN。
\end{itemize}
【因子信息结束】