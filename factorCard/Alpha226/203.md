【因子信息开始】===============================================================
【因子编号和名称】
因子编号: Alpha203
【1. 因子名称详情】
因子203: Alpha203
【2. 核心公式】
$$\text{Alpha203} = (\text{GP\_MIN}(\text{CLOSE} - \text{VOLUME}, \text{GP\_MAX}(\text{VOLUME}, \text{VWAP}))) \cdot \text{DELTA}(\text{HIGH}, 8)$$
【3. 变量定义】
* $\text{CLOSE}$: 每日收盘价。
* $\text{VOLUME}$: 每日成交量。
* $\text{VWAP}$: 每日成交量加权平均价。
* $\text{HIGH}$: 每日最高价。
【4. 函数与方法说明】
* $\text{GP\_MIN}(x, y)$: 逐元素比较 $x$ 和 $y$，返回较小值。
    $$
    \text{GP\_MIN}(x, y) = \min(x, y)
    $$
* $\text{GP\_MAX}(x, y)$: 逐元素比较 $x$ 和 $y$，返回较大值。
    $$
    \text{GP\_MAX}(x, y) = \max(x, y)
    $$
* $\text{DELTA}(\text{data}, n)$: 计算 $\text{data}$ 与其 $n$ 期前数据之差。
    $$
    \text{DELTA}(\text{data}, n)_t = \text{data}_t - \text{data}_{t-n}
    $$
【5. 计算步骤】
1.  计算 $\text{CLOSE} - \text{VOLUME}$。
2.  计算 $\text{GP\_MAX}(\text{VOLUME}, \text{VWAP})$：逐元素比较 $\text{VOLUME}$ 和 $\text{VWAP}$，返回较大值。
3.  计算 $\text{GP\_MIN}(\text{CLOSE} - \text{VOLUME}, \text{GP\_MAX}(\text{VOLUME}, \text{VWAP}))$：逐元素比较步骤1结果和步骤2结果，返回较小值。
4.  计算 $\text{DELTA}(\text{HIGH}, 8)$：计算 $\text{HIGH}$ 与其8期前数据之差。
5.  将步骤3的结果与步骤4的结果相乘，得到 $\text{Alpha203}$。
6.  将结果中的无穷大和负无穷大值替换为 $\text{NaN}$。
【6. 备注与参数说明】
该因子结合了收盘价与成交量差值、成交量与VWAP最大值的最小值，再乘以最高价的变化量。窗口期参数为8。

【因子信息结束】