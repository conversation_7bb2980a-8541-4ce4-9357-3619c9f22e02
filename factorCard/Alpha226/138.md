【因子信息开始】===============================================================

【因子编号和名称】

因子138: Alpha 138 (Alpha 138, A138)

【1. 因子名称详情】

因子138: Alpha 138 (Alpha 138, A138)

【2. 核心公式】

$$\text{Alpha138} = \text{sub} \left( \text{delta}(\text{open_price}, 9), \text{neg} \left( \text{gp_max} \left( \text{log}(\text{volume}), \text{ts_corr}(12, \text{low}, \text{volume}) \right) \right) \right)$$

【3. 变量定义】

* `open_price`: 开盘价。
* `volume`: 成交量。
* `low`: 最低价。

【4. 函数与方法说明】

* `delta(X, n)`: 计算 X 在当前周期的值与 n 个周期前的值的差。公式：$X_t - X_{t-n}$。
* `log(X)`: 计算输入数据 X 逐元素的绝对值的自然对数。公式：$\ln(|X|)$。
* `ts_corr(n, X, Y)`: 计算 X 和 Y 在过去 n 个周期内的滚动相关系数。公式：$\text{Corr}_n(X, Y)_t = \frac{\text{Cov}_n(X, Y)_t}{\text{StdDev}_n(X)_t \times \text{StdDev}_n(Y)_t}$。计算时 `min_periods=1`。
* `gp_max(A, B)`: 对两个输入数据 A 和 B 逐元素比较，取较大者。公式：$\max(A, B)$。
* `neg(X)`: 对输入数据 X 逐元素取负。公式：$-X$。
* `sub(A, B)`: 对输入数据 A 减去 B 进行逐元素减法操作。公式：$A - B$。

【5. 计算步骤】

1.  计算开盘价 (`open_price`) 在过去9个周期内的差值： $X_1 = \text{delta}(\text{open_price}, 9)$。
2.  计算成交量 (`volume`) 的绝对值的自然对数： $T_1 = \text{log}(\text{volume})$。
3.  计算最低价 (`low`) 和成交量 (`volume`) 在过去12个周期内的滚动相关系数： $T_2 = \text{ts_corr}(12, \text{low}, \text{volume})$。
4.  取 $T_1$ 和 $T_2$ 中逐元素的较大值： $T_3 = \text{gp_max}(T_1, T_2)$。
5.  计算 $T_3$ 的相反数： $X_2 = \text{neg}(T_3)$。
6.  计算 $X_1$ 与 $X_2$ 的差值得到 Alpha138： $\text{Alpha138} = \text{sub}(X_1, X_2)$。
7.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：9, 12。
* 滚动相关系数计算设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】