【因子信息开始】===============================================================
【因子编号和名称】
因子编号: Alpha_182 (Alpha_182)
【1. 因子名称详情】
因子全称: Alpha_182
【2. 核心公式】
$$Alpha_{182} = \text{arctan}(\text{ts_regres}(\text{sigmoid}(\text{ts_regbeta}(\text{abs}(\text{volume}), \text{sqrt}(\text{close}), 6)), \text{low}, 9))$$
【3. 变量定义】
\begin{itemize}
    \item $volume_t$: t时刻的成交量
    \item $close_t$: t时刻的收盘价
    \item $low_t$: t时刻的最低价
\end{itemize}
【4. 函数与方法说明】
\begin{itemize}
    \item $\text{abs}(data)$: 计算data的绝对值，即 $|data_t|$。
    \item $\text{sqrt}(data)$: 计算data的平方根，对于负数则取绝对值后再开方，即 $\sqrt{|data_t|}$。
    \item $\text{ts_regbeta}(x, y, window)$: 计算y对x在过去window期的滚动回归beta值。公式为：$\text{ts_regbeta}(x, y, window) = \frac{\text{Cov}(x, y, \text{window})}{\text{Var}(x, \text{window})}$。其中Cov和Var均为滚动计算。
    \item $\text{sigmoid}(x)$: Sigmoid函数，定义为 $1 / (1 + e^{-x})$。
    \item $\text{ts_regres}(x, y, window)$: 计算y对x在过去window期的滚动回归残差。公式为：$y_t - \text{ts_regbeta}(x, y, window) \times x_t$。
    \item $\text{arctan}(data)$: 计算data的反正切值。
\end{itemize}
【5. 计算步骤】
\begin{enumerate}
    \item 计算成交量 ($volume_t$) 的绝对值：$I_1 = \text{abs}(volume_t)$。
    \item 计算收盘价 ($close_t$) 的平方根：$I_2 = \text{sqrt}(close_t)$。
    \item 计算 $I_2$ 对 $I_1$ 在过去6期的滚动回归beta值：$I_3 = \text{ts_regbeta}(I_1, I_2, 6)$。
    \item 对 $I_3$ 应用Sigmoid函数：$I_4 = \text{sigmoid}(I_3)$。
    \item 计算最低价 ($low_t$) 对 $I_4$ 在过去9期的滚动回归残差：$I_5 = \text{ts_regres}(I_4, low_t, 9)$。
    \item 对 $I_5$ 应用反正切函数，得到因子值：$Alpha_{182} = \text{arctan}(I_5)$。
    \item 将结果中的正无穷 ($\text{np.inf}$) 和负无穷 ($-\text{np.inf}$) 替换为NaN。
\end{enumerate}
【6. 备注与参数说明】
\begin{itemize}
    \item 窗口期参数：ts\_regbeta为6，ts\_regres为9。
    \item 数据预处理：原始数据加载时已去除填充值。因子计算结果中的无穷值被替换为NaN。
\end{itemize}
【因子信息结束】