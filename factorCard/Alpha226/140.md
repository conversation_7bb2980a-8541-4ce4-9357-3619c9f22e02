【因子信息开始】===============================================================

【因子编号和名称】

因子140: Alpha 140 (Alpha 140, A140)

【1. 因子名称详情】

因子140: Alpha 140 (Alpha 140, A140)

【2. 核心公式】

$$\text{Alpha140} = \text{ts_regres} \left( \text{arctan} \left( \text{delay} \left( \text{ts_pctchg} \left( \text{gp_min} \left( \text{delay}(\text{open_price}, 16), \text{ts_pctchg} \left( \text{arctan}(\text{rank}(\text{vwap})), 12 \right) \right), 8 \right), 5 \right) \right), \text{neg}(\text{close}), 12 \right)$$

【3. 变量定义】

* `open_price`: 开盘价。
* `vwap`: 成交量加权平均价。
* `close`: 收盘价。

【4. 函数与方法说明】

* `delay(X, n)`: 获取 X 在 n 个周期前的值。公式：$X_{t-n}$。
* `rank(X)`: 计算 X 在截面上的排名。Pandas `rank(axis=1)` 默认使用升序排名。
* `arctan(X)`: 计算输入数据 X 逐元素的反正切值。公式：$\arctan(X)$。
* `ts_pctchg(X, n)`: 计算 X 在 n 个周期内的百分比变化率。公式：$(X_t - X_{t-n}) / X_{t-n}$。
* `gp_min(A, B)`: 对两个输入数据 A 和 B 逐元素比较，取较小者。公式：$\min(A, B)$。
* `ts_regres(X, Y, n)`: 计算 Y 对 X 在过去 n 个周期内的滚动回归的残差。公式：$\text{Resid}_{Y,X,n} = Y - \beta_{Y,X,n} \times X$。
* `neg(X)`: 对输入数据 X 逐元素取负。公式：$-X$。

【5. 计算步骤】

1.  获取开盘价 (`open_price`) 在16个周期前的值： $T_1 = \text{delay}(\text{open_price}, 16)$。
2.  计算 `vwap` 的截面排名： $T_2 = \text{rank}(\text{vwap})$。
3.  计算 $T_2$ 的反正切值： $T_3 = \text{arctan}(T_2)$。
4.  计算 $T_3$ 在过去12个周期内的百分比变化率： $T_4 = \text{ts_pctchg}(T_3, 12)$。
5.  取 $T_1$ 和 $T_4$ 中逐元素的较小值： $T_5 = \text{gp_min}(T_1, T_4)$。
6.  计算 $T_5$ 在过去8个周期内的百分比变化率： $T_6 = \text{ts_pctchg}(T_5, 8)$。
7.  获取 $T_6$ 在5个周期前的值： $T_7 = \text{delay}(T_6, 5)$。
8.  计算 $T_7$ 的反正切值： $X_1 = \text{arctan}(T_7)$。
9.  计算收盘价 (`close`) 的相反数： $X_2 = \text{neg}(\text{close})$。
10. 计算 $X_2$ 对 $X_1$ 在过去12个周期内的滚动回归残差得到 Alpha140： $\text{Alpha140} = \text{ts_regres}(X_1, X_2, 12)$。
11. 将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：16, 12, 8, 5, 12。
* 最终结果会进行无穷大值处理。

【因子信息结束】