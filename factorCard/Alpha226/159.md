【因子信息开始】===============================================================

【因子编号和名称】

因子159: Alpha 159 (Alpha 159, A159)

【1. 因子名称详情】

因子159: Alpha 159 (Alpha 159, A159)

【2. 核心公式】

$$\text{Alpha159} = \text{ts_regres} \left( \text{mul} \left( \text{arctan} \left( \text{ts_max}(\text{amount}, 10) \right), \text{ts_rank} \left( \text{ts_rank} \left( \text{ts_regbeta}(\text{close}, \text{amount}, 12), 10 \right), 8 \right) \right), \text{low}, 7 \right)$$

【3. 变量定义】

* `amount`: 成交额。
* `close`: 收盘价。
* `low`: 最低价。

【4. 函数与方法说明】

* `ts_max(X, n)`: 计算 X 在过去 n 个周期内的滚动最大值。公式：$\text{Max}_n(X)_t = \max(X_{t-n+1}, ..., X_t)$。计算时 `min_periods=1`。
* `arctan(X)`: 计算输入数据 X 逐元素的反正切值。公式：$\arctan(X)$。
* `ts_regbeta(X, Y, n)`: 计算 Y 对 X 在过去 n 个周期内的滚动回归的贝塔系数。公式：$\beta_{Y,X,n} = \frac{\text{Cov}_n(Y, X)}{\text{Var}_n(X)}$。
* `ts_rank(X, n)`: 计算 X 在过去 n 个周期内的滚动排名，并将结果归一化到 (0, 1] 区间。公式：$\text{Rank}_n^{\text{desc}}(X_t) / n$。计算时 `min_periods=1`。
* `mul(A, B)`: 对两个输入数据 A 和 B 进行逐元素乘法操作。公式：$A \times B$。
* `ts_regres(X, Y, n)`: 计算 Y 对 X 在过去 n 个周期内的滚动回归的残差。公式：$\text{Resid}_{Y,X,n} = Y - \beta_{Y,X,n} \times X$。

【5. 计算步骤】

1.  计算成交额 (`amount`) 在过去10个周期内的滚动最大值： $T_1 = \text{ts_max}(\text{amount}, 10)$。
2.  计算 $T_1$ 的反正切值： $M_1 = \text{arctan}(T_1)$。
3.  计算成交额 (`amount`) 对收盘价 (`close`) 在过去12个周期内的滚动回归贝塔系数： $T_2 = \text{ts_regbeta}(\text{close}, \text{amount}, 12)$。
4.  计算 $T_2$ 在过去10个周期内的滚动排名并归一化： $T_3 = \text{ts_rank}(T_2, 10)$。
5.  计算 $T_3$ 在过去8个周期内的滚动排名并归一化： $M_2 = \text{ts_rank}(T_3, 8)$。
6.  计算 $M_1$ 与 $M_2$ 的乘积： $X_1 = \text{mul}(M_1, M_2)$。
7.  计算最低价 (`low`) 对 $X_1$ 在过去7个周期内的滚动回归残差得到 Alpha159： $\text{Alpha159} = \text{ts_regres}(X_1, \text{low}, 7)$。
8.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：10, 12, 10, 8, 7。
* 所有滚动计算均设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】