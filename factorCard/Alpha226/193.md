【因子信息开始】===============================================================
【因子编号和名称】
因子编号: Alpha193
【1. 因子名称详情】
因子193: Alpha193
【2. 核心公式】
$$\text{Alpha193} = \text{TS\_COV}(8, \text{VWAP}, \text{TS\_RANK}(\text{VOLUME}, 12))$$
【3. 变量定义】
* $\text{VWAP}$: 每日成交量加权平均价。
* $\text{VOLUME}$: 每日成交量。
【4. 函数与方法说明】
* $\text{TS\_COV}(n, x, y)$: 计算 $x$ 和 $y$ 在 $n$ 周期内的协方差。
    $$
    \text{TS\_COV}(n, x, y)_t = \frac{1}{n-1} \sum_{i=0}^{n-1} (x_{t-i} - \text{TS\_MEAN}(x, n)_t) (y_{t-i} - \text{TS\_MEAN}(y, n)_t)
    $$
* $\text{TS\_RANK}(\text{data}, n)$: 计算 $\text{data}$ 在 $n$ 周期内的排名百分比。
    $$
    \text{TS\_RANK}(\text{data}, n)_t = \frac{\text{rank}(\text{data}_t \text{ in } \{\text{data}_{t-n+1}, \dots, \text{data}_t\})}{n}
    $$
    其中 $\text{rank}(x)$ 表示 $x$ 在给定序列中的从小到大的排名。
【5. 计算步骤】
1.  计算 $\text{TS\_RANK}(\text{VOLUME}, 12)$：对 $\text{VOLUME}$ 计算12周期内的排名百分比。
2.  计算 $\text{TS\_COV}(8, \text{VWAP}, \text{TS\_RANK}(\text{VOLUME}, 12))$：计算 $\text{VWAP}$ 和步骤1结果在8周期内的协方差，得到 $\text{Alpha193}$。
3.  将结果中的无穷大和负无穷大值替换为 $\text{NaN}$。
【6. 备注与参数说明】
该因子衡量了成交量加权平均价与成交量时间序列排名之间的协方差。窗口期参数分别为12和8。

【因子信息结束】