【因子信息开始】===============================================================

【因子编号和名称】

因子128: Alpha 128 (Alpha 128, A128)

【1. 因子名称详情】

因子128: Alpha 128 (Alpha 128, A128)

【2. 核心公式】

$$\text{Alpha128} = \text{add} \left( \text{delta}(\text{close}, 8), \text{arctan}(\text{high}) \right)$$

【3. 变量定义】

* `close`: 收盘价。
* `high`: 最高价。

【4. 函数与方法说明】

* `delta(X, n)`: 计算 X 在当前周期的值与 n 个周期前的值的差。公式：$X_t - X_{t-n}$。
* `arctan(X)`: 计算输入数据 X 逐元素的反正切值。公式：$\arctan(X)$。
* `add(A, B)`: 对两个输入数据 A 和 B 进行逐元素加法操作。公式：$A + B$。

【5. 计算步骤】

1.  计算收盘价 (`close`) 在过去8个周期内的差值： $X_1 = \text{delta}(\text{close}, 8)$。
2.  计算最高价 (`high`) 的反正切值： $X_2 = \text{arctan}(\text{high})$。
3.  计算 $X_1$ 与 $X_2$ 的和得到 Alpha128： $\text{Alpha128} = \text{add}(X_1, X_2)$。
4.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数为：8。
* 最终结果会进行无穷大值处理。

【因子信息结束】