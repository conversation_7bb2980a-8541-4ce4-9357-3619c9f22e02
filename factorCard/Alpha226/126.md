【因子信息开始】===============================================================

【因子编号和名称】

因子126: Alpha 126 (Alpha 126, A126)

【1. 因子名称详情】

因子126: Alpha 126 (Alpha 126, A126)

【2. 核心公式】

$$\text{Alpha126} = \text{ts_corr}(10, \text{abs}(\text{volume}), \text{ts_pctchg}(\text{open_price}, 16))$$

【3. 变量定义】

* `volume`: 成交量。
* `open_price`: 开盘价。

【4. 函数与方法说明】

* `abs(X)`: 计算 X 的绝对值。
* `ts_pctchg(X, n)`: 计算 X 在 n 个周期内的百分比变化率。公式：$(X_t - X_{t-n}) / X_{t-n}$。
* `ts_corr(n, X, Y)`: 计算 X 和 Y 在过去 n 个周期内的滚动相关系数。公式：$\text{Corr}_n(X, Y)_t = \frac{\text{Cov}_n(X, Y)_t}{\text{StdDev}_n(X)_t \times \text{StdDev}_n(Y)_t}$。计算时 `min_periods=1`。

【5. 计算步骤】

1.  计算成交量 (`volume`) 的绝对值： $X_1 = \text{abs}(\text{volume})$。
2.  计算开盘价 (`open_price`) 在过去16个周期内的百分比变化率： $X_2 = \text{ts_pctchg}(\text{open_price}, 16)$。
3.  计算 $X_1$ 和 $X_2$ 在过去10个周期内的滚动相关系数得到 Alpha126： $\text{Alpha126} = \text{ts_corr}(10, X_1, X_2)$。
4.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：16, 10。
* 滚动相关系数计算设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】