【因子信息开始】===============================================================

【因子编号和名称】

因子153: Alpha 153 (Alpha 153, A153)

【1. 因子名称详情】

因子153: Alpha 153 (Alpha 153, A153)

【2. 核心公式】

$$\text{Alpha153} = \text{mul} \left( \text{gp_max} \left( \text{sub}(\text{open_price}, \text{volume}), \text{gp_max}(\text{volume}, \text{vwap}) \right), \text{delta}(\text{high}, 8) \right)$$

【3. 变量定义】

* `open_price`: 开盘价。
* `volume`: 成交量。
* `vwap`: 成交量加权平均价。
* `high`: 最高价。

【4. 函数与方法说明】

* `sub(A, B)`: 对输入数据 A 减去 B 进行逐元素减法操作。公式：$A - B$。
* `gp_max(A, B)`: 对两个输入数据 A 和 B 逐元素比较，取较大者。公式：$\max(A, B)$。
* `delta(X, n)`: 计算 X 在当前周期的值与 n 个周期前的值的差。公式：$X_t - X_{t-n}$。
* `mul(A, B)`: 对两个输入数据 A 和 B 进行逐元素乘法操作。公式：$A \times B$。

【5. 计算步骤】

1.  计算开盘价 (`open_price`) 与成交量 (`volume`) 的差值： $T_1 = \text{sub}(\text{open_price}, \text{volume})$。
2.  取成交量 (`volume`) 和 `vwap` 中逐元素的较大值： $T_2 = \text{gp_max}(\text{volume}, \text{vwap})$。
3.  取 $T_1$ 和 $T_2$ 中逐元素的较大值： $X_1 = \text{gp_max}(T_1, T_2)$。
4.  计算最高价 (`high`) 在过去8个周期内的差值： $X_2 = \text{delta}(\text{high}, 8)$。
5.  计算 $X_1$ 与 $X_2$ 的乘积得到 Alpha153： $\text{Alpha153} = \text{mul}(X_1, X_2)$。
6.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数为：8。
* 最终结果会进行无穷大值处理。

【因子信息结束】