【因子信息开始】===============================================================

【因子编号和名称】

因子107: Alpha 107 (Alpha 107, A107)

【1. 因子名称详情】

因子107: Alpha 107 (Alpha 107, A107)

【2. 核心公式】

$$\text{Alpha107} = \text{ts_corr} \left( 15, \text{add}(\text{low}, \text{gp_max}(\text{volume}, \text{high})), \text{ts_pctchg}(\text{log}(\text{vwap}), 8) \right)$$

【3. 变量定义】

* `low`: 最低价。
* `volume`: 成交量。
* `high`: 最高价。
* `vwap`: 成交量加权平均价。

【4. 函数与方法说明】

* `gp_max(A, B)`: 对两个输入数据 A 和 B 逐元素比较，取较大者。公式：$\max(A, B)$。
* `add(A, B)`: 对两个输入数据 A 和 B 进行逐元素加法操作。公式：$A + B$。
* `log(X)`: 计算输入数据 X 逐元素的绝对值的自然对数。公式：$\ln(|X|)$。
* `ts_pctchg(X, n)`: 计算 X 在 n 个周期内的百分比变化率。公式：$(X_t - X_{t-n}) / X_{t-n}$。
* `ts_corr(n, X, Y)`: 计算 X 和 Y 在过去 n 个周期内的滚动相关系数。公式：$\text{Corr}_n(X, Y)_t = \frac{\text{Cov}_n(X, Y)_t}{\text{StdDev}_n(X)_t \times \text{StdDev}_n(Y)_t}$。计算时 `min_periods=1`。

【5. 计算步骤】

1.  取成交量 (`volume`) 和最高价 (`high`) 中逐元素的较大值： $T_1 = \text{gp_max}(\text{volume}, \text{high})$。
2.  计算最低价 (`low`) 与 $T_1$ 的和： $X_1 = \text{add}(\text{low}, T_1)$。
3.  计算 `vwap` 的绝对值的自然对数： $T_2 = \text{log}(\text{vwap})$。
4.  计算 $T_2$ 在过去8个周期内的百分比变化率： $X_2 = \text{ts_pctchg}(T_2, 8)$。
5.  计算 $X_1$ 和 $X_2$ 在过去15个周期内的滚动相关系数得到 Alpha107： $\text{Alpha107} = \text{ts_corr}(15, X_1, X_2)$。
6.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：8, 15。
* 滚动相关系数计算设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】