【因子信息开始】===============================================================
【因子编号和名称】
因子编号: Alpha_186 (Alpha_186)
【1. 因子名称详情】
因子全称: Alpha_186
【2. 核心公式】
$$Alpha_{186} = \text{ts_corr}(10, \text{volume}, \text{vwap})$$
【3. 变量定义】
\begin{itemize}
    \item $volume_t$: t时刻的成交量
    \item $vwap_t$: t时刻的成交量加权平均价
\end{itemize}
【4. 函数与方法说明】
\begin{itemize}
    \item $\text{ts_corr}(window, x, y)$: 计算x和y在过去window期（包括当期）的滚动相关系数。计算时，当期之前的可用数据不足window期时，则使用实际可用期数计算（min\_periods=1）。公式为：$\frac{\text{Cov}(x, y, \text{window})}{\text{StdDev}(x, \text{window}) \times \text{StdDev}(y, \text{window})}$。
\end{itemize}
【5. 计算步骤】
\begin{enumerate}
    \item 计算成交量 ($volume_t$) 和成交量加权平均价 ($vwap_t$) 在过去10期的滚动相关系数，得到因子值：$Alpha_{186} = \text{ts_corr}(10, volume_t, vwap_t)$。
    \item 将结果中的正无穷 ($\text{np.inf}$) 和负无穷 ($-\text{np.inf}$) 替换为NaN。
\end{enumerate}
【6. 备注与参数说明】
\begin{itemize}
    \item 窗口期参数：ts\_corr为10。
    \item 数据预处理：原始数据加载时已去除填充值。因子计算结果中的无穷值被替换为NaN。
\end{itemize}
【因子信息结束】