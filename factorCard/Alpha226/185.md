【因子信息开始】===============================================================
【因子编号和名称】
因子编号: Alpha_185 (Alpha_185)
【1. 因子名称详情】
因子全称: Alpha_185
【2. 核心公式】
$$Alpha_{185} = \text{abs}(\text{sub}(\text{ts_regbeta}(\text{amount}, \text{low}, 8), \text{div}(\text{close}, \text{open_price})))$$
【3. 变量定义】
\begin{itemize}
    \item $amount_t$: t时刻的成交额
    \item $low_t$: t时刻的最低价
    \item $close_t$: t时刻的收盘价
    \item $open\_price_t$: t时刻的开盘价
\end{itemize}
【4. 函数与方法说明】
\begin{itemize}
    \item $\text{ts_regbeta}(x, y, window)$: 计算y对x在过去window期的滚动回归beta值。公式为：$\text{ts_regbeta}(x, y, window) = \frac{\text{Cov}(x, y, \text{window})}{\text{Var}(x, \text{window})}$。其中Cov和Var均为滚动计算。
    \item $\text{div}(x, y)$: 元素级别的除法，即 $x_t / y_t$。
    \item $\text{sub}(x, y)$: 元素级别的减法，即 $x_t - y_t$。
    \item $\text{abs}(data)$: 计算data的绝对值，即 $|data_t|$。
\end{itemize}
【5. 计算步骤】
\begin{enumerate}
    \item 计算最低价 ($low_t$) 对成交额 ($amount_t$) 在过去8期的滚动回归beta值：$I_1 = \text{ts_regbeta}(amount_t, low_t, 8)$。
    \item 计算收盘价 ($close_t$) 除以开盘价 ($open\_price_t$)：$I_2 = \text{div}(close_t, open\_price_t)$。
    \item 计算 $I_1$ 与 $I_2$ 的差值：$I_3 = \text{sub}(I_1, I_2)$。
    \item 对 $I_3$ 取绝对值，得到因子值：$Alpha_{185} = \text{abs}(I_3)$。
    \item 将结果中的正无穷 ($\text{np.inf}$) 和负无穷 ($-\text{np.inf}$) 替换为NaN。
\end{enumerate}
【6. 备注与参数说明】
\begin{itemize}
    \item 窗口期参数：ts\_regbeta为8。
    \item 数据预处理：原始数据加载时已去除填充值。因子计算结果中的无穷值被替换为NaN。
\end{itemize}
【因子信息结束】