【因子信息开始】===============================================================
【因子编号和名称】
因子编号: Alpha_183 (Alpha_183)
【1. 因子名称详情】
因子全称: Alpha_183
【2. 核心公式】
$$Alpha_{183} = \text{add}(\text{delta}(\text{delta}(\text{close}, 16), 5), \text{ts_max}(\text{rank}(\text{amount}), 14))$$
【3. 变量定义】
\begin{itemize}
    \item $close_t$: t时刻的收盘价
    \item $amount_t$: t时刻的成交额
\end{itemize}
【4. 函数与方法说明】
\begin{itemize}
    \item $\text{delta}(data, window)$: 计算当前值与window期前值的差，即 $data_t - data_{t-window}$。
    \item $\text{delay}(data, window)$: 获取data在window期前的值，即 $data_{t-window}$。
    \item $\text{rank}(data)$: 计算data在截面上的排名（axis=1）。即对每一天，所有股票的因子值进行排序并赋予排名。
    \item $\text{ts_max}(data, n)$: 计算data在过去n期（包括当期）的滚动最大值。计算时，当期之前的可用数据不足n期时，则使用实际可用期数计算（min\_periods=1）。
    \item $\text{add}(x, y)$: 元素级别的加法，即 $x_t + y_t$。
\end{itemize}
【5. 计算步骤】
\begin{enumerate}
    \item 计算收盘价 ($close_t$) 的16期差值：$I_1 = \text{delta}(close_t, 16)$。
    \item 计算 $I_1$ 的5期差值 (即收盘价的二阶差分)：$I_2 = \text{delta}(I_1, 5)$。
    \item 对成交额 ($amount_t$) 进行截面排名：$I_3 = \text{rank}(amount_t)$。
    \item 计算 $I_3$ 在过去14期的滚动最大值：$I_4 = \text{ts_max}(I_3, 14)$。
    \item 计算 $I_2$ 与 $I_4$ 的和，得到因子值：$Alpha_{183} = \text{add}(I_2, I_4)$。
    \item 将结果中的正无穷 ($\text{np.inf}$) 和负无穷 ($-\text{np.inf}$) 替换为NaN。
\end{enumerate}
【6. 备注与参数说明】
\begin{itemize}
    \item 窗口期参数：第一个delta为16，第二个delta为5，ts\_max为14。
    \item 数据预处理：原始数据加载时已去除填充值。因子计算结果中的无穷值被替换为NaN。
\end{itemize}
【因子信息结束】