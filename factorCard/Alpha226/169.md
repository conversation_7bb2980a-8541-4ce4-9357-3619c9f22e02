【因子信息开始】===============================================================
【因子编号和名称】
因子编号: Alpha_169 (Alpha_169)
【1. 因子名称详情】
因子全称: Alpha_169
【2. 核心公式】
$$Alpha_{169} = \text{gp_min}(\text{gp_max}(\text{sigmoid}(\text{vwap}), \text{delta}(\text{volume}, 2)), \text{ts_pctchg}(\text{high}, 5))$$
【3. 变量定义】
\begin{itemize}
    \item $vwap_t$: t时刻的成交量加权平均价
    \item $volume_t$: t时刻的成交量
    \item $high_t$: t时刻的最高价
\end{itemize}
【4. 函数与方法说明】
\begin{itemize}
    \item $\text{sigmoid}(x)$: Sigmoid函数，定义为 $1 / (1 + e^{-x})$。
    \item $\text{delta}(data, window)$: 计算当前值与window期前值的差，即 $data_t - data_{t-window}$。
    \item $\text{delay}(data, window)$: 获取data在window期前的值，即 $data_{t-window}$。
    \item $\text{gp_max}(data1, data2)$: 元素级别的较大值，即 $\max(data1_t, data2_t)$。
    \item $\text{ts_pctchg}(data, n)$: 计算data在过去n期的百分比变化率，即 $(data_t - data_{t-n}) / data_{t-n}$。
    \item $\text{gp_min}(data1, data2)$: 元素级别的较小值，即 $\min(data1_t, data2_t)$。
\end{itemize}
【5. 计算步骤】
\begin{enumerate}
    \item 对成交量加权平均价 ($vwap_t$) 应用Sigmoid函数：$I_1 = \text{sigmoid}(vwap_t)$。
    \item 计算成交量 ($volume_t$) 的2期差值：$I_2 = \text{delta}(volume_t, 2)$。
    \item 取 $I_1$ 和 $I_2$ 中元素级别的较大值：$I_3 = \text{gp_max}(I_1, I_2)$。
    \item 计算最高价 ($high_t$) 的5期百分比变化率：$I_4 = \text{ts_pctchg}(high_t, 5)$。
    \item 取 $I_3$ 和 $I_4$ 中元素级别的较小值，得到因子值：$Alpha_{169} = \text{gp_min}(I_3, I_4)$。
    \item 将结果中的正无穷 ($\text{np.inf}$) 和负无穷 ($-\text{np.inf}$) 替换为NaN。
\end{enumerate}
【6. 备注与参数说明】
\begin{itemize}
    \item 窗口期参数：delta为2，ts\_pctchg为5。
    \item 数据预处理：原始数据加载时已去除填充值。因子计算结果中的无穷值被替换为NaN。
\end{itemize}
【因子信息结束】