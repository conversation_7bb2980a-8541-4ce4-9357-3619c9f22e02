【因子信息开始】===============================================================
【因子编号和名称】
因子编号: Alpha204
【1. 因子名称详情】
因子204: Alpha204
【2. 核心公式】
$$\text{Alpha204} = \frac{\frac{-\text{HIGH}}{\text{LOW} - \text{VWAP}}}{\text{RANK}(\text{TS\_COV}(16, \text{GP\_MAX}(\text{HIGH}, \text{VWAP}), \text{VOLUME}))}$$
【3. 变量定义】
* $\text{HIGH}$: 每日最高价。
* $\text{LOW}$: 每日最低价。
* $\text{VWAP}$: 每日成交量加权平均价。
* $\text{VOLUME}$: 每日成交量。
【4. 函数与方法说明】
* $\text{NEG}(x)$: 取 $x$ 的负值。
* $\text{GP\_MAX}(x, y)$: 逐元素比较 $x$ 和 $y$，返回较大值。
    $$
    \text{GP\_MAX}(x, y) = \max(x, y)
    $$
* $\text{TS\_COV}(n, x, y)$: 计算 $x$ 和 $y$ 在 $n$ 周期内的协方差。
    $$
    \text{TS\_COV}(n, x, y)_t = \frac{1}{n-1} \sum_{i=0}^{n-1} (x_{t-i} - \text{TS\_MEAN}(x, n)_t) (y_{t-i} - \text{TS\_MEAN}(y, n)_t)
    $$
* $\text{RANK}(x)$: 计算 $x$ 在截面上的排名（从小到大）。
【5. 计算步骤】
1.  计算 $-\text{HIGH}$。
2.  计算 $\text{LOW} - \text{VWAP}$。
3.  计算 $\frac{-\text{HIGH}}{\text{LOW} - \text{VWAP}}$。
4.  计算 $\text{GP\_MAX}(\text{HIGH}, \text{VWAP})$：逐元素比较 $\text{HIGH}$ 和 $\text{VWAP}$，返回较大值。
5.  计算 $\text{TS\_COV}(16, \text{GP\_MAX}(\text{HIGH}, \text{VWAP}), \text{VOLUME})$：计算步骤4结果和 $\text{VOLUME}$ 在16周期内的协方差。
6.  计算 $\text{RANK}(\text{TS\_COV}(16, \text{GP\_MAX}(\text{HIGH}, \text{VWAP}), \text{VOLUME}))$：对步骤5结果进行截面排名。
7.  将步骤3的结果除以步骤6的结果，得到 $\text{Alpha204}$。
8.  将结果中的无穷大和负无穷大值替换为 $\text{NaN}$。
【6. 备注与参数说明】
该因子结合了最高价负值与最低价和VWAP差值的比率，再除以最高价和VWAP最大值与成交量协方差的截面排名。窗口期参数为16。

【因子信息结束】