【因子信息开始】===============================================================

【因子编号和名称】

因子139: Alpha 139 (Alpha 139, A139)

【1. 因子名称详情】

因子139: Alpha 139 (Alpha 139, A139)

【2. 核心公式】

$$\text{Alpha139} = \text{mul} \left( \text{log}(\text{abs}(\text{volume})), \text{delta}(\text{rank}(\text{open_price}), 9) \right)$$
注意：原始代码中 `log(abs(self.volume))`，`log` 函数已包含 `abs` 处理。

【3. 变量定义】

* `volume`: 成交量。
* `open_price`: 开盘价。

【4. 函数与方法说明】

* `log(X)`: 计算输入数据 X 逐元素的绝对值的自然对数。公式：$\ln(|X|)$。
* `rank(X)`: 计算 X 在截面上的排名。Pandas `rank(axis=1)` 默认使用升序排名。
* `delta(X, n)`: 计算 X 在当前周期的值与 n 个周期前的值的差。公式：$X_t - X_{t-n}$。
* `mul(A, B)`: 对两个输入数据 A 和 B 进行逐元素乘法操作。公式：$A \times B$。

【5. 计算步骤】

1.  计算成交量 (`volume`) 的绝对值的自然对数： $X_1 = \text{log}(\text{volume})$。(代码中 `log(abs(volume))`，但`log`已处理abs)
2.  计算开盘价 (`open_price`) 的截面排名： $T_1 = \text{rank}(\text{open_price})$。
3.  计算 $T_1$ 在过去9个周期内的差值： $X_2 = \text{delta}(T_1, 9)$。
4.  计算 $X_1$ 与 $X_2$ 的乘积得到 Alpha139： $\text{Alpha139} = \text{mul}(X_1, X_2)$。
5.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数为：9。
* `log` 函数定义为 `np.log(abs(data))`，所以 `log(abs(volume))` 等价于 `log(volume)`。
* 最终结果会进行无穷大值处理。

【因子信息结束】