【因子信息开始】===============================================================

【因子编号和名称】

因子122: Alpha 122 (Alpha 122, A122)

【1. 因子名称详情】

因子122: Alpha 122 (Alpha 122, A122)

【2. 核心公式】

$$\text{Alpha122} = \text{rank}(\text{ts_corr}(8, \text{amount}, \text{open_price}))$$

【3. 变量定义】

* `amount`: 成交额，指在一定时间内特定证券交易的总金额。
* `open_price`: 开盘价，指交易日开始时的证券价格。

【4. 函数与方法说明】

* `ts_corr(n, X, Y)`: 计算 X 和 Y 在过去 n 个周期内的滚动相关系数。公式：$\text{Corr}_n(X, Y)_t = \frac{\text{Cov}_n(X, Y)_t}{\text{StdDev}_n(X)_t \times \text{StdDev}_n(Y)_t}$。计算时 `min_periods=1`。
* `rank(X)`: 计算 X 在截面上的排名。对于每个时间点，对所有样本（例如股票）的 X 值进行排序并赋予排名。Pandas `rank(axis=1)` 默认使用升序排名（数值越小排名越低），并对相同值取平均排名。

【5. 计算步骤】

1.  计算成交额 (`amount`) 和开盘价 (`open_price`) 在过去8个周期内的滚动相关系数： $X_1 = \text{ts_corr}(8, \text{amount}, \text{open_price})$。
2.  对 $X_1$ 在每个时间点上进行截面排名得到 Alpha122： $\text{Alpha122} = \text{rank}(X_1)$。
3.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数为：8。
* 滚动相关系数计算设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】