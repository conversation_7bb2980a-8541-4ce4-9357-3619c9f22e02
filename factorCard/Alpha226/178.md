【因子信息开始】===============================================================
【因子编号和名称】
因子编号: Alpha_178 (Alpha_178)
【1. 因子名称详情】
因子全称: Alpha_178
【2. 核心公式】
$$Alpha_{178} = \text{ts_regres}(\text{delay}(\text{high}, 16), \text{close}, 14)$$
【3. 变量定义】
\begin{itemize}
    \item $high_t$: t时刻的最高价
    \item $close_t$: t时刻的收盘价
\end{itemize}
【4. 函数与方法说明】
\begin{itemize}
    \item $\text{delay}(data, window)$: 获取data在window期前的值，即 $data_{t-window}$。
    \item $\text{ts_regbeta}(x, y, window)$: 计算y对x在过去window期的滚动回归beta值。公式为：$\text{ts_regbeta}(x, y, window) = \frac{\text{Cov}(x, y, \text{window})}{\text{Var}(x, \text{window})}$。其中Cov和Var均为滚动计算。
    \item $\text{ts_regres}(x, y, window)$: 计算y对x在过去window期的滚动回归残差。公式为：$y_t - \text{ts_regbeta}(x, y, window) \times x_t$。
\end{itemize}
【5. 计算步骤】
\begin{enumerate}
    \item 将最高价 ($high_t$) 延迟16期：$I_1 = \text{delay}(high_t, 16)$。
    \item 计算收盘价 ($close_t$) 对 $I_1$ 在过去14期的滚动回归残差，得到因子值：$Alpha_{178} = \text{ts_regres}(I_1, close_t, 14)$。
    \item 将结果中的正无穷 ($\text{np.inf}$) 和负无穷 ($-\text{np.inf}$) 替换为NaN。
\end{enumerate}
【6. 备注与参数说明】
\begin{itemize}
    \item 窗口期参数：delay为16，ts\_regres为14。
    \item 数据预处理：原始数据加载时已去除填充值。因子计算结果中的无穷值被替换为NaN。
\end{itemize}
【因子信息结束】