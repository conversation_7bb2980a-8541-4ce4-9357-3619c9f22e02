【因子信息开始】===============================================================

【因子编号和名称】

因子131: Alpha 131 (Alpha 131, A131)

【1. 因子名称详情】

因子131: Alpha 131 (Alpha 131, A131)

【2. 核心公式】

$$\text{Alpha131} = \text{add} \left( \text{rank} \left( \text{ts_regbeta}(\text{volume}, \text{open_price}, 9) \right), \text{neg} \left( \text{ts_regbeta}(\text{close}, \text{low}, 16) \right) \right)$$

【3. 变量定义】

* `volume`: 成交量。
* `open_price`: 开盘价。
* `close`: 收盘价。
* `low`: 最低价。

【4. 函数与方法说明】

* `ts_regbeta(X, Y, n)`: 计算 Y 对 X 在过去 n 个周期内的滚动回归的贝塔系数。公式：$\beta_{Y,X,n} = \frac{\text{Cov}_n(Y, X)}{\text{Var}_n(X)}$。
* `rank(X)`: 计算 X 在截面上的排名。Pandas `rank(axis=1)` 默认使用升序排名。
* `neg(X)`: 对输入数据 X 逐元素取负。公式：$-X$。
* `add(A, B)`: 对两个输入数据 A 和 B 进行逐元素加法操作。公式：$A + B$。

【5. 计算步骤】

1.  计算开盘价 (`open_price`) 对成交量 (`volume`) 在过去9个周期内的滚动回归贝塔系数： $T_1 = \text{ts_regbeta}(\text{volume}, \text{open_price}, 9)$。
2.  计算 $T_1$ 的截面排名： $X_1 = \text{rank}(T_1)$。
3.  计算最低价 (`low`) 对收盘价 (`close`) 在过去16个周期内的滚动回归贝塔系数： $T_2 = \text{ts_regbeta}(\text{close}, \text{low}, 16)$。
4.  计算 $T_2$ 的相反数： $X_2 = \text{neg}(T_2)$。
5.  计算 $X_1$ 与 $X_2$ 的和得到 Alpha131： $\text{Alpha131} = \text{add}(X_1, X_2)$。
6.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：9, 16。
* 最终结果会进行无穷大值处理。

【因子信息结束】