【因子信息开始】===============================================================
【因子编号和名称】
因子编号: Alpha201
【1. 因子名称详情】
因子201: Alpha201
【2. 核心公式】
$$\text{Alpha201} = \text{TS\_CORR}(16, \text{ARCTAN}(\text{LOW}), \text{GP\_MIN}(\text{TS\_PCTCHG}(\text{OPEN}, 12), \text{DELTA}(\text{VOLUME}, 10)))$$
【3. 变量定义】
* $\text{LOW}$: 每日最低价。
* $\text{OPEN}$: 每日开盘价。
* $\text{VOLUME}$: 每日成交量。
* $\text{ARCTAN}(x)$: 反正切函数。
【4. 函数与方法说明】
* $\text{ARCTAN}(x)$: 反正切函数。
* $\text{TS\_PCTCHG}(\text{df}, n)$: 计算DataFrame `df` 在 $n$ 周期内的百分比变化。
    $$
    \text{PCT\_CHG}(\text{df}, n)_t = \frac{\text{df}_t - \text{df}_{t-n}}{\text{df}_{t-n}}
    $$
* $\text{DELTA}(\text{data}, n)$: 计算 $\text{data}$ 与其 $n$ 期前数据之差。
    $$
    \text{DELTA}(\text{data}, n)_t = \text{data}_t - \text{data}_{t-n}
    $$
* $\text{GP\_MIN}(x, y)$: 逐元素比较 $x$ 和 $y$，返回较小值。
    $$
    \text{GP\_MIN}(x, y) = \min(x, y)
    $$
* $\text{TS\_CORR}(n, x, y)$: 计算 $x$ 和 $y$ 在 $n$ 周期内的相关系数。
    $$
    \text{TS\_CORR}(n, x, y)_t = \frac{\text{TS\_COV}(n, x, y)_t}{\text{TS\_STD}(x, n)_t \cdot \text{TS\_STD}(y, n)_t}
    $$
【5. 计算步骤】
1.  计算 $\text{ARCTAN}(\text{LOW})$：对 $\text{LOW}$ 计算反正切。
2.  计算 $\text{TS\_PCTCHG}(\text{OPEN}, 12)$：对 $\text{OPEN}$ 计算12周期内的百分比变化。
3.  计算 $\text{DELTA}(\text{VOLUME}, 10)$：计算 $\text{VOLUME}$ 与其10期前数据之差。
4.  计算 $\text{GP\_MIN}(\text{TS\_PCTCHG}(\text{OPEN}, 12), \text{DELTA}(\text{VOLUME}, 10))$：逐元素比较步骤2结果和步骤3结果，返回较小值。
5.  计算 $\text{TS\_CORR}(16, \text{ARCTAN}(\text{LOW}), \text{GP\_MIN}(\text{TS\_PCTCHG}(\text{OPEN}, 12), \text{DELTA}(\text{VOLUME}, 10)))$：计算步骤1结果和步骤4结果在16周期内的相关系数，得到 $\text{Alpha201}$。
6.  将结果中的无穷大和负无穷大值替换为 $\text{NaN}$。
【6. 备注与参数说明】
该因子衡量了最低价反正切与开盘价百分比变化和成交量差值的最小值之间的相关性。窗口期参数分别为12, 10和16。

【因子信息结束】