【因子信息开始】===============================================================

【因子编号和名称】

因子编号: TC003 (这是一个示例编号，您可以自行修改)
因子中文名称: 价格变动率指标-百刻度 (Rate of Change Ratio 100 Scale, ROCR100)

【1. 因子名称详情】

因子全称：价格变动率指标-百刻度 (Rate of Change Ratio 100 Scale)
英文简称：ROCR100
功能：衡量当前价格相对于N个周期前价格的比率，并以100为基准进行标度。

【2. 核心公式】

对于时间点 $t$ 和给定的时间周期参数 $N$，ROCR100的计算公式如下：

$ROCR100_t = \left( \frac{P_t}{P_{t-N}} \right) \times 100$

特殊情况处理：
如果 $P_{t-N} = 0$，则 $ROCR100_t = 0$。

【3. 变量定义】

*   $ROCR100_t$: 在时间点 $t$ 的ROCR100因子值。
*   $P_t$: 在时间点 $t$ 的价格（通常指收盘价，但可以是任何价格序列，如开盘价、最高价、最低价等）。
*   $P_{t-N}$: 在时间点 $t-N$ 的价格，即当前时间点 $t$ 向前回溯 $N$ 个周期的价格。
*   $N$: 时间周期参数，表示计算价格变动时回溯的周期数。

【4. 函数与方法说明】

该因子计算主要涉及基本的算术运算：
*   **除法**: 用于计算当前价格与N周期前价格的比率。
*   **乘法**: 用于将该比率乘以100，将其转换为以100为基准的刻度。

没有使用复杂的统计函数或特殊数学方法。

【5. 计算步骤】

1.  **数据准备**:
    *   获取输入的价格时间序列数据，记为 $P = \{P_0, P_1, P_2, \dots, P_M\}$，其中 $M+1$ 是数据点的总数。
    *   确定参数：时间周期 $N$ (例如，TA-LIB中默认为10)。

2.  **初始化**:
    *   由于计算需要 $N$ 个周期前的价格，因此ROCR100序列的前 $N$ 个值是未定义的或无法计算的。计算将从第 $N$ 个数据点开始（如果数据点从0开始索引，即 $t=N$）。

3.  **迭代计算**:
    *   对于时间序列中的每一个有效数据点 $t$（从 $N$ 到 $M$）：
        a.  获取当前周期的价格 $P_t$。
        b.  获取 $N$ 个周期前的价格 $P_{t-N}$。
        c.  **检查分母**：
            i.  如果 $P_{t-N}$ 不等于 0，则计算 $ROCR100_t = (P_t / P_{t-N}) \times 100$。
            ii. 如果 $P_{t-N}$ 等于 0，则 $ROCR100_t = 0.0$。
        d.  将计算得到的 $ROCR100_t$ 值存入结果序列。

4.  **输出**:
    *   生成ROCR100的时间序列。该序列的长度将比原始价格序列短 $N$ 个周期。

【6. 备注与参数说明】

*   **参数选择 ($N$)**:
    *   $N$ (对应源码中的 `optInTimePeriod`) 是此因子的核心参数。它定义了比较价格的时间跨度。
    *   TA-LIB中 `optInTimePeriod` 的默认值为10。常见的选择可能包括9, 12, 14, 20等，具体取决于分析的资产类型和交易策略的时间范围。较短的周期对近期价格变化更敏感，而较长的周期则反映更长期的趋势。
    *   参数 $N$ 的取值范围在TA-LIB源码中建议为1到100000。

*   **数据预处理**:
    *   输入的价格数据应为有效的数值。
    *   对于 $P_{t-N} = 0$ 的情况，因子值被设定为0，以避免除以零的错误。在实际金融数据中，价格通常不会为0，但此处理增加了计算的稳健性。

*   **因子解读**:
    *   当 $ROCR100_t > 100$ 时，表示当前价格 $P_t$ 高于 $N$ 个周期前的价格 $P_{t-N}$，可能指示上升势头。
    *   当 $ROCR100_t < 100$ 时，表示当前价格 $P_t$ 低于 $N$ 个周期前的价格 $P_{t-N}$，可能指示下降势头。
    *   当 $ROCR100_t = 100$ 时，表示当前价格 $P_t$ 等于 $N$ 个周期前的价格 $P_{t-N}$，价格在该周期内没有净变化。

*   **与其他变动率指标的区别**:
    TA-LIB中定义了多种变动率指标，ROCR100是其中一种，以100为基准。为了清晰起见，此处列出TA-LIB中的相关定义：
    *   `MOM = (price - prevPrice)` (动量)
    *   `ROC = ((price/prevPrice)-1)*100` (变动率，以0为中心)
    *   `ROCP = (price-prevPrice)/prevPrice` (百分比变动率，通常乘以100后使用，以0为中心)
    *   `ROCR = (price/prevPrice)` (变动率比率，以1为中心)
    *   `ROCR100 = (price/prevPrice)*100` (变动率比率，以100为中心)
    ROCR100始终为正值，并且围绕100波动。

【因子信息结束】===============================================================