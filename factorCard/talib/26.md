【因子信息开始】===============================================================

【因子编号和名称】

因子编号: CMO_001
因子中文名称: 钱德动量摆动指标 (Chande Momentum Oscillator, CMO)

【1. 因子名称详情】

因子CMO_001: 钱德动量摆动指标 (Chande Momentum Oscillator, CMO)。该指标由图莎尔·钱德（Tushar Chande）提出，通过计算指定周期内上涨日动量和与下跌日动量和的差值，再除以总动量和，来衡量价格动量的相对强度。

【2. 核心公式】

给定时间序列数据（通常为收盘价）$P_0, P_1, \ldots, P_t, \ldots$，参数为时间窗口 $N$。

1.  计算每日价格变化:
    $ \Delta P_t = P_t - P_{t-1} $

2.  分离上涨日动量 ($U_t$) 和下跌日动量 ($D_t$):
    $ U_t = \begin{cases} \Delta P_t & \text{if } \Delta P_t > 0 \\ 0 & \text{if } \Delta P_t \leq 0 \end{cases} $
    $ D_t = \begin{cases} |\Delta P_t| & \text{if } \Delta P_t < 0 \\ 0 & \text{if } \Delta P_t \geq 0 \end{cases} $
    (注意：TALIB源码中，当 `tempValue2 < 0` 时，`prevLoss -= tempValue2` 相当于 `prevLoss += abs(tempValue2)`）

3.  计算 $N$ 周期内上涨动量之和 ($S_{U,t}$) 和下跌动量之和 ($S_{D,t}$)。这里采用的是类似于RSI中的Wilder平滑方法：
    *   对于第一个计算周期（即第 $N$ 个数据点之后），初始的 $S_{U,N}$ 和 $S_{D,N}$ 是过去 $N$ 个周期 $U_i$ 和 $D_i$ 的简单算术平均值（或者直接求和，根据后续公式调整，TALIB中使用的是求和后取平均作为Wilder平滑的初始值）。
        $ \text{AvgGain}_N = \frac{1}{N} \sum_{i=1}^{N} U_i $
        $ \text{AvgLoss}_N = \frac{1}{N} \sum_{i=1}^{N} D_i $
        其中 $i$ 从 $t-(N-1)$ 到 $t$。

    *   对于后续周期 $t > N$:
        $ \text{AvgGain}_t = \frac{(\text{AvgGain}_{t-1} \times (N-1)) + U_t}{N} $
        $ \text{AvgLoss}_t = \frac{(\text{AvgLoss}_{t-1} \times (N-1)) + D_t}{N} $

4.  计算CMO:
    $ \text{CMO}_t = 100 \times \frac{\text{AvgGain}_t - \text{AvgLoss}_t}{\text{AvgGain}_t + \text{AvgLoss}_t} $
    如果 $ (\text{AvgGain}_t + \text{AvgLoss}_t) = 0 $，则 $ \text{CMO}_t = 0 $。

【3. 变量定义】

*   $P_t$: 在时间点 $t$ 的价格（通常为收盘价）。
*   $P_{t-1}$: 在时间点 $t-1$ 的价格。
*   $\Delta P_t$: 时间点 $t$ 相对于时间点 $t-1$ 的价格变化。
*   $N$: 计算CMO的时间窗口期（例如14天）。
*   $U_t$: 在时间点 $t$ 的价格上涨幅度。如果价格下跌或不变，则为0。
*   $D_t$: 在时间点 $t$ 的价格下跌幅度（取绝对值）。如果价格上涨或不变，则为0。
*   $\text{AvgGain}_t$: 在时间点 $t$ 的 $N$ 周期上涨幅度的平滑移动平均。
*   $\text{AvgLoss}_t$: 在时间点 $t$ 的 $N$ 周期下跌幅度的平滑移动平均。
*   $\text{CMO}_t$: 在时间点 $t$ 的钱德动量摆动指标值。

【4. 函数与方法说明】

*   **价格变化 ($\Delta P_t$)**: 当前周期的价格减去前一个周期的价格。
    $ \Delta P_t = P_t - P_{t-1} $
*   **上涨幅度 ($U_t$)**: 如果价格变化为正，则取该价格变化值；否则为0。
    $ U_t = \max(0, \Delta P_t) $
*   **下跌幅度 ($D_t$)**: 如果价格变化为负，则取该价格变化绝对值；否则为0。
    $ D_t = \max(0, - \Delta P_t) $
*   **平滑移动平均 (Wilder's Smoothing)**:
    这是一种特殊的指数移动平均 (EMA)，其平滑常数 $\alpha = 1/N$。
    计算公式：
    对于第一个值 (周期 $N$ 结束时):
    $ \text{AvgValue}_N = \frac{1}{N} \sum_{i=1}^{N} \text{Value}_i $
    对于后续值 (周期 $t > N$):
    $ \text{AvgValue}_t = \frac{(\text{AvgValue}_{t-1} \times (N-1)) + \text{Value}_t}{N} $
    这里 `Value` 分别指代 $U_t$ 和 $D_t$。

【5. 计算步骤】

1.  **数据准备**: 准备至少 $N$ 个周期的价格数据 $P_t$。
2.  **计算价格变化**: 从第二个数据点开始，计算每个周期的价格变化 $\Delta P_t = P_t - P_{t-1}$。
3.  **分离上涨和下跌幅度**: 对每个周期的 $\Delta P_t$：
    *   如果 $\Delta P_t > 0$，则 $U_t = \Delta P_t$，$D_t = 0$。
    *   如果 $\Delta P_t < 0$，则 $U_t = 0$，$D_t = |\Delta P_t|$。
    *   如果 $\Delta P_t = 0$，则 $U_t = 0$，$D_t = 0$。
4.  **计算初始平滑平均上涨/下跌幅度**:
    *   计算从第1个价格变化到第 $N$ 个价格变化（即对应数据点 $P_1$ 至 $P_N$ 相对于 $P_0$ 至 $P_{N-1}$ 的变化）的 $U_i$ 和 $D_i$ 值。
    *   $\text{AvgGain}_N = \frac{1}{N} \sum_{i=1}^{N} U_i$ (这里的 $U_i$ 指的是第 $i$ 个价格变化产生的上涨幅度)
    *   $\text{AvgLoss}_N = \frac{1}{N} \sum_{i=1}^{N} D_i$ (这里的 $D_i$ 指的是第 $i$ 个价格变化产生的下跌幅度)
    *   这个初始值对应于第 $N$ 个价格点计算完毕后（即有 $N$ 个 $\Delta P$ 值后）。
5.  **迭代计算后续平滑平均上涨/下跌幅度**: 对于第 $N+1$ 个价格变化及之后（即对应数据点 $P_{N+1}$ 及之后）:
    *   获取当前的 $U_t$ 和 $D_t$。
    *   $\text{AvgGain}_t = \frac{(\text{AvgGain}_{t-1} \times (N-1)) + U_t}{N}$
    *   $\text{AvgLoss}_t = \frac{(\text{AvgLoss}_{t-1} \times (N-1)) + D_t}{N}$
6.  **计算CMO值**: 从第 $N$ 个周期（即已获得 $\text{AvgGain}_N$ 和 $\text{AvgLoss}_N$）开始，对每个周期 $t$:
    *   计算分母: $S_t = \text{AvgGain}_t + \text{AvgLoss}_t$
    *   计算分子: $M_t = \text{AvgGain}_t - \text{AvgLoss}_t$
    *   如果 $S_t \neq 0$，则 $\text{CMO}_t = 100 \times \frac{M_t}{S_t}$。
    *   如果 $S_t = 0$，则 $\text{CMO}_t = 0$。

第一个有效的CMO值将在处理完 $N$ 个价格变化（即拥有 $P_0, \ldots, P_N$ 共 $N+1$ 个价格点）后产生。

【6. 备注与参数说明】

*   **时间窗口期 (`optInTimePeriod`, 即 $N$)**: 这是CMO指标的核心参数。常见值为14或20。较短的周期使指标对价格变化更敏感，较长的周期则更平滑。TALIB中该参数范围是2到100000，默认值为14。
*   **输入数据 (`inReal`)**: 通常使用资产的收盘价序列作为输入。
*   **输出范围**: CMO指标的值在 -100 和 +100 之间波动。接近+100表示强势超买，接近-100表示强势超卖。0值附近表示市场动量均衡。
*   **与RSI的比较**: CMO的计算逻辑与相对强弱指数（RSI）非常相似，特别是在平滑上涨和下跌幅度方面。主要区别在于最终的计算公式：
    *   RSI = $100 \times \frac{\text{AvgGain}}{\text{AvgGain} + \text{AvgLoss}}$ (或 $100 - \frac{100}{1 + RS}$ where $RS = \frac{\text{AvgGain}}{\text{AvgLoss}}$)
    *   CMO = $100 \times \frac{\text{AvgGain} - \text{AvgLoss}}{\text{AvgGain} + \text{AvgLoss}}$
*   **Metastock兼容性与不稳定期**: TALIB源码中包含了针对Metastock兼容性的特殊处理逻辑，这主要影响序列最初几个值的计算。同时，TALIB也考虑了指标输出的“不稳定期”（unstable period），即EMA类指标在初期由于历史数据不足可能产生不够可靠的值。本卡片描述的是基于Wilder平滑的经典计算思想。在实际应用中，为了获得稳定的指标值，需要足够的历史数据进行预热。
*   **周期为1的特殊情况**: 如果时间周期 $N=1$，TALIB的实现会直接复制输入数据到输出（这通常不用于实际分析，CMO的意义在于周期性比较）。本卡片主要描述 $N \ge 2$ 的情况。

【因子信息结束】===============================================================