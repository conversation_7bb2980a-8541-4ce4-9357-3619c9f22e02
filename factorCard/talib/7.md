【因子信息开始】===============================================================

【因子编号和名称】

因子1: 平均趋向指数 (Average Directional Movement Index, ADX)

【1. 因子名称详情】

平均趋向指数 (Average Directional Movement Index)，简称ADX，是一种用于衡量趋势强度的技术分析指标，由J<PERSON> Welles <PERSON> Jr. 开发。它不指示趋势方向，仅指示趋势的强度。ADX的计算基于正趋向指标 (+DI) 和负趋向指标 (-DI)，而这两者又源于趋向变动 (+DM 和 -DM) 和真实波幅 (TR)。

【2. 核心公式】

ADX的计算涉及多个步骤和中间指标：

1.  **真实波幅 (True Range, $TR_t$)**:
    $TR_t = \max(H_t - L_t, |H_t - C_{t-1}|, |L_t - C_{t-1}|)$

2.  **趋向变动 (+DM 和 -DM)**:
    $UpMove_t = H_t - H_{t-1}$
    $DownMove_t = L_{t-1} - L_t$

    $+DM_{1,t} = \begin{cases} UpMove_t & \text{if } UpMove_t > DownMove_t \text{ and } UpMove_t > 0 \\ 0 & \text{otherwise} \end{cases}$
    $-DM_{1,t} = \begin{cases} DownMove_t & \text{if } DownMove_t > UpMove_t \text{ and } DownMove_t > 0 \\ 0 & \text{otherwise} \end{cases}$
    注意：如果 $UpMove_t$ 和 $DownMove_t$ 相等或都小于等于0，则 $+DM_{1,t}$ 和 $-DM_{1,t}$ 均为0。

3.  **N周期平滑趋向变动 ($+DM_{N,t}$, $-DM_{N,t}$) 和N周期平滑真实波幅 ($TR_{N,t}$)**:
    这些指标使用 Wilder 平滑法（一种特殊的指数移动平均）。对于周期N：
    *   初始值 (例如，第一个 $TR_N$):
        $TR_{N,\text{initial}} = \sum_{i=1}^{N} TR_{1,i}$ (前N个单周期TR的总和)
    *   后续值:
        $CurrentSmoothedValue_t = PreviousSmoothedValue_{t-1} - \frac{PreviousSmoothedValue_{t-1}}{N} + CurrentSinglePeriodValue_t$
        这等价于：
        $CurrentSmoothedValue_t = \frac{(N-1) \times PreviousSmoothedValue_{t-1} + CurrentSinglePeriodValue_t}{N}$

    所以，
    $+DM_{N,t} = \text{WilderSmooth}(+DM_{1,t}, N)$
    $-DM_{N,t} = \text{WilderSmooth}(-DM_{1,t}, N)$
    $TR_{N,t} = \text{WilderSmooth}(TR_{1,t}, N)$

4.  **N周期正趋向指标 ($+DI_{N,t}$) 和N周期负趋向指标 ($-DI_{N,t}$)**:
    $+DI_{N,t} = 100 \times \frac{+DM_{N,t}}{TR_{N,t}}$
    $-DI_{N,t} = 100 \times \frac{-DM_{N,t}}{TR_{N,t}}$
    (如果 $TR_{N,t} = 0$，则 $+DI_{N,t}$ 和 $-DI_{N,t}$ 通常设为0)

5.  **N周期趋向指数 (Directional Movement Index, $DX_t$)**:
    $DX_t = 100 \times \frac{|+DI_{N,t} - (-DI_{N,t})|}{+DI_{N,t} + (-DI_{N,t})}$
    (如果分母 $(+DI_{N,t} + (-DI_{N,t})) = 0$，则 $DX_t$ 通常设为0)

6.  **N周期平均趋向指数 (Average Directional Movement Index, $ADX_{N,t}$)**:
    ADX是DX的Wilder平滑移动平均。
    *   初始值 (第一个 $ADX_N$):
        $ADX_{N,\text{initial}} = \frac{1}{N} \sum_{i=1}^{N} DX_{i}$ (前N个DX值的简单移动平均)
    *   后续值:
        $ADX_{N,t} = \frac{ADX_{N,t-1} \times (N-1) + DX_t}{N}$

【3. 变量定义】

*   $t$: 当前时间周期（例如，天、小时）。
*   $H_t$: 当前周期的最高价。
*   $L_t$: 当前周期的最低价。
*   $C_t$: 当前周期的收盘价。
*   $H_{t-1}$: 上一交易周期的最高价。
*   $L_{t-1}$: 上一交易周期的最低价。
*   $C_{t-1}$: 上一交易周期的收盘价。
*   $N$: 计算ADX所选定的时间周期长度（窗口期），通常为14。
*   $TR_{1,t}$: 第 $t$ 期的单周期真实波幅。
*   $UpMove_t$: 当期最高价与前期最高价之差。
*   $DownMove_t$: 前期最低价与当期最低价之差。
*   $+DM_{1,t}$: 第 $t$ 期的单周期正趋向变动。
*   $-DM_{1,t}$: 第 $t$ 期的单周期负趋向变动。
*   $TR_{N,t}$: 第 $t$ 期的N周期平滑真实波幅。
*   $+DM_{N,t}$: 第 $t$ 期的N周期平滑正趋向变动。
*   $-DM_{N,t}$: 第 $t$ 期的N周期平滑负趋向变动。
*   $+DI_{N,t}$: 第 $t$ 期的N周期正趋向指标。
*   $-DI_{N,t}$: 第 $t$ 期的N周期负趋向指标。
*   $DX_t$: 第 $t$ 期的N周期趋向指数。
*   $ADX_{N,t}$: 第 $t$ 期的N周期平均趋向指数。

【4. 函数与方法说明】

*   **$\max(a, b, c, ...)$**: 返回括号内所有参数中的最大值。
*   **$|x|$ (std_fabs(x) in C code)**: 返回 $x$ 的绝对值。
*   **Wilder平滑法 (Wilder's Smoothing Method / Modified Moving Average)**:
    这是J. Welles Wilder Jr. 在其技术指标中常用的一种平滑方法，实质上是一种特殊参数的指数移动平均 (EMA)。对于周期 $N$，其计算方法如下：
    1.  **初始值**: 第一个平滑值通常由前 $N$ 个原始数据的简单算术平均或总和（如TR, DM的初始值）得到。
        *   对于 $TR_N, +DM_N, -DM_N$ 的第一个值：计算前 $N$ 个 $TR_1, +DM_1, -DM_1$ 的简单和。
        *   对于 $ADX_N$ 的第一个值：计算前 $N$ 个 $DX$ 值的简单算术平均。
    2.  **后续值**: 后续的平滑值通过以下递归公式计算：
        $SmoothedValue_t = \frac{(N-1) \times SmoothedValue_{t-1} + CurrentRawValue_t}{N}$
        或者表示为:
        $SmoothedValue_t = SmoothedValue_{t-1} - \frac{SmoothedValue_{t-1}}{N} + CurrentRawValue_t$
        其中 $CurrentRawValue_t$ 是当前周期的原始值（如 $TR_{1,t}$, $+DM_{1,t}$, $-DM_{1,t}$, 或 $DX_t$）。

【5. 计算步骤】

假设我们有时间序列的最高价 ($H$)、最低价 ($L$) 和收盘价 ($C$)，以及参数 $N$ (例如, N=14)。

1.  **数据准备**:
    需要至少 $2N-1$ 个周期的 $H, L, C$ 数据才能计算出第一个 $ADX_N$ 值。更准确地说，为了得到稳定的ADX值，需要更多的数据进行预热。第一个ADX值通常在第 $2N-1$ 个数据点之后（即从时间 $t=0$ 到 $t=2N-2$ 的数据）。

2.  **计算单周期真实波幅 ($TR_1$) 和单周期趋向变动 ($+DM_1, -DM_1$)**:
    从第二个数据点开始 ($t=1$, 因为需要 $C_{t-1}, H_{t-1}, L_{t-1}$):
    *   $TR_{1,t} = \max(H_t - L_t, |H_t - C_{t-1}|, |L_t - C_{t-1}|)$
    *   $UpMove_t = H_t - H_{t-1}$
    *   $DownMove_t = L_{t-1} - L_t$
    *   $+DM_{1,t} = (UpMove_t > DownMove_t \land UpMove_t > 0) ? UpMove_t : 0$
    *   $-DM_{1,t} = (DownMove_t > UpMove_t \land DownMove_t > 0) ? DownMove_t : 0$

3.  **计算N周期平滑真实波幅 ($TR_N$)、N周期平滑正趋向变动 ($+DM_N$) 和N周期平滑负趋向变动 ($-DM_N$)**:
    *   **初始值**:
        *   第一个 $+DM_N$ (例如在第 $N$ 期计算得到)：$+DM_{N,N} = \sum_{i=1}^{N} +DM_{1,i}$
        *   第一个 $-DM_N$ (例如在第 $N$ 期计算得到)：$-DM_{N,N} = \sum_{i=1}^{N} -DM_{1,i}$
        *   第一个 $TR_N$ (例如在第 $N$ 期计算得到)：$TR_{N,N} = \sum_{i=1}^{N} TR_{1,i}$
    *   **后续值 (对于 $t > N$)**:
        *   $+DM_{N,t} = \frac{(N-1) \times (+DM_{N,t-1}) + (+DM_{1,t})}{N}$
        *   $-DM_{N,t} = \frac{(N-1) \times (-DM_{N,t-1}) + (-DM_{1,t})}{N}$
        *   $TR_{N,t} = \frac{(N-1) \times TR_{N,t-1} + TR_{1,t}}{N}$

4.  **计算N周期趋向指标 ($+DI_N, -DI_N$)**:
    对于 $t \ge N$ (即从第 $N$ 期开始，因为此时才有 $TR_N, +DM_N, -DM_N$ 的值):
    *   如果 $TR_{N,t} == 0$，则 $+DI_{N,t}=0$ 和 $-DI_{N,t}=0$。
    *   否则，$+DI_{N,t} = 100 \times \frac{+DM_{N,t}}{TR_{N,t}}$
    *   否则，$-DI_{N,t} = 100 \times \frac{-DM_{N,t}}{TR_{N,t}}$

5.  **计算N周期趋向指数 ($DX$)**:
    对于 $t \ge N$:
    *   $SumDI_t = +DI_{N,t} + (-DI_{N,t})$
    *   如果 $SumDI_t == 0$，则 $DX_t = 0$。
    *   否则，$DX_t = 100 \times \frac{|+DI_{N,t} - (-DI_{N,t})|}{SumDI_t}$
    第一个 $DX$ 值出现在第 $N$ 期。

6.  **计算N周期平均趋向指数 ($ADX_N$)**:
    *   **初始值**:
        *   第一个 $ADX_N$ (例如在第 $2N-1$ 期计算得到，因为它需要前 $N$ 个 $DX$ 值，而第一个 $DX$ 值在第 $N$ 期出现):
          $ADX_{N, 2N-1} = \frac{1}{N} \sum_{i=N}^{2N-1} DX_i$ (对第 $N$ 期到第 $2N-1$ 期的 $N$ 个 $DX$ 值取简单算术平均)
    *   **后续值 (对于 $t > 2N-1$)**:
        *   $ADX_{N,t} = \frac{ADX_{N,t-1} \times (N-1) + DX_t}{N}$

【6. 备注与参数说明】

*   **参数选择**:
    *   时间周期 $N$: 默认值为14。较短的周期（如7）对价格变动更敏感，产生更多波动；较长的周期（如28）更平滑，产生的信号较少。
*   **数据预处理**:
    *   确保输入的价格数据（高、低、收盘价）是准确且无缺失的。
    *   ADX指标的计算需要一段“预热期”。第一个有效的ADX值通常在 $2N-1$ 个数据点之后产生。为获得稳定的结果，通常需要比这个最短长度更多的数据。例如，对于N=14，至少需要 $2 \times 14 - 1 = 27$ 个数据点来计算第一个ADX，但为了让平滑稳定，可能需要更多（例如 TA-LIB 中 `Lookback` 函数返回 `(2 * N) + UnstablePeriod - 1`，其中 `UnstablePeriod` 对于ADX一般也为 $N-1$，所以是 $3N-2$）。
*   **除零处理**:
    *   当 $TR_{N,t}$ 为零时，该周期的 $+DI_{N,t}$ 和 $-DI_{N,t}$ 通常被设为0。
    *   当 $(+DI_{N,t} + (-DI_{N,t}))$ 为零时，$DX_t$ 通常被设为0。这可以防止除以零的错误，并表示此时没有明确的趋向强度。
*   **应用解释**:
    *   ADX值介于0到100之间。
    *   通常认为ADX值高于20或25表明存在趋势（上升或下降趋势由+DI和-DI的相对位置决定）。
    *   ADX值低于20表明市场处于盘整或趋势较弱。
    *   ADX曲线的斜率可以帮助判断趋势是在增强还是减弱。ADX上升表示趋势增强，ADX下降表示趋势减弱。

【因子信息结束】===============================================================