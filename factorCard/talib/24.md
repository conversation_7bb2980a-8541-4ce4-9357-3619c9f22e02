【因子信息开始】===============================================================

【因子编号和名称】

因子编号: CCI001: 商品通道指标 (Commodity Channel Index, CCI)

【1. 因子名称详情】

因子1: 商品通道指标 (Commodity Channel Index, CCI)

【2. 核心公式】

CCI指标衡量当前价格水平相对于其在给定时间窗口内的平均价格水平的偏离程度。其计算公式如下：

$CCI_t = \frac{TP_t - SMA(TP, N)_t}{0.015 \times MAD(TP, N)_t}$

其中：
*   $TP_t$ 是当前周期 $t$ 的典型价格。
*   $SMA(TP, N)_t$ 是典型价格 $TP$ 在过去 $N$ 个周期（包括当前周期）的简单移动平均值。
*   $MAD(TP, N)_t$ 是典型价格 $TP$ 在过去 $N$ 个周期（包括当前周期）相对于其简单移动平均值的平均绝对偏差。
*   $N$ 是计算周期。
*   $0.015$ 是一个常数，用于调整CCI值的范围，使其大部分波动落在-100到+100之间。

【3. 变量定义】

*   $H_t$: 时间周期 $t$ 的最高价 (High Price)。
*   $L_t$: 时间周期 $t$ 的最低价 (Low Price)。
*   $C_t$: 时间周期 $t$ 的收盘价 (Close Price)。
*   $TP_t$: 时间周期 $t$ 的典型价格 (Typical Price)。
*   $N$: 计算CCI的时间窗口期 (Time Period)，例如14天。
*   $SMA(TP, N)_t$: 在时间周期 $t$，过去 $N$ 个周期的典型价格 $TP$ 的简单移动平均值。
*   $MAD(TP, N)_t$: 在时间周期 $t$，过去 $N$ 个周期的典型价格 $TP$ 对其简单移动平均值的平均绝对偏差。
*   $CCI_t$: 时间周期 $t$ 的商品通道指标值。

【4. 函数与方法说明】

1.  **典型价格 (Typical Price, TP)**:
    每个周期的典型价格是该周期最高价、最低价和收盘价的算术平均值。
    $TP_t = \frac{H_t + L_t + C_t}{3}$

2.  **简单移动平均 (Simple Moving Average, SMA)**:
    给定一个时间序列 $X$（在此因子中为 $TP$）和周期 $N$，在时刻 $t$ 的简单移动平均值是该时刻及之前 $N-1$ 个时刻的 $X$值的算术平均。
    $SMA(X, N)_t = \frac{\sum_{i=0}^{N-1} X_{t-i}}{N} = \frac{X_t + X_{t-1} + \dots + X_{t-N+1}}{N}$

3.  **平均绝对偏差 (Mean Absolute Deviation, MAD)**:
    给定一个时间序列 $X$（在此因子中为 $TP$）、周期 $N$，以及该序列在对应周期的简单移动平均值 $SMA(X, N)_t$，在时刻 $t$ 的平均绝对偏差是每个 $X_{t-i}$ 值与其平均值 $SMA(X, N)_t$ 之间差的绝对值的算术平均。
    $MAD(X, N)_t = \frac{\sum_{i=0}^{N-1} |X_{t-i} - SMA(X, N)_t|}{N}$
    其中 $X_{t-i}$ 代表从当前周期 $t$ 回溯 $i$ 个周期的典型价格值（即 $TP_{t-i}$），$SMA(X, N)_t$ 是这些 $N$ 个典型价格的简单移动平均值。

【5. 计算步骤】

假设我们有连续的时间序列数据，包括每个周期的最高价 ($H$)、最低价 ($L$) 和收盘价 ($C$)。设定计算周期为 $N$。

1.  **数据准备**:
    获取至少 $N$ 个连续周期的 $H_t, L_t, C_t$ 数据。

2.  **计算典型价格 (TP)**:
    对于每个周期 $t$ (从最早的周期开始，直到当前周期)，计算典型价格：
    $TP_t = \frac{H_t + L_t + C_t}{3}$
    这将生成一个典型价格的时间序列 $TP_1, TP_2, \dots, TP_k$。

3.  **计算典型价格的简单移动平均 (SMA_TP)**:
    对于需要计算CCI值的每个周期 $t$ (从第 $N$ 个有效数据点开始)：
    取当前周期 $t$ 及之前的 $N-1$ 个周期的典型价格，共 $N$ 个 $TP$ 值。
    $SMA(TP, N)_t = \frac{TP_t + TP_{t-1} + \dots + TP_{t-N+1}}{N}$

4.  **计算典型价格的平均绝对偏差 (MAD_TP)**:
    对于与步骤3中相同的 $N$ 个典型价格 ($TP_t, TP_{t-1}, \dots, TP_{t-N+1}$) 和它们对应的简单移动平均值 $SMA(TP, N)_t$：
    首先计算每个 $TP_{t-i}$与 $SMA(TP, N)_t$ 的差的绝对值: $|TP_{t-i} - SMA(TP, N)_t|$。
    然后计算这些绝对差值的平均值：
    $MAD(TP, N)_t = \frac{\sum_{i=0}^{N-1} |TP_{t-i} - SMA(TP, N)_t|}{N}$

5.  **计算CCI值**:
    使用当前周期的典型价格 $TP_t$、步骤3计算的 $SMA(TP, N)_t$ 和步骤4计算的 $MAD(TP, N)_t$，代入核心公式：
    $CCI_t = \frac{TP_t - SMA(TP, N)_t}{0.015 \times MAD(TP, N)_t}$
    如果 $MAD(TP, N)_t$ 为0（即所有 $N$ 个 $TP$ 值都相等），则 $0.015 \times MAD(TP, N)_t$ 也为0。在这种情况下，如果 $TP_t - SMA(TP, N)_t$ 也为0，则 $CCI_t$ 设为0。如果分子不为0而分母为0，则CCI值在数学上是未定义的，但实际中通常也会处理为0或一个极大的值，或者沿用前值，Talib源码中若 $TP_t - SMA(TP, N)_t$ 或 $MAD(TP, N)_t$ （通过`tempReal2`间接判断）为0，则最终CCI输出为0。

6.  **迭代**:
    对于时间序列中的下一个周期 $t+1$，重复步骤2（如果尚未计算 $TP_{t+1}$）、步骤3、步骤4和步骤5，使用最新的 $N$ 个周期的 $TP$ 数据进行滚动计算。

【6. 备注与参数说明】

*   **时间窗口期 (N, `optInTimePeriod`)**: 这是CCI计算中最重要的参数。常见的默认值是14或20。较短的周期会使CCI对价格变化更敏感，产生更多波动；较长的周期会使CCI更平滑，反应较慢。源码中 `optInTimePeriod` 的默认值为14，有效范围为2到100000。
*   **常数0.015**: 这个常数由CCI指标的发明者Donald Lambert引入，目的是使大约70%到80%的CCI值落在-100到+100的范围内。这个常数确保了CCI值具有一定的统计可比性。
*   **数据预处理**: 计算第一个CCI值需要至少 $N$ 个周期的价格数据。因此，CCI序列的起始点会晚于原始价格序列的起始点（具体晚 $N-1$ 个周期）。
*   **典型价格**: 虽然CCI的原始定义使用 (High + Low + Close) / 3 作为典型价格，但也可以根据需要使用其他价格定义，如仅用收盘价或 (High + Low) / 2 等，但这将不再是标准的CCI。源码中严格遵循 (High + Low + Close) / 3。
*   **应用**: CCI常用于识别市场的超买/超卖状态（通常以+100为超买线，-100为超卖线）以及价格趋势的强度和潜在的背离信号。

【因子信息结束】===============================================================