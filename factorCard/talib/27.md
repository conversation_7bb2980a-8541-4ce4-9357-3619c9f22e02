【因子信息开始】===============================================================

【因子编号和名称】

因子编号: FC001: 皮尔逊相关系数 (Pearson Correlation Coefficient, CORREL)

【1. 因子名称详情】

因子1: 皮尔逊相关系数 (Pearson Correlation Coefficient, CORREL)

【2. 核心公式】

皮尔逊相关系数 `r` 的计算公式如下：

$r = \frac{n(\sum_{i=1}^{n} X_i Y_i) - (\sum_{i=1}^{n} X_i)(\sum_{i=1}^{n} Y_i)}{\sqrt{[n(\sum_{i=1}^{n} X_i^2) - (\sum_{i=1}^{n} X_i)^2][n(\sum_{i=1}^{n} Y_i^2) - (\sum_{i=1}^{n} Y_i)^2]}}$

其中：
*   分子表示为：$n \times \text{sumXY} - \text{sumX} \times \text{sumY}$
*   分母中的第一部分（X的离差平方和相关项）表示为：$n \times \text{sumX2} - (\text{sumX})^2$
*   分母中的第二部分（Y的离差平方和相关项）表示为：$n \times \text{sumY2} - (\text{sumY})^2$

在实际计算中，为了避免重复除以 `n`，TALib的实现采用了如下等价形式进行计算：
令：
$Num = (\sum_{i=1}^{n} X_i Y_i) - \frac{(\sum_{i=1}^{n} X_i)(\sum_{i=1}^{n} Y_i)}{n}$
$Den_X = (\sum_{i=1}^{n} X_i^2) - \frac{(\sum_{i=1}^{n} X_i)^2}{n}$
$Den_Y = (\sum_{i=1}^{n} Y_i^2) - \frac{(\sum_{i=1}^{n} Y_i)^2}{n}$

则 $r = \frac{Num}{\sqrt{Den_X \times Den_Y}}$

如果 $Den_X \times Den_Y \le 0$，则 $r = 0$。

【3. 变量定义】

*   $r$: 计算得到的皮尔逊相关系数。
*   $n$: 计算窗口期内的样本数量（时间周期长度）。
*   $X_i$: 在时间点 $i$ 的第一个输入序列（例如，序列A的价格）的值。
*   $Y_i$: 在时间点 $i$ 的第二个输入序列（例如，序列B的价格）的值。
*   $\sum_{i=1}^{n} X_i$: 窗口期内第一个输入序列 $X$ 的所有值之和 (代码中对应 `sumX`)。
*   $\sum_{i=1}^{n} Y_i$: 窗口期内第二个输入序列 $Y$ 的所有值之和 (代码中对应 `sumY`)。
*   $\sum_{i=1}^{n} X_i Y_i$: 窗口期内第一个输入序列 $X$ 和第二个输入序列 $Y$ 对应值乘积之和 (代码中对应 `sumXY`)。
*   $\sum_{i=1}^{n} X_i^2$: 窗口期内第一个输入序列 $X$ 所有值平方之和 (代码中对应 `sumX2`)。
*   $\sum_{i=1}^{n} Y_i^2$: 窗口期内第二个输入序列 $Y$ 所有值平方之和 (代码中对应 `sumY2`)。

【4. 函数与方法说明】

*   `sqrt(value)`: 平方根函数，计算 `value` 的算术平方根。
*   $\sum$: 求和符号，表示对指定范围内的一系列数值进行累加。

【5. 计算步骤】

该因子计算两个时间序列在给定窗口期内的皮尔逊相关系数。采用滚动窗口的方式进行计算。

1.  **数据准备**:
    *   获取两个输入时间序列，分别称为 `inReal0` (设为 $X$) 和 `inReal1` (设为 $Y$)。
    *   指定计算周期 `optInTimePeriod` (设为 $n$)。

2.  **初始化 (计算第一个窗口期的相关系数)**:
    *   确定计算的起始点。由于需要 $n$ 个数据点来计算第一个相关系数，实际计算从输入序列的第 $n-1$ 个索引（0-based）开始。
    *   选取从 `trailingIdx` (初始为 `startIdx - (n-1)`) 到 `startIdx` (通常是 `n-1`) 的 $n$ 个数据点作为第一个计算窗口。
    *   在该窗口内，累积计算以下五个值：
        *   `sumX`: $X$序列 $n$ 个数据点之和。
        *   `sumY`: $Y$序列 $n$ 个数据点之和。
        *   `sumX2`: $X$序列 $n$ 个数据点各自平方之和。
        *   `sumY2`: $Y$序列 $n$ 个数据点各自平方之和。
        *   `sumXY`: $X$序列和$Y$序列对应数据点乘积之和。
    *   计算分子项 `num_val = sumXY - (sumX * sumY) / n`。
    *   计算分母的两个组成部分：
        *   `den_x_val = sumX2 - (sumX * sumX) / n`
        *   `den_y_val = sumY2 - (sumY * sumY) / n`
    *   计算分母的平方根项 `den_sqrt_val = den_x_val * den_y_val`。
    *   如果 `den_sqrt_val` 大于 0 (TALib中通过 `!TA_IS_ZERO_OR_NEG(tempReal)` 判断，即 `tempReal > 0`):
        *   相关系数 $r = \text{num_val} / \text{sqrt}(\text{den_sqrt_val})$。
    *   否则 (即 `den_sqrt_val <= 0`，意味着其中一个序列在窗口内没有变化或变化很小导致方差为0或负数，后者理论上不应发生于平方和的计算):
        *   相关系数 $r = 0.0$。
    *   记录第一个计算出的相关系数。
    *   保存当前窗口最旧的数据点 $X_{trailing}$ 和 $Y_{trailing}$ (即 `inReal0[trailingIdx]` 和 `inReal1[trailingIdx]`)，并将 `trailingIdx` 后移一位。

3.  **滚动计算 (计算后续窗口期的相关系数)**:
    *   对于从 `startIdx + 1` 到 `endIdx` 的每一个后续时间点 `today`:
        *   **移除旧值**: 从各项累积和中减去上一个窗口最旧的数据点 ($X_{trailing}, Y_{trailing}$) 的贡献：
            *   `sumX = sumX - X_trailing`
            *   `sumX2 = sumX2 - X_trailing * X_trailing`
            *   `sumY = sumY - Y_trailing`
            *   `sumY2 = sumY2 - Y_trailing * Y_trailing`
            *   `sumXY = sumXY - X_trailing * Y_trailing`
        *   **加入新值**: 将当前时间点 `today` 的新数据点 ($X_{current} = \text{inReal0}[\text{today}]$, $Y_{current} = \text{inReal1}[\text{today}]$) 的贡献加入到各项累积和中：
            *   `sumX = sumX + X_current`
            *   `sumX2 = sumX2 + X_current * X_current`
            *   `sumY = sumY + Y_current`
            *   `sumY2 = sumY2 + Y_current * Y_current`
            *   `sumXY = sumXY + X_current * Y_current`
        *   重复步骤2中的公式计算分子项 `num_val`、分母组成部分 `den_x_val`, `den_y_val` 和 `den_sqrt_val`。
        *   根据 `den_sqrt_val` 的值，按步骤2的方法计算并记录当前窗口的相关系数 $r$。
        *   更新 $X_{trailing}$ 和 $Y_{trailing}$ 为当前窗口最旧的数据点 (即 `inReal0[trailingIdx]` 和 `inReal1[trailingIdx]`)，并将 `trailingIdx` 后移一位。

4.  **输出**:
    *   输出从 `startIdx` 到 `endIdx` 计算得到的皮尔逊相关系数序列。输出序列的第一个有效值对应输入序列的第 `optInTimePeriod - 1` 个数据点。

【6. 备注与参数说明】

*   **参数 `optInTimePeriod`**: 这是计算相关系数的窗口期长度 $n$。默认值为30。
    *   取值范围：1 到 100000。
    *   较短的周期对近期数据的相关性变化更敏感，但结果可能波动较大。
    *   较长的周期结果更平滑，但对相关性的短期变化反应较慢。
*   **数据对齐**: 计算前应确保两个输入时间序列是对齐的，即在同一时间点上有对应的数据。
*   **输出延迟**: 由于计算需要一个完整的窗口期数据，输出序列的第一个有效值将比输入序列晚 `optInTimePeriod - 1` 个周期。
*   **分母为零或负的处理**: 当计算出的分母因子 $Den_X \times Den_Y$ (即代码中的 `tempReal`) 为零或负数时，表明至少一个序列在窗口期内的方差为零（即所有值相同）或出现计算问题。在这种情况下，TALib将相关系数设为0.0，以避免除以零或对负数开平方根的错误。
*   **因子范围**: 皮尔逊相关系数的理论取值范围为 \[-1, 1\]。
    *   +1 表示完全正线性相关。
    *   -1 表示完全负线性相关。
    *   0 表示无线性相关（可能存在非线性相关）。
*   **实现版本**: 源码中包含 `double` 和 `float` 类型的输入数据处理版本，核心逻辑相同。这里描述的是其通用思想。

【因子信息结束】===============================================================