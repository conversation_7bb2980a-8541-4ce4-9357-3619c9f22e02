【因子信息开始】===============================================================

【因子编号和名称】

因子编号: 001: 威廉姆斯百分比范围指标 (Williams' Percent Range, %R)

【1. 因子名称详情】

因子001: 威廉姆斯百分比范围指标 (Williams' Percent Range, %R)，也常简称为威廉%R指标。它是一个动量震荡指标，用于衡量当前收盘价在过去一段时间内的价格区间中所处的位置。

【2. 核心公式】

对于给定的时间周期 $P$，在时间点 $t$ 的威廉姆斯百分比范围（%R）计算公式如下：

\[ \%R_t = \frac{H_{P,t} - C_t}{H_{P,t} - L_{P,t}} \times (-100) \]

其中：
*   如果 $H_{P,t} - L_{P,t} = 0$，则 $\%R_t = 0$。（这是源码中的特殊处理情况）

【3. 变量定义】

*   $C_t$: 当期（第 $t$ 期）的收盘价。
*   $H_{P,t}$: 截至当期（第 $t$ 期）的过去 $P$ 个周期内的最高价（包含当期）。
*   $L_{P,t}$: 截至当期（第 $t$ 期）的过去 $P$ 个周期内的最低价（包含当期）。
*   $P$: 计算指标所选定的时间周期长度（例如，14天）。

【4. 函数与方法说明】

*   **周期内最高价 ($H_{P,t}$)**:
    计算方法：在当前时间点 $t$ 及之前的 $P-1$ 个时间点（共 $P$ 个数据点）的最高价序列 $(High_{t-P+1}, High_{t-P+2}, \dots, High_t)$ 中选取最大值。
    \[ H_{P,t} = \max(High_{t-P+1}, High_{t-P+2}, \dots, High_t) \]

*   **周期内最低价 ($L_{P,t}$)**:
    计算方法：在当前时间点 $t$ 及之前的 $P-1$ 个时间点（共 $P$ 个数据点）的最低价序列 $(Low_{t-P+1}, Low_{t-P+2}, \dots, Low_t)$ 中选取最小值。
    \[ L_{P,t} = \min(Low_{t-P+1}, Low_{t-P+2}, \dots, Low_t) \]

【5. 计算步骤】

1.  **数据准备**: 收盘价 ($Close$)、最高价 ($High$)、最低价 ($Low$) 的时间序列数据。
2.  **参数设定**: 确定计算周期 $P$ (例如， $P=14$ )。
3.  **初始化**:
    *   计算将从第 $P$ 个数据点开始，因为需要至少 $P$ 期的数据来形成第一个完整的计算窗口。
    *   对于每个计算点 `today`（从数据序列的第 `P-1` 个索引开始，如果索引从0开始），其对应的回溯窗口为 `[today - P + 1, today]`。

4.  **迭代计算**: 对于数据序列中从第 $P$ 期开始的每一个时间点 $t$：
    a.  **确定当前计算窗口**: 选定从时间点 $t-P+1$ 到 $t$ 的 $P$ 个连续周期。
    b.  **计算周期内最高价 ($H_{P,t}$)**: 在当前 $P$周期窗口内，找到所有K线的最高价中的最大值。
        *   遍历从 $t-P+1$ 到 $t$ 的每一根K线。
        *   记录下这些K线最高价中的最大值作为 $H_{P,t}$。
    c.  **计算周期内最低价 ($L_{P,t}$)**: 在当前 $P$周期窗口内，找到所有K线的最低价中的最小值。
        *   遍历从 $t-P+1$ 到 $t$ 的每一根K线。
        *   记录下这些K线最低价中的最小值作为 $L_{P,t}$。
    d.  **获取当期收盘价 ($C_t$)**: 即时间点 $t$ 的收盘价。
    e.  **计算价格范围差 ($RangeDiff_t$)**:
        \[ RangeDiff_t = H_{P,t} - L_{P,t} \]
    f.  **计算%R值**:
        *   **情况1**: 如果 $RangeDiff_t = 0$ (即周期内最高价等于最低价，价格无波动)，则 $\%R_t = 0$。
        *   **情况2**: 如果 $RangeDiff_t \neq 0$，则
            \[ \%R_t = \frac{H_{P,t} - C_t}{RangeDiff_t} \times (-100) \]

5.  **输出**: 得到一系列的 $\%R_t$ 值。

【6. 备注与参数说明】

*   **时间周期 ($P$)**:
    *   该参数定义了回顾期长度。常用的默认值为14。
    *   较短的周期（如5或7）会使指标对价格变化更敏感，产生更多的交易信号，但可能假信号也较多。
    *   较长的周期（如20或30）会使指标更平滑，减少噪音，但可能滞后于市场变化。
*   **数值范围**:
    *   %R指标的值域通常在 -100 到 0 之间。
    *   值接近 0 (例如 -20 到 0) 通常被认为是超买区域，表明当前收盘价接近或处于近期价格范围的顶部。
    *   值接近 -100 (例如 -80 到 -100) 通常被认为是超卖区域，表明当前收盘价接近或处于近期价格范围的底部。
*   **与随机指标 (Stochastic Oscillator) 的关系**:
    *   Williams' %R 与随机指标中的 %K 线非常相似。实际上，%R 可以看作是未平滑的快速随机指标 (%K Fast) 的一个版本，只是其刻度是颠倒并乘以-100。
*   **数据预处理**: 通常不需要特殊的数据预处理，但应确保输入的最高价、最低价和收盘价数据是有效的（例如，最高价 >= 收盘价，最高价 >= 最低价，收盘价 >= 最低价）。
*   **关于除零处理**: 源码中明确指出，当周期内的最高价等于最低价时 ($H_{P,t} - L_{P,t} = 0$)，%R的值被设定为0。其他实现中可能会有不同的处理方式，例如返回前一个值、设定为-50或NaN。

【因子信息结束】===============================================================