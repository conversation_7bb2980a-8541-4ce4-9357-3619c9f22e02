【因子信息开始】===============================================================

【因子编号和名称】

因子编号: VOL_001
因子中文名称: 正量指标 (Positive Volume Index, PVI)

【1. 因子名称详情】

因子VOL_001: 正量指标 (Positive Volume Index, PVI)。该指标旨在衡量当成交量增加时资金流动的强度，通常用于判断市场是否由“非专业”或“未获内幕消息”的交易者驱动。

【2. 核心公式】

设 $PVI_t$ 为在时间点 $t$ 的正量指标值。
设 $C_t$ 为在时间点 $t$ 的收盘价。
设 $V_t$ 为在时间点 $t$ 的成交量。

PVI的计算是迭代的，初始值通常设定为100或1000。我们这里以100为例。
令 $PVI_{initial} = 100$ 作为计算序列中有效PVI值的前一个基准值。

对于时间序列中的第一个数据点（$t=0$），其 $C_0$ 和 $V_0$ 将作为计算第二个数据点（$t=1$）PVI的基础。
第一个计算出的PVI值将是 $PVI_1$。

对于 $t \ge 1$:
如果 $V_t > V_{t-1}$ （当日成交量大于昨日成交量）：
\[ PVI_t = PVI_{t-1} + PVI_{t-1} \times \frac{C_t - C_{t-1}}{C_{t-1}} \]
如果 $V_t \le V_{t-1}$ （当日成交量小于或等于昨日成交量）：
\[ PVI_t = PVI_{t-1} \]
其中，$PVI_{t-1}$ 代表上一期的PVI值。对于计算 $PVI_1$ 时，$PVI_0$ 即为 $PVI_{initial}$。

【3. 变量定义】

*   $PVI_t$: 时刻 $t$ 的正量指标值。
*   $PVI_{t-1}$: 时刻 $t-1$ 的正量指标值（或对于第一个计算点，为初始基准值）。
*   $C_t$: 时刻 $t$ 的资产收盘价。
*   $C_{t-1}$: 时刻 $t-1$ 的资产收盘价。
*   $V_t$: 时刻 $t$ 的资产成交量。
*   $V_{t-1}$: 时刻 $t-1$ 的资产成交量。
*   $PVI_{initial}$: PVI计算的初始基准值，通常设为100。

【4. 函数与方法说明】

*   **价格百分比变化率**: 公式 $\frac{C_t - C_{t-1}}{C_{t-1}}$ 用于计算从时刻 $t-1$ 到时刻 $t$ 资产收盘价的百分比变化。这是当成交量增加时，用于调整PVI值的核心驱动因素。

【5. 计算步骤】

1.  **数据准备**:
    获取资产的每日收盘价时间序列 $C = \{C_0, C_1, C_2, \ldots, C_N\}$ 和对应的每日成交量时间序列 $V = \{V_0, V_1, V_2, \ldots, V_N\}$。

2.  **初始化PVI**:
    设置一个变量 `previous_PVI`，其初始值为 $PVI_{initial}$ (例如, 100)。这个值将作为计算第一个有效PVI值（即对应输入数据中 $C_1, V_1$ 的 $PVI_1$）时的 $PVI_{t-1}$。

3.  **迭代计算PVI**:
    从数据序列的第二个时间点开始（即 $t=1$），依次执行到最后一个时间点 $N$：
    a.  获取当期数据：收盘价 $C_t$ 和成交量 $V_t$。
    b.  获取前期数据：收盘价 $C_{t-1}$ 和成交量 $V_{t-1}$。
    c.  **比较成交量**:
        *   如果 $V_t > V_{t-1}$:
            i.  计算价格变化率: $PriceChangeRatio = \frac{C_t - C_{t-1}}{C_{t-1}}$。 (注意：若 $C_{t-1}$ 为0，需特殊处理，通常此种情况在金融数据中罕见，或意味着数据问题。为避免除零错误，可将 $PriceChangeRatio$ 视为0或跳过该期）。
            ii. 计算当期PVI: $PVI_t = \text{previous\_PVI} + (\text{previous\_PVI} \times PriceChangeRatio)$。
        *   如果 $V_t \le V_{t-1}$:
            i.  当期PVI维持不变: $PVI_t = \text{previous\_PVI}$。
    d.  **更新previous\_PVI**: 将当前计算得到的 $PVI_t$ 赋值给 `previous_PVI`，供下一期计算使用: $\text{previous\_PVI} = PVI_t$。
    e.  记录 $PVI_t$：将 $PVI_t$ 作为第 $t$ 期的正量指标输出值。输出序列的第一个值对应输入序列的第二个数据点（即 $PVI_1$ 对应 $C_1, V_1$）。

【6. 备注与参数说明】

*   **初始值 ($PVI_{initial}$)**: PVI的起始值（如100或1000）是一个任意设定的基准。改变这个初始值会等比例地放大或缩小整个PVI序列的值，但不会改变其形状或其所指示的趋势。
*   **计算起点**: 由于PVI的计算依赖于前一期的数据（收盘价和成交量），PVI序列的第一个有效值是针对输入数据序列中的第二个数据点计算得出的。第一个数据点（$C_0, V_0$）仅作为计算的基准。
*   **数据质量**: 输入的收盘价和成交量数据需要准确且对应。如果前期收盘价 $C_{t-1}$ 为零或极小，可能导致价格变化率计算出现问题（如除以零错误或结果极端化），实际应用中应注意数据清洗和异常值处理。
*   **解释**: PVI上升通常表明在成交量放大的日子里价格也随之上涨，这可能被解读为买方力量增强，或者是由消息驱动的“群众”参与度提高。PVI下降或横盘则表明成交量放大时价格下跌，或成交量未放大。将PVI与其移动平均线结合使用是常见的分析方法。
*   该因子没有可调窗口期参数，其计算逻辑是固定的滚动累积。

【因子信息结束】===============================================================