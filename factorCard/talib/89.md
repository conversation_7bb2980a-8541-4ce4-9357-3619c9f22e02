【因子信息开始】===============================================================

【因子编号和名称】

因子编号: (此为示例，请根据您的系统分配) FACTOR_NATR_001: 归一化平均真实波幅 (Normalized Average True Range, NATR)

【1. 因子名称详情】

归一化平均真实波幅 (Normalized Average True Range, NATR)。该指标是平均真实波幅 (ATR) 的归一化版本，通过将ATR表示为当日收盘价的百分比，从而可以在不同价格水平的证券之间或同一证券的不同历史时期进行波动性比较。

【2. 核心公式】

该因子的计算主要分为三个步骤：真实波幅 (TR)，平均真实波幅 (ATR)，以及最终的归一化平均真实波幅 (NATR)。

1.  **真实波幅 (True Range, TR)**
    对于每一个交易周期 `t`（例如一天），真实波幅 `TR_t` 定义为以下三者中的最大值：
    *   当期最高价与当期最低价之差： \( H_t - L_t \)
    *   当期最高价与前期收盘价之差的绝对值： \( |H_t - C_{t-1}| \)
    *   当期最低价与前期收盘价之差的绝对值： \( |L_t - C_{t-1}| \)
    数学表达式为：
    \\[ TR_t = \max(H_t - L_t, |H_t - C_{t-1}|, |L_t - C_{t-1}|) \\]

2.  **平均真实波幅 (Average True Range, ATR)**
    ATR 是对真实波幅 TR 进行平滑移动平均得到的。通常采用威尔德平滑法 (Wilder's Smoothing)。
    *   对于第一个 ATR 值 (即计算周期为 N 的 ATR，在时间点 N)：
        \\[ ATR_N = \frac{1}{N} \sum_{i=1}^{N} TR_i \\]
        （注意：Talib的实现中，第一个ATR值是前N个TR值的简单平均数，计算发生在完成第N个TR之后。）
    *   对于后续的 ATR 值 (对于 \( t > N \) )：
        \\[ ATR_t = \frac{(N-1) \times ATR_{t-1} + TR_t}{N} \\]

3.  **归一化平均真实波幅 (Normalized Average True Range, NATR)**
    NATR 是将 ATR 值表示为当日收盘价的百分比：
    \\[ NATR_t = \frac{ATR_t}{C_t} \times 100 \\]
    如果 \( C_t = 0 \)，则 \( NATR_t \) 通常设为 0 或不计算。

【3. 变量定义】

*   \( t \): 当前时间周期（例如，天、小时）。
*   \( H_t \): 时间周期 \( t \) 的最高价。
*   \( L_t \): 时间周期 \( t \) 的最低价。
*   \( C_t \): 时间周期 \( t \) 的收盘价。
*   \( C_{t-1} \): 时间周期 \( t-1 \) (即前一个周期) 的收盘价。
*   \( TR_t \): 时间周期 \( t \) 的真实波幅。
*   \( N \): 计算平均真实波幅 (ATR) 的时间窗口长度（参数）。
*   \( ATR_t \): 时间周期 \( t \) 的平均真实波幅。
*   \( ATR_{t-1} \): 时间周期 \( t-1 \) 的平均真实波幅。
*   \( NATR_t \): 时间周期 \( t \) 的归一化平均真实波幅。

【4. 函数与方法说明】

*   **绝对值函数 \( |x| \):** 返回 \(x\) 的非负值。如果 \(x \ge 0\)，则 \(|x| = x\)；如果 \(x < 0\)，则 \(|x| = -x\)。
*   **最大值函数 \( \max(a, b, c) \):** 返回 \(a, b, c\) 中数值最大的那个。
*   **简单移动平均 (Simple Moving Average, SMA):** 用于计算初始的ATR值。对于一个序列 \(X\) 和周期 \(N\)，其在时间点 \(k\) (通常是序列中的第N个点，包含之前N-1个点的数据) 的SMA值为：
    \\[ SMA(X, N)_k = \frac{X_k + X_{k-1} + \dots + X_{k-N+1}}{N} \\]
    具体到ATR的初始化，是取序列TR的前N个值进行简单平均。
*   **威尔德平滑法 (Wilder's Smoothing / Wilder's Moving Average):**
    这是一种特殊的指数移动平均 (EMA)，其平滑因子 \(\alpha = 1/N\)。公式为：
    \\[ \text{Current EMA} = \frac{\text{Current Data Point} + (N-1) \times \text{Previous EMA}}{N} \\]
    或者等价地：
    \\[ \text{Current EMA} = \text{Previous EMA} + \frac{1}{N} (\text{Current Data Point} - \text{Previous EMA}) \\]
    在ATR计算中，"Current Data Point" 是当前的 \(TR_t\)，"Previous EMA" 是 \(ATR_{t-1}\)。

【5. 计算步骤】

1.  **数据准备:**
    获取至少 \(N + \text{lookback}_{\text{initial}}\) 个周期的历史最高价 (\(H\))、最低价 (\(L\)) 和收盘价 (\(C\)) 序列。 \( \text{lookback}_{\text{initial}} \) 通常为1，因为TR的计算需要前一天的收盘价。
    设定参数 \(N\) (例如，常用的14)。

2.  **计算真实波幅 (TR) 序列:**
    从第二个数据点开始（假设为时间点 \(t=1\)，则需要 \(C_0\)），对于每个时间点 \(t\)，计算 \(TR_t\):
    \( TR_t = \max(H_t - L_t, |H_t - C_{t-1}|, |L_t - C_{t-1}|) \)
    这将得到一个 \(TR\) 值序列。第一个有效的 \(TR\) 值对应原始数据的第二个时间点。

3.  **计算平均真实波幅 (ATR) 序列:**
    a.  **初始化 ATR:**
        取计算得到的 \(TR\) 序列中的前 \(N\) 个值。计算这 \(N\) 个 \(TR\) 值的简单算术平均数。这个结果是第 \(N\) 个 \(TR\) 值所对应的 \(ATR\) 值 (即 \(ATR_N\))。
        例如，如果 \(TR_1, TR_2, \dots, TR_N\) 是前 \(N\) 个真实波幅值，则：
        \\[ ATR_N = \frac{TR_1 + TR_2 + \dots + TR_N}{N} \\]
        （注意：在原始数据时间序列中，这个 \(ATR_N\) 对应的是第 \(N+1\) 个数据点，因为第一个 \(TR\) 值本身就滞后了一个周期。）

    b.  **后续 ATR 计算:**
        对于第 \(N+1\) 个 \(TR\) 值 (即 \(TR_{N+1}\)) 及其之后的所有 \(TR_t\)，使用威尔德平滑法计算 \(ATR_t\):
        \\[ ATR_t = \frac{(N-1) \times ATR_{t-1} + TR_t}{N} \quad \text{for } t > N \\]
        其中 \(ATR_{t-1}\) 是上一个周期计算得到的ATR值。

4.  **计算归一化平均真实波幅 (NATR) 序列:**
    对于每一个已经计算出 \(ATR_t\) 值的周期 \(t\)，并且对应的收盘价 \(C_t\) 不为零：
    \\[ NATR_t = \left( \frac{ATR_t}{C_t} \right) \times 100 \\]
    如果 \(C_t = 0\)，则 \(NATR_t\) 通常被设置为0。

    *注意：* 由于ATR的计算依赖于前一个ATR，因此ATR序列（以及NATR序列）的初始值会经历一个“不稳定时期”。Talib内部会处理这个不稳定周期，通常会跳过这些初始的不稳定值，使得输出的第一个NATR值是基于已经稳定下来的ATR计算的。这个不稳定的长度通常与参数N相关。如果应用中需要与Talib完全一致的起始点，需要考虑Talib中 `TA_GLOBALS_UNSTABLE_PERIOD` 的影响，它定义了在输出有效数据前，需要预热计算的周期数。对于NATR，这个不稳周期长度等于其参数`optInTimePeriod`。

【6. 备注与参数说明】

*   **参数 `optInTimePeriod` (N):** 这是计算ATR时采用的时间周期长度。常用的默认值是14。较短的周期使NATR对近期价格波动更敏感，而较长的周期则提供更平滑但滞后性更强的结果。
*   **数据预处理:**
    *   输入数据（高、低、收盘价）应确保无缺失值或进行了合理插补。
    *   在计算NATR时，如果当期收盘价 \(C_t\) 为0，会导致除零错误。源码中已处理这种情况，将结果赋为0.0。
*   **不稳健性周期:** 类似于指数移动平均线，ATR（及其衍生的NATR）在计算初期会有一个不稳定的阶段。其值需要经过一定数量的周期才能趋于稳定。Talib库的实现中，会通过一个内部机制（`TA_GLOBALS_UNSTABLE_PERIOD`）来确定并跳过这些不稳定的初始值，确保输出结果的可靠性。对于NATR，这个不稳定周期长度与`optInTimePeriod`相同。因此，NATR的第一个有效输出值会比原始输入数据序列的起始点晚 `optInTimePeriod + (optInTimePeriod - 1)` 个周期（`optInTimePeriod-1` 来自TR的计算，然后是ATR的`optInTimePeriod`个周期稳定）。
*   **应用场景:** NATR通过将波动率标准化为价格的百分比，使得在不同价格水平的股票之间，或者同一股票在价格大幅变动后的不同时期，其波动性具有可比性。高NATR值通常表示市场波动较大，低NATR值则表示市场波动较小。

【因子信息结束】===============================================================