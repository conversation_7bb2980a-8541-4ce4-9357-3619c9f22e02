【因子信息开始】===============================================================

【因子编号和名称】

因子编号: MACDFIX03: 固定的移动平均收敛散离差柱状图 (Fixed Moving Average Convergence Divergence Histogram, MACDFIX Hist)

【1. 因子名称详情】

因子完整名称：固定的移动平均收敛散离差柱状图 (Fixed Moving Average Convergence Divergence Histogram)
英文简称：MACDFIX Hist

该因子是MACD指标中的柱状图（Histogram或Bar），它表示MACD线 (MACDFIX01) 与信号线 (MACDFIX02) 之间的差值。

【2. 核心公式】

MACD柱状图的计算公式为：
$MACDHistogram_t = MACDLine_t - SignalLine_t$

其中，$MACDLine_t$ 和 $SignalLine_t$ 的计算方式如下 ($t$为时间索引)：
$MACDLine_t = EMA(P, N_{fast})_t - EMA(P, N_{slow})_t$
$SignalLine_t = EMA(MACDLine, N_{signal})_t$
$EMA(X, N)_t = X_t \cdot K + EMA(X, N)_{t-1} \cdot (1-K)$
平滑系数 $K = \frac{2}{N+1}$

【3. 变量定义】

*   $P_t$: 在时刻 $t$ 的资产价格（通常使用收盘价）。
*   $N_{fast}$: 用于计算MACD线的短期EMA周期，固定为12。
*   $N_{slow}$: 用于计算MACD线的长期EMA周期，固定为26。
*   $N_{signal}$: 信号线EMA的周期，是一个可配置参数，默认值为9。
*   $MACDLine_t$: 在时刻 $t$ 的MACD线值。
*   $SignalLine_t$: 在时刻 $t$ 的信号线值。
*   $MACDHistogram_t$: 在时刻 $t$ 的MACD柱状图值。
*   $EMA(X, N)_t$: 时间序列 $X$ 在时刻 $t$ 的 $N$ 周期指数移动平均值。

【4. 函数与方法说明】

*   **指数移动平均 (EMA - Exponential Moving Average)**:
    指数移动平均是一种加权移动平均，它将较大的权重赋予最近的数据点。
    给定一个时间序列数据 $X = (X_0, X_1, ..., X_t, ...)$ 和一个周期参数 $N$。
    1.  计算平滑系数 $K$:
        $K = \frac{2}{N+1}$
    2.  EMA的计算是递归的。第一个EMA值 $EMA_0$ 通常使用序列的第一个数据点 $X_0$进行初始化：
        $EMA_0 = X_0$
    3.  对于后续的每个时间点 $t > 0$:
        $EMA_t = X_t \cdot K + EMA_{t-1} \cdot (1-K)$
    此方法确保EMA序列从输入序列的第一个点开始就有定义。然而，EMA值通常需要大约 $N$ 个周期的数据才能较好地反映其目标平滑特性，早期值受初始化的影响较大。

【5. 计算步骤】

1.  **计算依赖因子：MACD线 ($MACDLine$)**:
    a.  **数据准备**: 获取资产价格的时间序列数据 $P = (P_0, P_1, P_2, ..., P_T)$。
    b.  **计算短期EMA ($EMA_{fast}$)** (周期 $N_{fast}=12$):
        *   $K_{fast} = 2 / (12+1) = 2/13$。
        *   初始化: $(EMA_{fast})_0 = P_0$。
        *   对于 $t = 1, 2, ..., T$: $(EMA_{fast})_t = P_t \cdot K_{fast} + (EMA_{fast})_{t-1} \cdot (1-K_{fast})$。
    c.  **计算长期EMA ($EMA_{slow}$)** (周期 $N_{slow}=26$):
        *   $K_{slow} = 2 / (26+1) = 2/27$。
        *   初始化: $(EMA_{slow})_0 = P_0$。
        *   对于 $t = 1, 2, ..., T$: $(EMA_{slow})_t = P_t \cdot K_{slow} + (EMA_{slow})_{t-1} \cdot (1-K_{slow})$。
    d.  **生成MACD线序列**:
        *   对于每个时间点 $t = 0, 1, ..., T$: $MACDLine_t = (EMA_{fast})_t - (EMA_{slow})_t$。
        得到时间序列 $MACDLine = (MACDLine_0, MACDLine_1, ..., MACDLine_T)$。

2.  **计算依赖因子：信号线 ($SignalLine$)**:
    a.  使用上一步生成的 $MACDLine$ 序列作为输入。
    b.  设定信号线EMA周期 $N_{signal}$ (例如，默认值为9)。
    c.  计算信号线平滑系数 $K_{signal} = \frac{2}{N_{signal}+1}$。
    d.  计算 $SignalLine$ 序列：
        *   初始化: $(SignalLine)_0 = MACDLine_0$。
        *   对于 $t = 1, 2, ..., T$: $(SignalLine)_t = MACDLine_t \cdot K_{signal} + (SignalLine)_{t-1} \cdot (1-K_{signal})$。
        得到时间序列 $SignalLine = (SignalLine_0, SignalLine_1, ..., SignalLine_T)$。

3.  **计算MACD柱状图 ($MACDHistogram$)**:
    a.  对于每个时间点 $t = 0, 1, ..., T$：
        $MACDHistogram_t = MACDLine_t - SignalLine_t$

【6. 备注与参数说明】

*   **参数 `optInSignalPeriod` ($N_{signal}$)**: 这个参数间接影响柱状图，因为它决定了信号线的计算。默认值为9。
*   **固定周期**: 构成MACD线的EMA周期 (12和26) 是固定的。
*   **数据窗口期**: MACD柱状图的计算依赖于MACD线和信号线，两者都需要时间稳定。因此，柱状图的值也需要较长的时间窗口（通常是 $N_{slow} + N_{signal}$ 个周期左右）才能稳定并变得有意义。
*   **用途**: MACD柱状图可以更直观地显示MACD线和信号线之间的差距和变化速率，常用于判断趋势的强度和潜在的背离信号。正值表示MACD线在信号线上方，负值反之。柱状图从正转负或从负转正，以及柱状图的峰谷，常被视为交易信号。



【7. 关联因子补充说明】

【关联因子信息开始】===============================================================

【因子编号和名称】

因子编号: MACDFIX01: 固定的移动平均收敛散离差线 (Fixed Moving Average Convergence Divergence Line, MACDFIX Line)

【1. 因子名称详情】

因子完整名称：固定的移动平均收敛散离差线 (Fixed Moving Average Convergence Divergence Line)
英文简称：MACDFIX Line

该因子是固定周期参数（短期12周期，长期26周期）的MACD指标中的MACD线。它反映了资产价格短期和长期趋势之间的差异。

【2. 核心公式】

MACD线的计算基于价格的两个指数移动平均线的差值：
1.  短期指数移动平均 ($EMA_{fast}$)，周期 $N_{fast}$ 固定为12。
2.  长期指数移动平均 ($EMA_{slow}$)，周期 $N_{slow}$ 固定为26。

数学表达式如下：
$EMA(P, N)_t = P_t \cdot K + EMA(P, N)_{t-1} \cdot (1-K)$
其中，平滑系数 $K = \frac{2}{N+1}$

$MACDLine_t = EMA(P, N_{fast})_t - EMA(P, N_{slow})_t$

【3. 变量定义】

*   $P_t$: 在时刻 $t$ 的资产价格（通常使用收盘价）。
*   $N_{fast}$: 短期EMA的周期，固定为12。
*   $N_{slow}$: 长期EMA的周期，固定为26。
*   $K$: EMA计算中的平滑系数。 对于 $N_{fast}=12$, $K_{fast} = \frac{2}{12+1} = \frac{2}{13}$. 对于 $N_{slow}=26$, $K_{slow} = \frac{2}{26+1} = \frac{2}{27}$.
*   $EMA(P, N)_t$: 价格序列 $P$ 在时刻 $t$ 的 $N$ 周期指数移动平均值。
*   $MACDLine_t$: 在时刻 $t$ 的MACD线值。

【4. 函数与方法说明】

*   **指数移动平均 (EMA - Exponential Moving Average)**:
    指数移动平均是一种加权移动平均，它将较大的权重赋予最近的数据点。
    给定一个时间序列数据 $X = (X_0, X_1, ..., X_t, ...)$ 和一个周期参数 $N$。
    1.  计算平滑系数 $K$:
        $K = \frac{2}{N+1}$
    2.  EMA的计算是递归的。第一个EMA值 $EMA_0$ 通常使用序列的第一个数据点 $X_0$进行初始化：
        $EMA_0 = X_0$
    3.  对于后续的每个时间点 $t > 0$:
        $EMA_t = X_t \cdot K + EMA_{t-1} \cdot (1-K)$
        （这个公式也可以表达为 $EMA_t = EMA_{t-1} + K \cdot (X_t - EMA_{t-1})$）
    此方法确保EMA序列从输入序列的第一个点开始就有定义。然而，EMA值通常需要大约 $N$ 个周期的数据才能较好地反映其目标平滑特性，早期值受初始化的影响较大。

【5. 计算步骤】

1.  **数据准备**:
    获取资产价格的时间序列数据 $P = (P_0, P_1, P_2, ..., P_T)$，通常为每日收盘价。

2.  **计算短期EMA ($EMA_{fast}$)**:
    a.  设定周期 $N_{fast} = 12$。
    b.  计算短期平滑系数 $K_{fast} = \frac{2}{12+1} = \frac{2}{13}$。
    c.  计算 $EMA_{fast}$ 序列：
        *   初始化: $(EMA_{fast})_0 = P_0$。
        *   对于 $t = 1, 2, ..., T$: $(EMA_{fast})_t = P_t \cdot K_{fast} + (EMA_{fast})_{t-1} \cdot (1-K_{fast})$。

3.  **计算长期EMA ($EMA_{slow}$)**:
    a.  设定周期 $N_{slow} = 26$。
    b.  计算长期平滑系数 $K_{slow} = \frac{2}{26+1} = \frac{2}{27}$。
    c.  计算 $EMA_{slow}$ 序列：
        *   初始化: $(EMA_{slow})_0 = P_0$。
        *   对于 $t = 1, 2, ..., T$: $(EMA_{slow})_t = P_t \cdot K_{slow} + (EMA_{slow})_{t-1} \cdot (1-K_{slow})$。

4.  **计算MACD线 ($MACDLine$)**:
    a.  对于每个时间点 $t = 0, 1, ..., T$：
        $MACDLine_t = (EMA_{fast})_t - (EMA_{slow})_t$。

【6. 备注与参数说明】

*   **输入数据**: 因子计算通常使用收盘价，但也可以应用于其他价格数据如开盘价、最高价、最低价或典型价格。
*   **固定周期**: 此因子的特点是其EMA周期固定为12和26，这是MACD指标最经典的参数设置。
*   **数据预处理**: 无特殊预处理要求，但输入价格序列应无缺失值。
*   **数据窗口期**: 理论上，从第一个数据点即可开始计算。但在实际应用中，MACD线的值需要一段时间（通常至少等于长期EMA周期 $N_{slow}$）才能稳定并变得有意义。因此，序列头部的因子值可能不适用于决策。
*   **用途**: MACD线用于识别趋势方向和潜在的转折点。它常与信号线结合使用。

【关联因子信息结束】===============================================================

【关联因子信息开始】===============================================================

【因子编号和名称】

因子编号: MACDFIX02: 固定的移动平均收敛散离差信号线 (Fixed Moving Average Convergence Divergence Signal Line, MACDFIX Signal)

【1. 因子名称详情】

因子完整名称：固定的移动平均收敛散离差信号线 (Fixed Moving Average Convergence Divergence Signal Line)
英文简称：MACDFIX Signal

该因子是MACD指标中的信号线（Signal Line），它是对MACDFIX01（即MACD线）进行指数移动平均得到的。默认平滑周期为9。

【2. 核心公式】

信号线的计算是对MACD线进行指数移动平均：
$SignalLine_t = EMA(MACDLine, N_{signal})_t$

其中，$MACDLine_u$的计算方式如下（$u$为时间索引）：
$MACDLine_u = EMA(P, N_{fast})_u - EMA(P, N_{slow})_u$
$EMA(X, N)_t = X_t \cdot K + EMA(X, N)_{t-1} \cdot (1-K)$
平滑系数 $K = \frac{2}{N+1}$

【3. 变量定义】

*   $P_t$: 在时刻 $t$ 的资产价格（通常使用收盘价）。
*   $N_{fast}$: 用于计算MACD线的短期EMA周期，固定为12。
*   $N_{slow}$: 用于计算MACD线的长期EMA周期，固定为26。
*   $MACDLine_t$: 在时刻 $t$ 的MACD线值。
*   $N_{signal}$: 信号线EMA的周期，是一个可配置参数，默认值为9。
*   $K_{signal}$: 信号线EMA计算中的平滑系数, $K_{signal} = \frac{2}{N_{signal}+1}$。
*   $EMA(X, N)_t$: 时间序列 $X$ 在时刻 $t$ 的 $N$ 周期指数移动平均值。
*   $SignalLine_t$: 在时刻 $t$ 的信号线值。

【4. 函数与方法说明】

*   **指数移动平均 (EMA - Exponential Moving Average)**:
    指数移动平均是一种加权移动平均，它将较大的权重赋予最近的数据点。
    给定一个时间序列数据 $X = (X_0, X_1, ..., X_t, ...)$ 和一个周期参数 $N$。
    1.  计算平滑系数 $K$:
        $K = \frac{2}{N+1}$
    2.  EMA的计算是递归的。第一个EMA值 $EMA_0$ 通常使用序列的第一个数据点 $X_0$进行初始化：
        $EMA_0 = X_0$
    3.  对于后续的每个时间点 $t > 0$:
        $EMA_t = X_t \cdot K + EMA_{t-1} \cdot (1-K)$
    此方法确保EMA序列从输入序列的第一个点开始就有定义。然而，EMA值通常需要大约 $N$ 个周期的数据才能较好地反映其目标平滑特性，早期值受初始化的影响较大。

【5. 计算步骤】

1.  **计算依赖因子：MACD线 ($MACDLine$)**:
    a.  **数据准备**: 获取资产价格的时间序列数据 $P = (P_0, P_1, P_2, ..., P_T)$。
    b.  **计算短期EMA ($EMA_{fast}$)** (周期 $N_{fast}=12$):
        *   $K_{fast} = 2 / (12+1) = 2/13$。
        *   初始化: $(EMA_{fast})_0 = P_0$。
        *   对于 $t = 1, 2, ..., T$: $(EMA_{fast})_t = P_t \cdot K_{fast} + (EMA_{fast})_{t-1} \cdot (1-K_{fast})$。
    c.  **计算长期EMA ($EMA_{slow}$)** (周期 $N_{slow}=26$):
        *   $K_{slow} = 2 / (26+1) = 2/27$。
        *   初始化: $(EMA_{slow})_0 = P_0$。
        *   对于 $t = 1, 2, ..., T$: $(EMA_{slow})_t = P_t \cdot K_{slow} + (EMA_{slow})_{t-1} \cdot (1-K_{slow})$。
    d.  **生成MACD线序列**:
        *   对于每个时间点 $t = 0, 1, ..., T$: $MACDLine_t = (EMA_{fast})_t - (EMA_{slow})_t$。
        得到时间序列 $MACDLine = (MACDLine_0, MACDLine_1, ..., MACDLine_T)$。

2.  **计算信号线 ($SignalLine$)**:
    a.  使用上一步生成的 $MACDLine$ 序列作为输入。
    b.  设定信号线EMA周期 $N_{signal}$ (例如，默认值为9)。
    c.  计算信号线平滑系数 $K_{signal} = \frac{2}{N_{signal}+1}$。
    d.  计算 $SignalLine$ 序列：
        *   初始化: $(SignalLine)_0 = MACDLine_0$。
        *   对于 $t = 1, 2, ..., T$: $(SignalLine)_t = MACDLine_t \cdot K_{signal} + (SignalLine)_{t-1} \cdot (1-K_{signal})$。

【6. 备注与参数说明】

*   **参数 `optInSignalPeriod` ($N_{signal}$)**: 这是计算信号线EMA的周期。默认值为9。可调范围通常较广（如1到100000），但实际应用中常见9。
*   **固定周期**: 构成MACD线的EMA周期 (12和26) 是固定的。
*   **数据窗口期**: 信号线的计算依赖于MACD线，而MACD线本身需要时间稳定。因此，信号线需要更长的时间窗口才能稳定并变得有意义。实际应用中，头部因子值（通常是 $N_{slow} + N_{signal}$ 个周期左右）可能不适用于决策。
*   **用途**: 信号线与MACD线的交叉点常被用作买卖信号。

【关联因子信息结束】===============================================================

【关联因子信息开始】===============================================================

【因子编号和名称】

因子编号: MACDFIX03: 固定的移动平均收敛散离差柱状图 (Fixed Moving Average Convergence Divergence Histogram, MACDFIX Hist)

【1. 因子名称详情】

因子完整名称：固定的移动平均收敛散离差柱状图 (Fixed Moving Average Convergence Divergence Histogram)
英文简称：MACDFIX Hist

该因子是MACD指标中的柱状图（Histogram或Bar），它表示MACD线 (MACDFIX01) 与信号线 (MACDFIX02) 之间的差值。

【2. 核心公式】

MACD柱状图的计算公式为：
$MACDHistogram_t = MACDLine_t - SignalLine_t$

其中，$MACDLine_t$ 和 $SignalLine_t$ 的计算方式如下 ($t$为时间索引)：
$MACDLine_t = EMA(P, N_{fast})_t - EMA(P, N_{slow})_t$
$SignalLine_t = EMA(MACDLine, N_{signal})_t$
$EMA(X, N)_t = X_t \cdot K + EMA(X, N)_{t-1} \cdot (1-K)$
平滑系数 $K = \frac{2}{N+1}$

【3. 变量定义】

*   $P_t$: 在时刻 $t$ 的资产价格（通常使用收盘价）。
*   $N_{fast}$: 用于计算MACD线的短期EMA周期，固定为12。
*   $N_{slow}$: 用于计算MACD线的长期EMA周期，固定为26。
*   $N_{signal}$: 信号线EMA的周期，是一个可配置参数，默认值为9。
*   $MACDLine_t$: 在时刻 $t$ 的MACD线值。
*   $SignalLine_t$: 在时刻 $t$ 的信号线值。
*   $MACDHistogram_t$: 在时刻 $t$ 的MACD柱状图值。
*   $EMA(X, N)_t$: 时间序列 $X$ 在时刻 $t$ 的 $N$ 周期指数移动平均值。

【4. 函数与方法说明】

*   **指数移动平均 (EMA - Exponential Moving Average)**:
    指数移动平均是一种加权移动平均，它将较大的权重赋予最近的数据点。
    给定一个时间序列数据 $X = (X_0, X_1, ..., X_t, ...)$ 和一个周期参数 $N$。
    1.  计算平滑系数 $K$:
        $K = \frac{2}{N+1}$
    2.  EMA的计算是递归的。第一个EMA值 $EMA_0$ 通常使用序列的第一个数据点 $X_0$进行初始化：
        $EMA_0 = X_0$
    3.  对于后续的每个时间点 $t > 0$:
        $EMA_t = X_t \cdot K + EMA_{t-1} \cdot (1-K)$
    此方法确保EMA序列从输入序列的第一个点开始就有定义。然而，EMA值通常需要大约 $N$ 个周期的数据才能较好地反映其目标平滑特性，早期值受初始化的影响较大。

【5. 计算步骤】

1.  **计算依赖因子：MACD线 ($MACDLine$)**:
    a.  **数据准备**: 获取资产价格的时间序列数据 $P = (P_0, P_1, P_2, ..., P_T)$。
    b.  **计算短期EMA ($EMA_{fast}$)** (周期 $N_{fast}=12$):
        *   $K_{fast} = 2 / (12+1) = 2/13$。
        *   初始化: $(EMA_{fast})_0 = P_0$。
        *   对于 $t = 1, 2, ..., T$: $(EMA_{fast})_t = P_t \cdot K_{fast} + (EMA_{fast})_{t-1} \cdot (1-K_{fast})$。
    c.  **计算长期EMA ($EMA_{slow}$)** (周期 $N_{slow}=26$):
        *   $K_{slow} = 2 / (26+1) = 2/27$。
        *   初始化: $(EMA_{slow})_0 = P_0$。
        *   对于 $t = 1, 2, ..., T$: $(EMA_{slow})_t = P_t \cdot K_{slow} + (EMA_{slow})_{t-1} \cdot (1-K_{slow})$。
    d.  **生成MACD线序列**:
        *   对于每个时间点 $t = 0, 1, ..., T$: $MACDLine_t = (EMA_{fast})_t - (EMA_{slow})_t$。
        得到时间序列 $MACDLine = (MACDLine_0, MACDLine_1, ..., MACDLine_T)$。

2.  **计算依赖因子：信号线 ($SignalLine$)**:
    a.  使用上一步生成的 $MACDLine$ 序列作为输入。
    b.  设定信号线EMA周期 $N_{signal}$ (例如，默认值为9)。
    c.  计算信号线平滑系数 $K_{signal} = \frac{2}{N_{signal}+1}$。
    d.  计算 $SignalLine$ 序列：
        *   初始化: $(SignalLine)_0 = MACDLine_0$。
        *   对于 $t = 1, 2, ..., T$: $(SignalLine)_t = MACDLine_t \cdot K_{signal} + (SignalLine)_{t-1} \cdot (1-K_{signal})$。
        得到时间序列 $SignalLine = (SignalLine_0, SignalLine_1, ..., SignalLine_T)$。

3.  **计算MACD柱状图 ($MACDHistogram$)**:
    a.  对于每个时间点 $t = 0, 1, ..., T$：
        $MACDHistogram_t = MACDLine_t - SignalLine_t$。

【6. 备注与参数说明】

*   **参数 `optInSignalPeriod` ($N_{signal}$)**: 这个参数间接影响柱状图，因为它决定了信号线的计算。默认值为9。
*   **固定周期**: 构成MACD线的EMA周期 (12和26) 是固定的。
*   **数据窗口期**: MACD柱状图的计算依赖于MACD线和信号线，两者都需要时间稳定。因此，柱状图的值也需要较长的时间窗口（通常是 $N_{slow} + N_{signal}$ 个周期左右）才能稳定并变得有意义。
*   **用途**: MACD柱状图可以更直观地显示MACD线和信号线之间的差距和变化速率，常用于判断趋势的强度和潜在的背离信号。正值表示MACD线在信号线上方，负值反之。柱状图从正转负或从负转正，以及柱状图的峰谷，常被视为交易信号。

【关联因子信息结束】===============================================================

【因子信息结束】===============================================================
