【因子信息开始】===============================================================

【因子编号和名称】

SSC001: 中点价格 (Midpoint Price, MIDPRICE_BAR)

*注：TALib中称其为Median Price (MEDPRICE)，但其计算方式是 (最高价+最低价)/2，这更接近于“中点价格”或“典型价格”的一种。为避免与统计学上的“中位数”混淆，此处采用“中点价格”的称呼，并添加后缀_BAR以强调其基于单个K线周期。*

【1. 因子名称详情】

因子SSC001: 单周期中点价格 (Midpoint Price per Bar, MIDPRICE_BAR)

【2. 核心公式】

对于任意给定的时间周期 `t`（例如，一天、一小时），该周期的中点价格计算如下：

`MIDPRICE_BAR_t = (H_t + L_t) / 2`

其中：
*   `MIDPRICE_BAR_t` 是在时间周期 `t` 的中点价格。
*   `H_t` 是在时间周期 `t` 的最高价。
*   `L_t` 是在时间周期 `t` 的最低价。

【3. 变量定义】

*   `MIDPRICE_BAR_t`: 时刻 `t` 的单周期中点价格。这是一个价格度量，表示该周期价格范围的中心点。
*   `H_t`: 时刻 `t` 内观察到的资产最高成交价格。
*   `L_t`: 时刻 `t` 内观察到的资产最低成交价格。
*   `t`: 特定的时间周期（例如，一个交易日、一个小时K线等）。

【4. 函数与方法说明】

该因子的计算不涉及复杂的特殊函数或统计方法。它主要使用基础的算术运算：
*   **加法**: 将同一时间周期的最高价和最低价相加。
*   **除法**: 将相加得到的和除以2，以得到算术平均值，即中点价格。

【5. 计算步骤】

计算单周期中点价格的步骤如下：

1.  **数据准备**:
    *   对于分析时间序列中的每一个独立的K线周期 `t`，获取该周期的最高价 `H_t` 和最低价 `L_t`。这些数据通常直接从市场行情数据中获得。

2.  **计算中点价格**:
    *   对于每一个时间周期 `t`：
        a.  取该周期的最高价 `H_t`。
        b.  取该周期的最低价 `L_t`。
        c.  进行加法运算：`Sum_t = H_t + L_t`。
        d.  进行除法运算：`MIDPRICE_BAR_t = Sum_t / 2`。
    *   该计算是逐K线进行的，当前周期的中点价格仅依赖于当前周期的最高价和最低价，不依赖于历史数据或其他因子（即，其回看期为0）。

【6. 备注与参数说明】

*   **无参数**: 该因子本身没有可调参数（如窗口期）。它直接作用于每个独立的K线数据。
*   **数据频率**: 可以应用于任何频率的数据（例如，日线、小时线、分钟线），只要每个数据点包含最高价和最低价信息。
*   **数据预处理**:
    *   输入的最高价和最低价数据应为有效数值。
    *   通常，`H_t` 应大于或等于 `L_t`。如果数据源存在 `L_t > H_t` 的异常情况，应进行数据清洗或标记。
    *   对于数据缺失的周期，无法计算该因子。
*   **应用解释**:
    *   中点价格提供了一个对单个交易周期内价格活动中心点的简单估计。
    *   它可以被视为一种简化的典型价格（Typical Price 通常是 (High + Low + Close) / 3）。
    *   它不同于跨越多个周期的中点价格（例如，N周期内的最高价和N周期内的最低价的平均值，TALib中称这类为`MIDPRICE`函数，注意区分）。此因子（TALib中的`MEDPRICE`）是针对 *单个* 周期的。
*   **实现选择**: 源码包含单精度 (`float`) 和双精度 (`double`) 输入的版本，核心逻辑完全相同。因子卡片基于双精度 (`double`) 作为输入进行描述，因为这在金融计算中更为常见且能提供更高精度，尽管对于此简单运算，精度差异可能不显著。输出统一为双精度 (`double`)。

【因子信息结束】===============================================================