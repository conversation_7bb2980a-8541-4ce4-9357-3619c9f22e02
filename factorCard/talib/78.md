【因子信息开始】===============================================================

【因子编号和名称】
因子编号: F001
因子中文名称: 历史区间中间价 (Midpoint Price, MIDPRICE)

【1. 因子名称详情】
因子F001: 历史区间中间价 (Midpoint Price, MIDPRICE)
该因子计算在过去N个周期内，最高价的最高值与最低价的最低值之间的中点。

【2. 核心公式】
给定长度为 $N$ 的计算周期，在 $t$ 时刻的中间价计算公式如下：
$$ MIDPRICE_t = \frac{HH_N(t) + LL_N(t)}{2} $$
其中：
$HH_N(t)$ 是指从时刻 $t-N+1$ 到时刻 $t$ (包含) 这 $N$ 个周期内所有最高价中的最大值。
$LL_N(t)$ 是指从时刻 $t-N+1$ 到时刻 $t$ (包含) 这 $N$ 个周期内所有最低价中的最小值。

【3. 变量定义】
*   $MIDPRICE_t$: 在 $t$ 时刻计算得到的历史区间中间价。
*   $High_i$: 在第 $i$ 个周期的最高价。
*   $Low_i$: 在第 $i$ 个周期的最低价。
*   $N$: 计算历史区间中间价的时间窗口长度（周期数）。
*   $t$: 当前计算周期的索引或时间点。
*   $HH_N(t)$: 在当前时间点 $t$ 结束的 $N$ 周期窗口内的最高价的最高值。数学表达式为：
    $$ HH_N(t) = \max(High_{t-N+1}, High_{t-N+2}, \dots, High_t) $$
*   $LL_N(t)$: 在当前时间点 $t$ 结束的 $N$ 周期窗口内的最低价的最低值。数学表达式为：
    $$ LL_N(t) = \min(Low_{t-N+1}, Low_{t-N+2}, \dots, Low_t) $$

【4. 函数与方法说明】
*   $\max(x_1, x_2, \dots, x_N)$: 求取一组数值 $x_1, x_2, \dots, x_N$ 中的最大值。
*   $\min(x_1, x_2, \dots, x_N)$: 求取一组数值 $x_1, x_2, \dots, x_N$ 中的最小值。

【5. 计算步骤】
1.  **数据准备**:
    *   获取历史数据的最高价序列 $High = (High_1, High_2, \dots, High_T)$。
    *   获取历史数据的最低价序列 $Low = (Low_1, Low_2, \dots, Low_T)$。
    *   $T$ 是数据序列的总长度。
2.  **参数设定**:
    *   确定计算周期 $N$ (例如， $N=14$ )。
3.  **初始化**:
    *   第一个可以计算出 $MIDPRICE$ 值的时刻（或索引）是 $t=N$ (假设数据从索引1开始) 或 $t=N-1$ (假设数据从索引0开始)。在此之前的周期，由于数据不足 $N$ 个，无法计算。
4.  **滚动计算**:
    对于数据序列中从第 $N$ 个周期开始的每一个周期 $t$ (或从索引 $N-1$ 开始)：
    a.  选定计算窗口：当前周期 $t$ 以及向前追溯 $N-1$ 个周期，共计 $N$ 个周期的数据。即时间窗口为 $[t-N+1, t]$。
    b.  在该窗口内，找出所有 $High_i$ (其中 $i$ 从 $t-N+1$ 到 $t$) 中的最大值，记为 $HH_N(t)$。
        *   初始时，可以将窗口内第一个周期的最高价 $High_{t-N+1}$ 设为临时的 $HH_N(t)$。
        *   然后，遍历窗口内从第二个周期 $High_{t-N+2}$ 到最后一个周期 $High_t$ 的最高价，如果发现有值大于当前的临时 $HH_N(t)$，则更新临时 $HH_N(t)$。
        *   遍历完成后，临时的 $HH_N(t)$ 即为所求的 $N$ 周期内的最高价。
    c.  在该窗口内，找出所有 $Low_i$ (其中 $i$ 从 $t-N+1$ 到 $t$) 中的最小值，记为 $LL_N(t)$。
        *   初始时，可以将窗口内第一个周期的最低价 $Low_{t-N+1}$ 设为临时的 $LL_N(t)$。
        *   然后，遍历窗口内从第二个周期 $Low_{t-N+2}$ 到最后一个周期 $Low_t$ 的最低价，如果发现有值小于当前的临时 $LL_N(t)$，则更新临时 $LL_N(t)$。
        *   遍历完成后，临时的 $LL_N(t)$ 即为所求的 $N$ 周期内的最低价。
    d.  根据公式计算 $MIDPRICE_t = \frac{HH_N(t) + LL_N(t)}{2}$。
    e.  将计算结果 $MIDPRICE_t$ 存储到输出序列中。
    f.  移动到下一个周期 $t+1$，重复步骤 4a 至 4e，直到处理完所有数据。

【6. 备注与参数说明】
*   **参数 $N$ (optInTimePeriod)**:
    *   该参数指定了计算所用的历史周期数。
    *   TALib中此参数的建议范围是 2 至 100000，默认值为14。
    *   选择较小的 $N$ 会使因子对近期价格变化更敏感，选择较大的 $N$ 则会使因子更平滑，反映更长期的趋势中点。
*   **数据有效性**:
    *   输入数据必须包含有效的最高价和最低价序列。
    *   输出序列的起始位置会因为需要 $N$ 个周期的初始数据而向后推移 $N-1$ 个周期。例如，如果输入数据有 $T$ 个点，输出的 $MIDPRICE$ 序列将有 $T - (N-1)$ 个点。
*   **与MEDPRICE的关系**:
    *   当周期 $N=1$ 时，$HH_1(t) = High_t$ 且 $LL_1(t) = Low_t$，此时 $MIDPRICE_t = \frac{High_t + Low_t}{2}$。这与单周期的中点价格 (Median Price / MEDPRICE, 即 (最高价+最低价)/2) 的计算结果相同。

【因子信息结束】===============================================================