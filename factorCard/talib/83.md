【因子信息开始】===============================================================

【因子编号和名称】

因子编号: 001-1 (示例编号-最低值索引)
因子中文名称: 周期极值位置索引 - 周期内最低值位置索引 (Min Index, MININDEX)

【1. 因子名称详情】

因子完整名称: 周期极值位置索引 - 周期内最低值位置索引 (Period Min Value Index)
该因子用于确定在过去指定时间周期（窗口）内，输入数据序列中最小值首次出现的索引（取最新出现的位置）。

【2. 核心公式】

对于每个时间点 t 和给定的计算周期 P，计算值为：

  INDEX_{min, t} = argmax { j | S_j = min_{k in [t-P+1, t]} (S_k) }

解释:
* min_{k in [t-P+1, t]} (S_k) 表示在索引从 t-P+1 到 t 的窗口内 S 的最小值。
* argmax { j | ... } 表示在所有满足条件的索引 j 中选取最大的那个索引，即在存在多个相同最小值时选取最近的（最大索引）的那个。

【3. 变量定义】

• S_i: 输入数据序列中第 i 期的取值。
• P: 计算周期，即回顾窗口的长度。
• t: 当前时间点索引，从 t=P-1 开始。
• INDEX_{min, t}: 对应时间点 t 在窗口 [t-P+1, t] 内达到最小值的最新索引。

【4. 函数与方法说明】

• 滑动窗口最小值索引搜索: 在固定长度为 P 的窗口内，通过检查当前值是否小于或等于已知的最小值，更新最小值及其索引。
• 平值处理: 当窗口内存在多个相同最小值时，选择索引值最大的那个，以确保取最新的发生位置。

【5. 计算步骤】

1. 数据准备: 获取序列 S 和计算周期 P。
2. 初始化: 设置起始索引 t_start=P-1，初始化 window_lowest_val 与 window_lowest_idx。
3. 迭代计算:
   a. 对于每个 t，从 P-1 到 N-1，确定窗口 [trailingIdx, t]。
   b. 检查旧的最低值索引是否仍在窗口内，否则重新扫描窗口更新 window_lowest_val 和 window_lowest_idx。
   c. 对新数据进行比较，如 S_t <= window_lowest_val，则更新 window_lowest_val 和 window_lowest_idx。
   d. 将 window_lowest_idx 作为 INDEX_{min, t} 输出。
   e. 滑动窗口: trailingIdx 增加 1，为下一步做准备。

【6. 备注与参数说明】

• 参数 P 的选择决定窗口大小（常用值如14、20、30）。
• 数据预处理保证输入序列 S 为连续一维数值序列。
• 输出为相对于原始数据 S 的 0-based 索引。



【7. 关联因子补充说明】

【关联因子信息开始】===============================================================

【因子编号和名称】

因子编号: 001 (示例编号)
因子中文名称: 周期极值位置索引 (Min Max Index, MINMAXINDEX)

【1. 因子名称详情】

因子完整名称: 周期极值位置索引 (Period Min/Max Value Index)
该因子用于确定在过去指定时间周期（窗口）内，输入数据序列中最小值和最大值首次出现的索引（即它们在原始数据序列中的位置）。因子会输出两个序列：一个是每个时间点对应的周期内最低值索引，另一个是最高值索引。

【2. 核心公式】

对于每个时间点 $t$ 和给定的计算周期 $P$，该因子计算以下两个值：

1.  **周期内最低值位置 ($INDEX_{min, t}$):**
    $INDEX_{min, t} = \underset{i \in [t-P+1, t]}{\text{argmax}} \{ j \mid S_j = \min_{k \in [t-P+1, t]} (S_k) \}$
    这表示在时间窗口 $[t-P+1, t]$ 内，找到所有使得数据值 $S_j$ 达到最小的索引 $j$ 中的那个最大（即最新）的索引。

2.  **周期内最高值位置 ($INDEX_{max, t}$):**
    $INDEX_{max, t} = \underset{i \in [t-P+1, t]}{\text{argmax}} \{ j \mid S_j = \max_{k \in [t-P+1, t]} (S_k) \}$
    这表示在时间窗口 $[t-P+1, t]$ 内，找到所有使得数据值 $S_j$ 达到最大的索引 $j$ 中的那个最大（即最新）的索引。

**解释:**
*   $\min_{k \in [t-P+1, t]} (S_k)$：表示在索引从 $t-P+1$ 到 $t$ 的窗口内，数据序列 $S$ 的最小值。
*   $\max_{k \in [t-P+1, t]} (S_k)$：表示在索引从 $t-P+1$ 到 $t$ 的窗口内，数据序列 $S$ 的最大值。
*   $\text{argmax} \{ j \mid \dots \}$：表示在满足特定条件的所有索引 $j$ 中，选择值最大的那个索引。这样做是为了处理当窗口内有多个相同极值时，选择时间上最靠后的那一个（即索引值最大的那一个）。

【3. 变量定义】

*   $S_i$: 输入数据序列在第 $i$ 期（或索引 $i$ 位置）的值。例如，可以是每日的收盘价、最高价、最低价等。索引 $i$ 是相对于整个输入数据序列的、从0开始的绝对位置。
*   $P$: 计算周期（或称窗口长度）。这是一个正整数，表示回顾的期数。
*   $t$: 当前时间点（即当前数据点在序列中的索引）。计算从 $t = P-1$ 开始。
*   $INDEX_{min, t}$: 在时间点 $t$ 计算得到的，在当前回顾窗口 $[t-P+1, t]$ 内数据序列 $S$ 达到最小值时的索引。
*   $INDEX_{max, t}$: 在时间点 $t$ 计算得到的，在当前回顾窗口 $[t-P+1, t]$ 内数据序列 $S$ 达到最大值时的索引。
*   $[t-P+1, t]$: 表示包含当前时间点 $t$ 在内的、长度为 $P$ 的滑动时间窗口。

【4. 函数与方法说明】

*   **滑动窗口最小值/最大值索引搜索**: 该方法的核心是在一个固定长度为 $P$ 的滑动窗口内寻找最小值和最大值所在位置的索引。
    *   当窗口向前滑动时，旧的极值点可能移出窗口。
    *   如果旧的极值点仍在窗口内，则只需将新进入窗口的数据点与当前已知的窗口极值进行比较。
    *   如果旧的极值点已移出窗口，则需要重新扫描整个当前窗口以找到新的极值点及其索引。
*   **平值处理 (Tie-breaking rule)**: 当窗口内存在多个相同的最小值或最大值时，算法选择其中索引最大的那个（即时间上最靠后的那个元素）作为极值点的位置。这在上述公式中通过外层的 $\text{argmax}$ 实现，在代码中通过比较时使用 `>=` (对于最大值) 和 `<=` (对于最小值) 并在值相等时更新索引到当前最新点来实现。

【5. 计算步骤】

因子计算会为每个有效的输入时间点 $t$（从 $P-1$ 开始）生成 $INDEX_{min, t}$ 和 $INDEX_{max, t}$。

1.  **数据准备**:
    *   获取输入数据序列 $S = \{S_0, S_1, \dots, S_{N-1}\}$，其中 $N$ 是数据序列的总长度。
    *   指定计算周期 $P$。

2.  **初始化**:
    *   因子输出的起始索引为 $t_{start} = P-1$。
    *   设置第一个计算窗口的尾部（最旧的数据点）索引 `trailingIdx = 0`。
    *   初始化当前已知的周期内最低值 `window_lowest_val` 和其索引 `window_lowest_idx = -1` (表示无效或待定)。
    *   初始化当前已知的周期内最高值 `window_highest_val` 和其索引 `window_highest_idx = -1` (表示无效或待定)。

3.  **对每个时间点 $t$ 从 $P-1$ 到 $N-1$ 进行迭代计算:**

    a.  **确定当前窗口**: 当前考察的数据窗口为 $S_k$ 其中 $k \in [\text{trailingIdx}, t]$。

    b.  **计算周期内最低值索引 ($INDEX_{min, t}$):**
        i.  **检查旧的最低值索引是否有效**: 如果 `window_lowest_idx` 小于 `trailingIdx` (意味着上一个窗口的最低值点已经滑出了当前窗口)，则需要重新扫描当前窗口：
            1.  将 `window_lowest_val` 初始化为 $S_{\text{trailingIdx}}$，`window_lowest_idx` 初始化为 `trailingIdx`。
            2.  对于当前窗口内从 `trailingIdx + 1` 到 $t$ 的每个索引 $k$：
                如果 $S_k \le \text{window\_lowest\_val}$，则更新 `window_lowest_val} = S_k$，并且 `window_lowest_idx} = k$。
        ii. **比较新进入的数据点**: 否则 (即 `window_lowest_idx` 仍然在当前窗口内)，只需比较当前时间点 $t$ 的值 $S_t$：
            如果 $S_t \le \text{window\_lowest\_val}$，则更新 `window_lowest_val} = S_t$，并且 `window_lowest_idx} = t$。
        iii.将计算得到的 `window_lowest_idx` 作为 $INDEX_{min, t}$ 输出。

    c.  **计算周期内最高值索引 ($INDEX_{max, t}$):**
        i.  **检查旧的最高值索引是否有效**: 如果 `window_highest_idx` 小于 `trailingIdx` (意味着上一个窗口的最高值点已经滑出了当前窗口)，则需要重新扫描当前窗口：
            1.  将 `window_highest_val` 初始化为 $S_{\text{trailingIdx}}$，`window_highest_idx` 初始化为 `trailingIdx`。
            2.  对于当前窗口内从 `trailingIdx + 1` 到 $t$ 的每个索引 $k$：
                如果 $S_k \ge \text{window\_highest\_val}$，则更新 `window_highest_val} = S_k$，并且 `window_highest_idx} = k$。
        ii. **比较新进入的数据点**: 否则 (即 `window_highest_idx` 仍然在当前窗口内)，只需比较当前时间点 $t$ 的值 $S_t$：
            如果 $S_t \ge \text{window\_highest\_val}$，则更新 `window_highest_val} = S_t$，并且 `window_highest_idx} = t$。
        iii.将计算得到的 `window_highest_idx` 作为 $INDEX_{max, t}$ 输出。

    d.  **窗口滑动**: 将 `trailingIdx` 增加 1，为处理下一个时间点 $t+1$ 做准备。

4.  **输出**:
    *   $INDEX_{min}$: 包含所有 $INDEX_{min, t}$ 值的序列。
    *   $INDEX_{max}$: 包含所有 $INDEX_{max, t}$ 值的序列。

【6. 备注与参数说明】

*   **参数选择**:
    *   `optInTimePeriod` ($P$): 计算周期。该参数决定了回顾窗口的大小。常见的选择有14、20、30等。其取值必须大于等于2。
*   **数据预处理**:
    *   输入数据应为一维数值序列。
    *   不需要特别的数据平滑，但数据的质量（如去除极端异常值）可能会影响结果的稳定性。
*   **起始期 (Lookback Period)**:
    *   为了计算第一个因子值 ($INDEX_{min, P-1}$ 和 $INDEX_{max, P-1}$)，需要至少 $P$ 个数据点。因此，指标输出会比原始数据序列短 $P-1$ 期。
*   **输出值**:
    *   输出的索引值是相对于原始输入数据序列的绝对位置（0-based indexing）。例如，如果输出的 $INDEX_{min, t} = 5$，表示在计算时间点 $t$ 时的 $P$ 周期窗口内，原始数据 $S_5$ 是最小值（或并列最小值中最新的一个）。
*   **与MIN/MAX指标的区别**:
    *   标准的MIN/MAX指标通常输出周期内的最小值/最大值本身，而MINMAXINDEX输出的是这些极值在原始序列中的索引。
*   **应用**:
    *   此因子可用于确定支撑位和阻力位形成的时间点。
    *   可用于识别近期高点/低点，作为趋势分析或通道指标（如Donchian Channel）构建的辅助。
    *   通过跟踪极值索引的变化，可以观察极值点在时间上的漂移。

【关联因子信息结束】===============================================================

【因子信息结束】===============================================================