【因子信息开始】===============================================================

【因子编号和名称】
因子编号: 001
因子中文名称: 简单移动平均线 (Simple Moving Average, SMA)

【1. 因子名称详情】
因子1: 简单移动平均线 (Simple Moving Average, SMA)
该指标计算特定周期内一系列数值（通常是收盘价）的算术平均值。它被广泛用于识别趋势方向和产生交易信号。

【2. 核心公式】
对于给定的时间点 $t$ 和周期长度 $N$，简单移动平均值 $SMA_t$ 的计算公式如下：
$$
SMA_t = \frac{\sum_{i=0}^{N-1} P_{t-i}}{N} = \frac{P_t + P_{t-1} + \dots + P_{t-N+1}}{N}
$$
其中：
$P_k$ 代表在时间点 $k$ 的价格（或其他输入数据）。
$N$ 代表计算移动平均的周期长度。

【3. 变量定义】
*   $SMA_t$: 在时间点 $t$ 计算得到的简单移动平均值。
*   $P_t$: 在时间点 $t$ 的输入数据值（例如，收盘价）。
*   $P_{t-i}$: 在时间点 $t$ 之前的第 $i$ 个周期的输入数据值（其中 $i$ 从 $0$ 到 $N-1$）。
*   $N$: 计算移动平均的周期长度，表示用于计算平均值的连续数据点的数量。

【4. 函数与方法说明】
*   **求和 (Summation, $\sum$)**: 此符号表示将指定范围内的所有数值相加。在SMA的计算中，它指的是将最近 $N$ 个周期的价格数据加起来。
*   **算术平均 (Arithmetic Mean)**: 计算一组数值的算术平均值，方法是将所有数值相加，然后除以数值的个数。SMA本质上就是对特定窗口期内数据的算术平均。
*   **滑动窗口 (Sliding Window)**: 计算移动平均时，随着时间的推移，计算窗口会向前“滑动”。对于每个新的时间点，窗口会包含最新的数据点，并排除掉最旧的数据点，然后重新计算平均值。一种高效的计算方法是，在每个新周期，从前一个周期的总和中减去最旧的数据点的值，并加上最新的数据点的值，然后将得到的新的总和除以周期长度 $N$。

【5. 计算步骤】
1.  **数据准备**:
    *   获取一个时间序列的输入数据，例如每日收盘价 $P = \{P_1, P_2, \dots, P_M\}$，其中 $M$ 是数据点的总数。
    *   确定计算周期 $N$（例如，5天，20天等）。

2.  **确定起始计算点**:
    *   为了计算第一个SMA值，至少需要 $N$ 个数据点。因此，SMA序列的第一个有效值将对应于输入序列中的第 $N$ 个数据点（如果数据从索引0开始，则是索引为 $N-1$ 的数据点）。

3.  **计算第一个SMA值**:
    *   选取输入序列中从第1个数据点到第 $N$ 个数据点（即 $P_1, P_2, \dots, P_N$）。
    *   将这 $N$ 个数据点的值相加得到初始的总和 $S_N = \sum_{j=1}^{N} P_j$。
    *   第一个SMA值为 $SMA_N = \frac{S_N}{N}$。

4.  **计算后续SMA值 (使用滑动窗口优化)**:
    *   对于输入序列中的第 $N+1$ 个数据点 $P_{N+1}$：
        *   获取前一个窗口的总和 $S_N$。
        *   新的总和 $S_{N+1}$ 通过从 $S_N$ 中减去该窗口中最旧的数据点 $P_1$，并加上最新的数据点 $P_{N+1}$ 得到： $S_{N+1} = S_N - P_1 + P_{N+1}$。
        *   计算 $SMA_{N+1} = \frac{S_{N+1}}{N}$。
    *   推广到任意时间点 $t > N$：
        *   当前窗口的总和 $S_t$ 可以通过上一个窗口的总和 $S_{t-1}$，减去滑出窗口的最旧数据点 $P_{t-N}$，并加上滑入窗口的最新数据点 $P_t$ 来计算：
          $S_t = S_{t-1} - P_{t-N} + P_t$
        *   然后计算 $SMA_t = \frac{S_t}{N}$。
    *   重复此过程，直到处理完所有可用的数据点。

    *具体实现细节（对应源码逻辑）：*
    *   初始化一个累加器 `periodTotal` 为0。
    *   找到第一个可计算SMA的K期数据的起始位置 `trailingIdx`（即第 $startIdx - (N-1)$ 个数据点）。
    *   首先累加从 `trailingIdx` 到 `startIdx-1` （即前 $N-1$ 个周期）的数据到 `periodTotal`。
    *   然后进入主循环，从 `startIdx` 开始遍历到 `endIdx`：
        a.  将当前周期 `i` 的数据 `inReal[i]` 加入 `periodTotal`。此时 `periodTotal` 包含当前窗口 $N$ 个周期的完整和。
        b.  将 `periodTotal / N` 作为当前周期的SMA值存入输出数组。
        c.  从 `periodTotal` 中减去当前窗口最旧的数据 `inReal[trailingIdx]`。
        d.  将 `trailingIdx` 和 `i` 分别递增，准备计算下一个周期的SMA。

【6. 备注与参数说明】
*   **周期长度 $N$ (optInTimePeriod)**:
    *   这是SMA计算中最重要的参数。
    *   较短的周期（如5或10）会使SMA对价格变化更敏感，产生更多的信号，但可能包含更多噪音。
    *   较长的周期（如50、100或200）会使SMA更平滑，更能反映长期趋势，但对价格变化的反应较慢。
    *   参数 $N$ 的典型取值范围是从2到100000。默认值通常是30。
*   **输入数据 (inReal)**:
    *   通常使用收盘价作为计算SMA的输入数据。
    *   也可以应用于其他价格数据（如开盘价、最高价、最低价、典型价格、加权收盘价）或成交量等其他时间序列数据。
*   **滞后性**:
    *   SMA是一个滞后指标，因为它基于历史数据计算。周期越长，滞后性越明显。
*   **数据起始点 (Lookback)**:
    *   计算第一个 $N$ 周期SMA值需要 $N$ 个数据点。因此，输出序列会比输入序列短 $N-1$ 个数据点。例如，如果周期为20，那么前19个数据点不会有对应的SMA值。

【因子信息结束】===============================================================