【因子信息开始】===============================================================

【因子编号和名称】

因子编号: F023 (此编号为示例，您可以自行编排)
因子中文名称: 变周期移动平均 (Variable Period Moving Average, MAVP)

【1. 因子名称详情】

因子F023: 变周期移动平均 (Variable Period Moving Average, MAVP)。该指标计算一个移动平均，但其计算周期在每个时间点可以动态变化。

【2. 核心公式】

设 \(X_t\) 为在时间点 \(t\) 的输入数据（如收盘价）。
设 \(P_t^{raw}\) 为在时间点 \(t\) 输入的原始周期值。
设 \(P_{min}\) 和 \(P_{max}\) 分别为允许的最小周期和最大周期。

首先，确定在时间点 \(t\) 的有效计算周期 \(N_t\):
\[ N_t = \text{round}(\text{clamp}(P_t^{raw}, P_{min}, P_{max})) \]
其中 \(\text{clamp}(value, min\_val, max\_val) = \max(min\_val, \min(value, max\_val))\)， \(\text{round}(\cdot)\) 表示四舍五入到最近的整数（在Talib实现中，是直接截断为整数）。

然后，MAVP在时间点 \(t\) 的值 \(MAVP_t\) 计算如下：
\[ MAVP_t = MA(X, N_t)_t \]
这里 \(MA(X, N_t)_t\) 表示对输入序列 \(X\) 使用周期 \(N_t\) 计算得到的在时间点 \(t\) 的移动平均值。\(MA\) 的具体类型（如SMA, EMA, WMA等）是一个参数。

【3. 变量定义】
*   \(X_t\): 在时间点 \(t\) 的输入数据序列值，通常为价格序列（如收盘价）。
*   \(P_t^{raw}\): 一个与 \(X_t\) 同步的时间序列，指定了在时间点 \(t\) 用于计算移动平均的“原始”或“建议”周期长度。
*   \(P_{min}\): 允许的最小周期参数，用于限制 \(N_t\) 的下界。
*   \(P_{max}\): 允许的最大周期参数，用于限制 \(N_t\) 的上界。
*   \(N_t\): 在时间点 \(t\) 实际用于计算移动平均的周期。它是 \(P_t^{raw}\) 经过整数化和上下限（\(P_{min}, P_{max}\)）约束后的结果。
*   \(MA\_Type\): 移动平均线的类型，例如简单移动平均 (SMA)、指数移动平均 (EMA)、加权移动平均 (WMA)等。
*   \(MAVP_t\): 在时间点 \(t\) 计算得到的变周期移动平均值。

【4. 函数与方法说明】
*   \(\text{clamp}(value, min\_val, max\_val)\): 截断函数。它将 `value` 限制在 `min_val` 和 `max_val` 之间。如果 `value` 小于 `min_val`，则结果为 `min_val`；如果 `value` 大于 `max_val`，则结果为 `max_val`；否则结果为 `value`。
    公式：\(\text{clamp}(value, min\_val, max\_val) = \max(min\_val, \min(value, max\_val))\)。
*   \(\text{round}(\cdot)\): 四舍五入函数。在Talib的C源码中，通过 `(int)` 类型转换实现向零取整（截断小数部分）。
*   \(MA(X, N)_t\): 表示对输入序列 \(X\)，使用固定周期 \(N\)，计算其在时间点 \(t\) 的移动平均值。具体计算方法依赖于所选的 `MA_Type`：
    *   **简单移动平均 (Simple Moving Average, SMA):**
        \[ SMA(X, N)_t = \frac{1}{N} \sum_{i=0}^{N-1} X_{t-i} \]
        它是过去N个周期数据的算术平均值。
    *   **指数移动平均 (Exponential Moving Average, EMA):**
        \[ EMA(X, N)_t = \alpha \cdot X_t + (1-\alpha) \cdot EMA(X, N)_{t-1} \]
        其中平滑系数 \(\alpha = \frac{2}{N+1}\)。第一个EMA值（\(EMA_0\)）通常使用序列的第一个值 \(X_0\) 或前N个数据的SMA值进行初始化。当用于MAVP时，对于每个时间点 \(t\), 其 \(EMA(X, N_t)_t\) 是指：基于一个完整序列 \(X\), 采用固定的周期参数 \(N_t\), 计算得到的在该序列上的EMA指标在时间点 \(t\) 的值。
    *   **加权移动平均 (Weighted Moving Average, WMA):**
        \[ WMA(X, N)_t = \frac{\sum_{i=0}^{N-1} (N-i) \cdot X_{t-i}}{\sum_{i=0}^{N-1} (N-i)} = \frac{\sum_{i=0}^{N-1} (N-i) \cdot X_{t-i}}{N(N+1)/2} \]
        它给予近期数据更大的权重，权重线性递减。

【5. 计算步骤】
1.  **数据准备:**
    *   获取输入数据序列 \(X = \{X_0, X_1, ..., X_{T-1}\}\)。
    *   获取原始周期序列 \(P^{raw} = \{P_0^{raw}, P_1^{raw}, ..., P_{T-1}^{raw}\}\)。
    *   设定参数：最小周期 \(P_{min}\)，最大周期 \(P_{max}\)，以及移动平均类型 \(MA\_Type\)。

2.  **确定各时间点的计算周期:**
    *   对于输出范围内的每一个时间点 \(t\):
        a.  读取该点的原始周期值 \(P_t^{raw}\)。
        b.  将 \(P_t^{raw}\) 转换为整数 (例如，通过截断小数部分)。
        c.  应用上下限约束：\(N_t^{(temp)} = P_t^{raw}\) (整数化后)。
            如果 \(N_t^{(temp)} < P_{min}\)，则 \(N_t = P_{min}\)。
            如果 \(N_t^{(temp)} > P_{max}\)，则 \(N_t = P_{max}\)。
            否则，\(N_t = N_t^{(temp)}\)。
        d.  将所有计算出的 \(N_t\) 存储起来，形成一个有效周期序列。

3.  **计算变周期移动平均值:**
    *   对于需要计算MAVP值的每一个时间点 \(t\):
        a.  获取该点的有效周期 \(N_t\)。
        b.  根据选定的 \(MA\_Type\) 和周期 \(N_t\)，计算输入序列 \(X\) 在时间点 \(t\) 的移动平均值。
            这意味着，对于一个特定的 \(N_t\), 我们是计算一个以 \(N_t\) 为固定周期的标准移动平均序列，并取该序列在时间点 \(t\) 的值作为 \(MAVP_t\)。
            例如，若 \(MA\_Type\) 为SMA，则 \(MAVP_t = \frac{1}{N_t} \sum_{i=0}^{N_t-1} X_{t-i}\)。
            若 \(MA\_Type\) 为EMA，则 \(MAVP_t\) 是一个以 \(N_t\) 为固定周期计算的EMA序列在 \(t\) 时刻的值。这意味着，如果 \(N_t\) 与 \(N_{t-1}\) 不同，那么 \(MAVP_t\) 和 \(MAVP_{t-1}\) 可能来自两个不同周期参数的EMA序列。

4.  **输出:**
    *   得到变周期移动平均序列 \(MAVP = \{MAVP_{lookback}, ..., MAVP_{T-1}\}\)，其中 `lookback` 是由 \(P_{max}\) 和 \(MA\_Type\) 决定的初始数据不足无法计算MA的长度。

    *(Talib C代码中的优化说明：为了效率，Talib的实现会识别出哪些时间点使用了相同的有效周期 \(N_k\)。对于一个给定的 \(N_k\)，它会一次性计算出使用该周期的标准MA序列（覆盖所有需要输出的时间点）。然后，对于所有需要 \(N_k\) 作为周期的输出点 \(t\)，直接从这个预计算的MA序列中取值 \(MA(X, N_k)_t\)。这避免了对相同周期的MA值进行重复计算。)*

【6. 备注与参数说明】
*   **参数 `optInMinPeriod` (\(P_{min}\)):** 最小周期。限定周期值的下限，例如2。
*   **参数 `optInMaxPeriod` (\(P_{max}\)):** 最大周期。限定周期值的上限，例如30。
*   **参数 `optInMAType` (\(MA\_Type\)):** 移动平均的类型。Talib支持多种类型，如SMA (0), EMA (1), WMA (2), DEMA (3), TEMA (4), TRIMA (5), KAMA (6), MAMA (7), T3 (8)。最经典常用的是SMA, EMA, WMA。
*   **输入 `inPeriods` (\(P_t^{raw}\)):** 这是一个与价格数据对齐的数组，每个元素代表对应价格点计算MA时所用的周期。这个序列的来源可以多种多样，例如可以是基于波动率的动态周期，或者是某些特定模型计算出的周期序列。
*   **数据对齐与长度:** 输入的价格序列 `inReal` 和周期序列 `inPeriods` 必须长度一致且时间点对齐。
*   **滞后性:** MAVP的滞后性会根据当前使用的周期 \(N_t\) 动态变化。较短的周期意味着较小的滞后和更快的响应速度，而较长的周期则会产生更平滑但滞后更大的均线。
*   **整数周期:** Talib 实现中，从 `inPeriods` 读取的周期值会被转换为整数再进行计算。
*   **起始期数据:** 由于移动平均计算需要一定数量的先前数据，MAVP序列的初始部分将没有值，其长度取决于 `optInMaxPeriod` 和 `optInMAType` 所对应的标准MA所需的最大回看期。

【因子信息结束】===============================================================