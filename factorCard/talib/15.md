【因子信息开始】===============================================================

【因子编号和名称】

因子编号: F001
因子中文名称: 反正切函数 (Arctangent, ATAN)

【1. 因子名称详情】

F001: 反正切函数 (Arctangent, ATAN)

该因子计算输入序列中每个数据点的反正切值。

【2. 核心公式】

对于输入序列中的任意一个值 $x_t$，其对应的因子值为：

$ ATAN_t = \arctan(x_t) $

其中：
*   $ATAN_t$ 是在时间点 $t$ 的因子值。
*   $x_t$ 是在时间点 $t$ 的输入数据值。
*   $\arctan()$ 是反正切函数。

【3. 变量定义】

*   $x_t$: 时间点 $t$ 的输入数据值。它可以是任何实数，例如价格、另一个指标的输出值等。
*   $ATAN_t$: 时间点 $t$ 的反正切因子值。其结果通常以弧度表示。

【4. 函数与方法说明】

*   $\arctan(x)$: 反正切函数。
    *   定义：对于给定的实数 $x$，$\arctan(x)$ 返回一个角度 $\theta_r$ (以弧度为单位)，使得 $\tan(\theta_r) = x$。
    *   定义域：所有实数 $(-\infty, +\infty)$。
    *   值域：$(-\pi/2, \pi/2)$ 弧度，约等于 $(-1.570796, 1.570796)$。
    *   例如，$\arctan(0) = 0$，$\arctan(1) = \pi/4 \approx 0.785398$，$\arctan(-1) = -\pi/4 \approx -0.785398$。

【5. 计算步骤】

1.  **数据准备**:
    *   获取一个输入数据序列 $X = \{x_1, x_2, \ldots, x_N\}$，其中 $N$ 是数据点的总数。

2.  **逐点计算**:
    *   对于输入序列 $X$ 中的每一个数据点 $x_t$ (其中 $t$ 从 1 到 $N$):
        *   计算该数据点的反正切值: $ATAN_t = \arctan(x_t)$。

3.  **输出**:
    *   得到一个新的序列 $F_{ATAN} = \{ATAN_1, ATAN_2, \ldots, ATAN_N\}$，该序列即为反正切因子序列。

该因子计算不依赖于其他因子或历史数据（除了当前点的数据）。

【6. 备注与参数说明】

*   **参数**: 该因子没有可调整的参数（如时间窗口长度）。
*   **窗口期**: 计算当前点的因子值不需要历史数据，因此其回顾期（lookback period）为0。这意味着从输入数据的第一个点开始就可以计算出相应的因子值。
*   **输入数据类型**: 输入数据 `inReal` 可以是任何实数序列。
*   **输出单位**: 输出结果以弧度为单位。如果需要将其转换为角度，可以将结果乘以 $(180 / \pi)$。
*   **应用场景**: 反正切函数可以将任意范围的实数映射到 $(-\pi/2, \pi/2)$ 区间内，常用于数据的归一化或转换，使其具有相似的尺度或分布，或者用于某些振荡器类型的指标中。例如，可以将一个无界指标（如价格差）通过反正切函数转换为有界指标。

【因子信息结束】===============================================================