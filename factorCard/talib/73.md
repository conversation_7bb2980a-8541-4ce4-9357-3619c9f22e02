【因子信息开始】===============================================================

【因子编号和名称】

因子编号: FC001: N周期最高值 (N-Period Highest Value, MAX)

【1. 因子名称详情】

因子FC001: N周期最高值 (N-Period Highest Value, MAX)。该因子计算在指定周期 `N` 内，输入序列的最大值。例如，N日最高价。

【2. 核心公式】

对于一个输入时间序列 $P = \{P_0, P_1, P_2, \dots \}$, 在时间点 $t$ 时，周期为 $N$ 的最高值 $MAX_t(N)$ 计算如下：

$MAX_t(N) = \max_{k=t-N+1}^{t} (P_k)$

其中：
- $MAX_t(N)$ 是在时间点 $t$ 计算得到的周期 $N$ 内的最高值。
- $P_k$ 是输入序列在时间点 $k$ 的值。
- $N$ 是计算最高值的时间窗口大小（周期长度）。
- $t$ 是当前时间点的索引，计算要求 $t \ge N-1$。
- 窗口包含 $N$ 个数据点：$P_{t-N+1}, P_{t-N+2}, \dots, P_t$。

【3. 变量定义】

- $P_k$: 输入时间序列在时间点 $k$ (或索引 $k$) 的值。这可以代表价格、成交量或其他任何数值型序列。
- $N$: 计算最高值所使用的时间窗口长度（周期数）。这是一个正整数，通常 $N \ge 2$。
- $t$: 当前数据点的时间索引（从0开始计数）。
- $MAX_t(N)$: 在时间点 $t$ 计算得到的，从时间点 $t-N+1$ 到 $t$ (包含两者) 的 $N$ 个周期内 $P_k$ 的最大值。

【4. 函数与方法说明】

该因子计算的核心是找出在特定滑动窗口内的最大数值。其实现采用了以下方法和策略：

1.  **最大值函数 (Maximum Function)**:
    对一组给定的数值，找出其中的最大者。例如，对于集合 $S = \{x_1, x_2, \dots, x_m\}$，其最大值为 $\max(S) = \max(x_i \mid x_i \in S)$。

2.  **滑动窗口 (Sliding Window)**:
    因子是在一个固定长度为 $N$ 的时间窗口上计算的。这个窗口随着时间的推移（即 $t$ 值的增加）向前滑动。对于每一个新的时间点 $t$，都会形成一个新的数据窗口（包含从 $t-N+1$ 到 $t$ 的数据），并在此新窗口上计算最高值。

3.  **滑动窗口最大值的高效计算策略**:
    为了避免在每个时间点都对整个窗口的数据进行重复扫描以寻找最大值，采用了一种优化的计算策略。该策略的核心思想是跟踪当前窗口内最大值及其在原始序列中的位置（索引）。
    -   当窗口向前滑动时，一个新的数据点进入窗口，同时一个最旧的数据点离开窗口。
    -   **情况一 (最大值移出窗口)**: 如果先前记录的窗口最大值的位置（索引）已经超出了当前新窗口的范围（即它属于被移出的旧数据点），则算法需要重新扫描当前窗口内的所有数据点，以确定新的最大值及其位置。
    -   **情况二 (最大值仍在窗口内)**: 如果先前记录的窗口最大值的位置仍在当前新窗口的范围内：
        -   将此（旧的）最大值与新进入窗口的数据点进行比较。
        -   如果新进入的数据点大于或等于该（旧的）最大值，则此新数据点成为当前窗口的新最大值，并更新其位置。
        -   否则（新进入的数据点小于该（旧的）最大值），则该（旧的）最大值仍然是当前窗口的最大值，其位置不变。
    这种方法显著提高了计算效率，尤其是在最大值不频繁变化或新进入数据成为最大值的情况下。

【5. 计算步骤】

假设输入的时间序列为 $P = \{P_0, P_1, \dots, P_{M-1}\}$，其中 $M$ 是序列的总长度。指定的计算周期为 $N$。输出是一个新的时间序列 $MAX\_Series$。

1.  **数据准备与初始化**:
    -   确保输入序列 $P$ 至少有 $N$ 个数据点，因为第一个有效的最高值输出需要 $N$ 个数据点。
    -   初始化一个变量 `current_max_value` 用于存储当前计算窗口内的最高值。
    -   初始化一个变量 `current_max_index` 用于存储 `current_max_value` 在原始序列 $P$ 中的索引。为确保对第一个窗口执行完整扫描，可将 `current_max_index` 初始化为一个无效的索引值（例如，-1）。

2.  **迭代计算**:
    对于时间索引 $t$ 从 $N-1$ 到 $M-1$（包含两端），执行以下步骤：
    a.  **确定当前窗口范围**: 当前窗口包含的数据点为 $P_k$，其中 $k$ 的范围是从 `window_start_index = t - N + 1` 到 `t`。
    b.  获取当前时间点 $t$ 的输入值: `value_at_t = P_t`。
    c.  **更新窗口最高值**:
        i.  **检查 `current_max_index`的有效性**: 如果 `current_max_index < window_start_index` (即之前记录的最高值已滑出当前窗口)，则需要重新扫描当前窗口：
            1.  将 `current_max_value` 初始化为当前窗口的第一个值，即 `P_{window_start_index}`。
            2.  将 `current_max_index` 初始化为 `window_start_index`。
            3.  遍历当前窗口中的后续值：对于从 `j = window_start_index + 1` 到 $t$ 的每个索引 `j`：
                如果 $P_j > \text{current\_max\_value}$，则更新 $\text{current\_max\_value} = P_j$ 并更新 $\text{current\_max\_index} = j$。
        ii. **否则 (`current_max_index` 仍然在当前窗口内)**:
            比较当前时间点的值 `value_at_t` 与 `current_max_value`：
            如果 $\text{value\_at\_t} \ge \text{current\_max\_value}$，则更新 $\text{current\_max\_value} = \text{value\_at\_t}$ 并更新 $\text{current\_max\_index} = t$。
            (如果 $\text{value\_at\_t} < \text{current\_max\_value}$，则 `current_max_value` 和 `current_max_index` 保持不变，因为之前窗口内的最大值仍然是当前窗口的最大值，且比新进入的值大。)
    d.  **存储结果**: 将计算得到的 `current_max_value` 作为时间点 $t$ 的因子值，即 $MAX\_Series_t = \text{current\_max\_value}$。

3.  **输出**:
    序列 $MAX\_Series$ 从索引 $N-1$ 开始有有效值，其长度为 $M - (N-1)$。

【6. 备注与参数说明】

-   **参数 `N` (周期长度)**: 代表计算最高值时回溯的周期数量。该参数对因子的表现有显著影响。较短的周期对近期价格变动更敏感，而较长的周期则更平滑，反映长期趋势中的高点。
    -   TA-Lib中的 `optInTimePeriod` 对应此处的 `N`。其取值范围通常是从2到100000，常见的默认值为30。用户应根据分析目标和数据特性（如波动性、周期性）选择合适的 `N` 值。
-   **数据类型**: 输入序列 `P` 应为数值型数据。输出的最高值通常与输入数据保持相同的数据类型精度或更高精度（例如，输入float，输出double）。
-   **起始数据要求**: 计算第一个因子值至少需要 `N` 个数据点。因此，输出的因子序列将比原始输入序列短 `N-1` 个数据点（如果从序列头部开始计算）。例如，若输入数据索引从0开始，则第一个 $MAX_t(N)$ 值对应于 $t=N-1$。
-   **数据预处理**: 通常不需要对输入数据进行特殊预处理，除非存在需要剔除的极端异常值（这取决于分析策略是否希望包含这些极端值的影响）。
-   **应用场景**: N周期最高值常用于趋势分析、支撑阻力位识别（如唐奇安通道的上轨）、突破策略等。
-   **无未来数据**: 此因子的计算严格基于历史和当前数据，不使用任何未来数据。

【因子信息结束】===============================================================