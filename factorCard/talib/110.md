【因子信息开始】===============================================================

【因子编号和名称】

因子编号: STOCHF_001
因子中文名称: 快速随机指标K值 (Fast Stochastic %K, FastK)

【1. 因子名称详情】

因子的完整名称为“快速随机指标K值”，英文名称为“Fast Stochastic %K”，简称“FastK”。它衡量的是当前收盘价在过去一段时间内的价格区间中所处的位置。

【2. 核心公式】

对于每一个时间点 $t$：
$ FastK_t = \begin{cases} \frac{C_t - L_n}{H_n - L_n} \times 100 & \text{if } H_n - L_n \neq 0 \\ 0 & \text{if } H_n - L_n = 0 \end{cases} $

其中：
*   $C_t$ 是在时间点 $t$ 的收盘价。
*   $L_n$ 是在过去 $n$ 个周期（包括当前周期 $t$）内的最低价。
*   $H_n$ 是在过去 $n$ 个周期（包括当前周期 $t$）内的最高价。
*   $n$ 是计算 FastK 值所选定的时间窗口长度。

【3. 变量定义】

*   $C_t$: 在时间点 $t$ 的收盘价 (Close Price)。
*   $Low_t$: 在时间点 $t$ 的最低价 (Low Price)。
*   $High_t$: 在时间点 $t$ 的最高价 (High Price)。
*   $n$: 计算 FastK 值的时间窗口参数 (optInFastK_Period)。表示回顾的周期数量，用于确定计算最高价和最低价的区间。
*   $L_n$: 在从时间点 $t-n+1$ 到 $t$ 的 $n$ 个周期内的最低价。
    $ L_n = \min(Low_{t-n+1}, Low_{t-n+2}, \dots, Low_t) $
*   $H_n$: 在从时间点 $t-n+1$ 到 $t$ 的 $n$ 个周期内的最高价。
    $ H_n = \max(High_{t-n+1}, High_{t-n+2}, \dots, High_t) $
*   $FastK_t$: 在时间点 $t$ 计算得到的快速随机指标K值。

【4. 函数与方法说明】

*   $\min(X_1, X_2, \dots, X_k)$: 返回集合 $\{X_1, X_2, \dots, X_k\}$ 中的最小值。
*   $\max(X_1, X_2, \dots, X_k)$: 返回集合 $\{X_1, X_2, \dots, X_k\}$ 中的最大值。

【5. 计算步骤】

1.  **数据准备**: 准备时间序列数据，包括每个周期的最高价 (High)、最低价 (Low) 和收盘价 (Close)。
2.  **参数设定**: 设定 FastK 的时间窗口长度 $n$ (例如，设置为5天或14天，对应源码中的 `optInFastK_Period`)。
3.  **遍历计算**: 从第 $n$ 个数据点开始（因为需要 $n$ 个周期的数据来计算第一个 $L_n$ 和 $H_n$），对于每个时间点 $t$：
    a.  获取当前周期的收盘价 $C_t$。
    b.  确定计算窗口：从当前周期 $t$ 向前回溯 $n$ 个周期，即时间窗口为 $[t-n+1, t]$。
    c.  计算窗口内的最低价 $L_n$: 在上述时间窗口内，找到所有最低价中的最小值。
    d.  计算窗口内的最高价 $H_n$: 在上述时间窗口内，找到所有最高价中的最大值。
    e.  计算价格区间差 $Range_n = H_n - L_n$。
    f.  计算 FastK 值：
        i.  如果 $Range_n > 0$，则 $FastK_t = \frac{C_t - L_n}{Range_n} \times 100$。
        ii. 如果 $Range_n = 0$ (即过去 $n$ 周期内的最高价等于最低价)，则 $FastK_t = 0$。
4.  **输出**: 得到一系列的 $FastK_t$ 值。

【6. 备注与参数说明】

*   **时间窗口 $n$ (optInFastK_Period)**: 这是 FastK 计算的核心参数。较短的窗口期（如5）会使 FastK 线更敏感，波动更大；较长的窗口期（如14）会使 FastK 线更平滑。默认值通常为5或14。源码中默认值为5。
*   **数据预处理**: 确保输入的高、低、收盘价数据是有效的。计算 FastK 至少需要 $n$ 个周期的数据。
*   **边界条件**: 当过去 $n$ 周期内的最高价和最低价相同时 ($H_n - L_n = 0$)，FastK 值被设定为0，以避免除以零的错误。其他实现中，也可能设定为前一个周期的 FastK 值或50。该实现中为0。



【7. 关联因子补充说明】

【关联因子信息开始】===============================================================

【因子编号和名称】

因子编号: STOCHF_001
因子中文名称: 快速随机指标K值 (Fast Stochastic %K, FastK)

【1. 因子名称详情】

因子的完整名称为“快速随机指标K值”，英文名称为“Fast Stochastic %K”，简称“FastK”。它衡量的是当前收盘价在过去一段时间内的价格区间中所处的位置。

【2. 核心公式】

对于每一个时间点 $t$：
$ FastK_t = \begin{cases} \frac{C_t - L_n}{H_n - L_n} \times 100 & \text{if } H_n - L_n \neq 0 \\ 0 & \text{if } H_n - L_n = 0 \end{cases} $

其中：
*   $C_t$ 是在时间点 $t$ 的收盘价。
*   $L_n$ 是在过去 $n$ 个周期（包括当前周期 $t$）内的最低价。
*   $H_n$ 是在过去 $n$ 个周期（包括当前周期 $t$）内的最高价。
*   $n$ 是计算 FastK 值所选定的时间窗口长度。

【3. 变量定义】

*   $C_t$: 在时间点 $t$ 的收盘价 (Close Price)。
*   $Low_t$: 在时间点 $t$ 的最低价 (Low Price)。
*   $High_t$: 在时间点 $t$ 的最高价 (High Price)。
*   $n$: 计算 FastK 值的时间窗口参数 (optInFastK_Period)。表示回顾的周期数量，用于确定计算最高价和最低价的区间。
*   $L_n$: 在从时间点 $t-n+1$ 到 $t$ 的 $n$ 个周期内的最低价。
    $ L_n = \min(Low_{t-n+1}, Low_{t-n+2}, \dots, Low_t) $
*   $H_n$: 在从时间点 $t-n+1$ 到 $t$ 的 $n$ 个周期内的最高价。
    $ H_n = \max(High_{t-n+1}, High_{t-n+2}, \dots, High_t) $
*   $FastK_t$: 在时间点 $t$ 计算得到的快速随机指标K值。

【4. 函数与方法说明】

*   $\min(X_1, X_2, \dots, X_k)$: 返回集合 $\{X_1, X_2, \dots, X_k\}$ 中的最小值。
*   $\max(X_1, X_2, \dots, X_k)$: 返回集合 $\{X_1, X_2, \dots, X_k\}$ 中的最大值。

【5. 计算步骤】

1.  **数据准备**: 准备时间序列数据，包括每个周期的最高价 (High)、最低价 (Low) 和收盘价 (Close)。
2.  **参数设定**: 设定 FastK 的时间窗口长度 $n$ (例如，设置为5天或14天，对应源码中的 `optInFastK_Period`)。
3.  **遍历计算**: 从第 $n$ 个数据点开始（因为需要 $n$ 个周期的数据来计算第一个 $L_n$ 和 $H_n$），对于每个时间点 $t$：
    a.  获取当前周期的收盘价 $C_t$。
    b.  确定计算窗口：从当前周期 $t$ 向前回溯 $n$ 个周期，即时间窗口为 $[t-n+1, t]$。
    c.  计算窗口内的最低价 $L_n$: 在上述时间窗口内，找到所有最低价中的最小值。
    d.  计算窗口内的最高价 $H_n$: 在上述时间窗口内，找到所有最高价中的最大值。
    e.  计算价格区间差 $Range_n = H_n - L_n$。
    f.  计算 FastK 值：
        i.  如果 $Range_n > 0$，则 $FastK_t = \frac{C_t - L_n}{Range_n} \times 100$。
        ii. 如果 $Range_n = 0$ (即过去 $n$ 周期内的最高价等于最低价)，则 $FastK_t = 0$。
4.  **输出**: 得到一系列的 $FastK_t$ 值。

【6. 备注与参数说明】

*   **时间窗口 $n$ (optInFastK_Period)**: 这是 FastK 计算的核心参数。较短的窗口期（如5）会使 FastK 线更敏感，波动更大；较长的窗口期（如14）会使 FastK 线更平滑。默认值通常为5或14。源码中默认值为5。
*   **数据预处理**: 确保输入的高、低、收盘价数据是有效的。计算 FastK 至少需要 $n$ 个周期的数据。
*   **边界条件**: 当过去 $n$ 周期内的最高价和最低价相同时 ($H_n - L_n = 0$)，FastK 值被设定为0，以避免除以零的错误。其他实现中，也可能设定为前一个周期的 FastK 值或50。该实现中为0。

【关联因子信息结束】===============================================================

---

【关联因子信息开始】===============================================================

【因子编号和名称】

因子编号: STOCHF_002
因子中文名称: 快速随机指标D值 (Fast Stochastic %D, FastD)

【1. 因子名称详情】

因子的完整名称为“快速随机指标D值”，英文名称为“Fast Stochastic %D”，简称“FastD”。它是 FastK 线的移动平均，通常作为 FastK 的信号线使用。

【2. 核心公式】

$ FastD_t = MA(FastK, m, \text{type})_t $

其中：
*   $FastK$ 是快速随机指标K值的时间序列。
*   $m$ 是计算 FastD 的移动平均窗口长度 (对应源码中的 `optInFastD_Period`)。
*   $\text{type}$ 指示用于计算 FastD 的移动平均类型 (对应源码中的 `optInFastD_MAType`)。最常见的是简单移动平均 (SMA)。

如果使用简单移动平均 (SMA)，则公式为：
$ FastD_t = \frac{1}{m} \sum_{i=0}^{m-1} FastK_{t-i} $

【3. 变量定义】

*   $FastK_t$: 在时间点 $t$ 计算得到的快速随机指标K值。
*   $m$: 计算 FastD 值的移动平均窗口长度 (optInFastD_Period)。
*   $\text{type}$: 计算 FastD 值时使用的移动平均类型 (optInFastD_MAType)。
*   $FastD_t$: 在时间点 $t$ 计算得到的快速随机指标D值。

【4. 函数与方法说明】

*   $MA(\text{Series}, m, \text{type})_t$: 计算时间序列 $\text{Series}$ 在时间点 $t$ 的 $m$ 周期移动平均值，移动平均的类型由 $\text{type}$ 指定。
    *   **简单移动平均 (Simple Moving Average, SMA)**:
        $ SMA_t(X, m) = \frac{X_t + X_{t-1} + \dots + X_{t-m+1}}{m} = \frac{1}{m} \sum_{i=0}^{m-1} X_{t-i} $
        其中 $X$ 是输入序列，$m$ 是平均周期。
    *   其他移动平均类型可包括指数移动平均 (EMA)、加权移动平均 (WMA) 等，其计算方法各有不同。源码中 `optInFastD_MAType` 默认为0，即SMA。

【5. 计算步骤】

1.  **依赖计算：计算 FastK 序列**：
    首先，需要计算得到 FastK 的时间序列。其计算步骤如下：
    a.  准备时间序列数据，包括每个周期的最高价 (High)、最低价 (Low) 和收盘价 (Close)。
    b.  设定 FastK 的时间窗口长度 $n_{FK}$ (对应 `optInFastK_Period`)。
    c.  从第 $n_{FK}$ 个数据点开始，对于每个时间点 $\tau$：
        i.   获取当前周期的收盘价 $C_\tau$。
        ii.  确定计算窗口：从当前周期 $\tau$ 向前回溯 $n_{FK}$ 个周期，即时间窗口为 $[\tau-n_{FK}+1, \tau]$。
        iii. 计算窗口内的最低价 $L_{n_{FK}, \tau} = \min(Low_{\tau-n_{FK}+1}, \dots, Low_\tau)$。
        iv.  计算窗口内的最高价 $H_{n_{FK}, \tau} = \max(High_{\tau-n_{FK}+1}, \dots, High_\tau)$。
        v.   计算价格区间差 $Range_{n_{FK}, \tau} = H_{n_{FK}, \tau} - L_{n_{FK}, \tau}$。
        vi.  计算 $FastK_\tau$ 值：
            *   如果 $Range_{n_{FK}, \tau} > 0$，则 $FastK_\tau = \frac{C_\tau - L_{n_{FK}, \tau}}{Range_{n_{FK}, \tau}} \times 100$。
            *   如果 $Range_{n_{FK}, \tau} = 0$，则 $FastK_\tau = 0$。
    d.  得到 FastK 时间序列: $\{FastK_1, FastK_2, \dots, FastK_t, \dots \}$.

2.  **参数设定**:
    a.  设定 FastD 的移动平均窗口长度 $m$ (例如，设置为3，对应源码中的 `optInFastD_Period`)。
    b.  选择移动平均的类型 $\text{type}$ (例如，选择 SMA，对应源码中 `optInFastD_MAType` 默认为0)。

3.  **计算 FastD**:
    对于每个时间点 $t$（从 FastK 序列的第 $m$ 个有效值开始，或者说，从原始价格数据的第 $n_{FK} - 1 + m$ 个点开始才能有第一个FastD值）：
    a.  取最近 $m$ 个周期的 FastK 值：$FastK_t, FastK_{t-1}, \dots, FastK_{t-m+1}$。
    b.  根据选定的移动平均类型 $\text{type}$，计算这些 FastK 值的平均。
        例如，如果 $\text{type}$ 为 SMA：
        $ FastD_t = \frac{1}{m} \sum_{i=0}^{m-1} FastK_{t-i} $

4.  **输出**: 得到一系列的 $FastD_t$ 值。

【6. 备注与参数说明】

*   **FastD 周期 $m$ (optInFastD_Period)**: 这是 FastD 计算的核心参数，决定了对 FastK 线的平滑程度。常用值为3。源码中默认值为3。
*   **FastD 移动平均类型 (optInFastD_MAType)**: 指定计算 FastD 时对 FastK 进行平均的方法。最常见的是简单移动平均 (SMA)，TALib中 `MAType=0` 即代表SMA。其他类型如EMA, WMA等也可以使用，会产生不同的平滑效果。
*   **数据依赖**: FastD 的计算完全依赖于预先计算好的 FastK 序列。因此，FastD 的计算起始点会晚于 FastK 的起始点，具体取决于参数 $m$。总共需要 $n_{FK} - 1 + \text{Lookback}_{MA}(m, \text{type})$ 个原始数据点才能计算出第一个 FastD 值，其中 $\text{Lookback}_{MA}$ 是所选MA类型的回看期。对于 SMA，$\text{Lookback}_{MA}(m, \text{SMA}) = m-1$。因此，第一个FastD值需要 $n_{FK}-1 + m-1 = n_{FK}+m-2$ 个历史数据点，在第 $n_{FK}+m-1$ 个位置输出第一个FastD。
*   **信号线**: FastD 通常作为 FastK 的信号线，它们的交叉点可以提供交易信号。

【关联因子信息结束】===============================================================

【因子信息结束】===============================================================