【因子信息开始】===============================================================

【因子编号和名称】

因子编号: LRA001: 线性回归角度 (Linear Regression Angle, LRA)

【1. 因子名称详情】

因子 LRA001: 线性回归角度 (Linear Regression Angle, LRA)。该因子通过计算指定周期内输入数据（如收盘价）的线性回归线的斜率，并将该斜率转换为角度值，用以衡量数据趋势的强度和方向。

【2. 核心公式】

线性回归角度($\alpha$)的计算分为两步：首先计算线性回归线的斜率 $m$，然后将斜率转换为角度。

1.  线性回归线的斜率 $m$：
    ```latex
    m = \frac{P \sum_{i=0}^{P-1} (i \cdot Y_i) - \left(\sum_{i=0}^{P-1} i\right) \left(\sum_{i=0}^{P-1} Y_i\right)}{P \sum_{i=0}^{P-1} i^2 - \left(\sum_{i=0}^{P-1} i\right)^2}
    ```

2.  线性回归角度 $\alpha$ (单位：度)：
    ```latex
    \alpha = \operatorname{atan}(m) \times \frac{180}{\pi}
    ```

公式解释：
*   线性回归模型为 $Y = a + mX$，其中 $Y$是输入数据（如价格），$X$是时间序数。我们计算的是斜率 $m$。
*   $P$ 是计算周期长度。
*   $Y_i$ 是计算窗口内第 $i$ 个数据点的值（按时间顺序，从最旧到最新）。
*   $i$ 是对应数据点 $Y_i$ 的时间序数，从 $0$ (最旧的数据点) 到 $P-1$ (最新的数据点)。
*   $\sum_{i=0}^{P-1} i$ 是时间序数的总和。
*   $\sum_{i=0}^{P-1} i^2$ 是时间序数平方的总和。
*   $\sum_{i=0}^{P-1} Y_i$ 是计算窗口内数据值的总和。
*   $\sum_{i=0}^{P-1} (i \cdot Y_i)$ 是数据值与其对应时间序数乘积的总和。
*   $\operatorname{atan}(m)$ 是斜率 $m$ 的反正切值，结果以弧度为单位。
*   $\frac{180}{\pi}$ 是将弧度转换为度的转换因子。

【3. 变量定义】

*   $P$: (optInTimePeriod) 观测窗口期长度，即用于计算线性回归的数据点数量。例如，如果周期为14天，则 $P=14$。
*   $C_t$: 在时间点 $t$ 的原始输入数据值（例如：股票的每日收盘价）。
*   $Y_i$: 在当前计算窗口中，按时间顺序排列的第 $i$ 个数据点的值。设当前计算点为时间 $k$，则窗口内的数据为 $C_{k-(P-1)}, C_{k-(P-2)}, \dots, C_k$。则 $Y_0 = C_{k-(P-1)}$ (窗口中最旧的数据)，$Y_1 = C_{k-(P-2)}$，依此类推，直到 $Y_{P-1} = C_k$ (窗口中最新的数据)。
*   $x_i$: 对应 $Y_i$ 的时间序数（或自变量）。通常设置为 $x_i = i$，其中 $i = 0, 1, 2, \dots, P-1$。
*   $m$: 计算得到的线性回归线的斜率。
*   $\alpha$: 最终计算得到的线性回归角度，以度为单位。
*   $\pi$: 圆周率，约等于 3.1415926535...

【4. 函数与方法说明】

1.  **求和 ($\sum$)**:
    *   $\sum_{i=0}^{P-1} i = S_x = 0 + 1 + \dots + (P-1) = \frac{P(P-1)}{2}$
    *   $\sum_{i=0}^{P-1} i^2 = S_{xx} = 0^2 + 1^2 + \dots + (P-1)^2 = \frac{(P-1)P(2P-1)}{6}$
    *   $\sum_{i=0}^{P-1} Y_i = S_y = Y_0 + Y_1 + \dots + Y_{P-1}$ (窗口内数据点总和)
    *   $\sum_{i=0}^{P-1} (i \cdot Y_i) = S_{xy} = (0 \cdot Y_0) + (1 \cdot Y_1) + \dots + ((P-1) \cdot Y_{P-1})$ (数据点与时间序数乘积的总和)

2.  **线性回归 (Least Squares Method)**:
    该方法通过最小化实际数据点 $Y_i$ 与回归线 $a + m x_i$ 之间的平方差之和来找到最佳拟合直线 $Y = a + mX$。斜率 $m$ 的计算公式如【2. 核心公式】所示。

3.  **反正切函数 ($\operatorname{atan}$)**:
    $\operatorname{atan}(m)$ 计算一个给定斜率 $m$ 所对应的角度（以弧度为单位）。这个角度是回归线与水平轴（时间轴）的夹角。

4.  **弧度到度的转换**:
    将以弧度为单位的角度乘以 $\frac{180}{\pi}$ 即可转换为以度为单位的角度。

【5. 计算步骤】

对于每个计算点（例如，每个交易日 $k$）：

1.  **数据准备**:
    *   确定计算周期 $P$ (例如, $P=14$)。
    *   选取当前时间点 $k$ 及其之前的 $P-1$ 个时间点的数据，形成一个包含 $P$ 个数据点的窗口：$\{C_{k-(P-1)}, C_{k-(P-2)}, \dots, C_k\}$。
    *   将这些数据点表示为 $Y_0, Y_1, \dots, Y_{P-1}$，其中 $Y_0 = C_{k-(P-1)}$ (最旧)，$Y_{P-1} = C_k$ (最新)。
    *   对应的时间序数为 $x_i = i$ for $i = 0, 1, \dots, P-1$.

2.  **计算中间和**:
    *   计算时间序数的和 $S_x$:
        $S_x = \sum_{i=0}^{P-1} i = \frac{P(P-1)}{2}$
    *   计算时间序数平方的和 $S_{xx}$:
        $S_{xx} = \sum_{i=0}^{P-1} i^2 = \frac{P(P-1)(2P-1)}{6}$
    *   计算窗口内数据值的和 $S_y$:
        $S_y = \sum_{i=0}^{P-1} Y_i$
    *   计算数据值与时间序数乘积的和 $S_{xy}$:
        $S_{xy} = \sum_{i=0}^{P-1} (i \cdot Y_i)$

3.  **计算线性回归斜率 $m$**:
    *   计算分母 $D = P \cdot S_{xx} - (S_x)^2$
    *   计算分子 $N = P \cdot S_{xy} - S_x S_y$
    *   如果 $D \neq 0$，则斜率 $m = N / D$。如果 $D=0$ (在 $P \ge 2$ 时理论上不会发生，除非 $P$ 个 $x_i$ 值都相同，但这里 $x_i=i$ 是不同的)，则 $m$ 未定义或可设为0（视具体处理）。

4.  **计算角度 (弧度)**:
    $\theta_{rad} = \operatorname{atan}(m)$

5.  **转换为度**:
    $\alpha = \theta_{rad} \times \frac{180}{\pi}$

6.  **输出**:
    $\alpha$ 即为当前时间点 $k$ 的线性回归角度因子值。

【6. 备注与参数说明】

*   **参数 `optInTimePeriod` ($P$)**:
    *   这是计算线性回归所用的数据点数量（窗口期）。
    *   TA-Lib中默认值为14。
    *   该参数的最小有效值为2（至少需要两个点才能确定一条直线）。
    *   窗口期的选择会影响因子的敏感度。较短的窗口期对近期价格变化反应更灵敏，产生更多波动；较长的窗口期则更平滑，反映长期趋势。

*   **输入数据 ($C_t$)**:
    *   通常使用收盘价序列作为输入数据。也可以使用开盘价、最高价、最低价、成交量或其他时间序列数据。

*   **数据预处理**:
    *   确保输入数据序列没有缺失值，或对缺失值进行合理填充。
    *   第一个有效的因子值将在有 $P$ 个数据点之后才能计算出来。因此，输出序列的开始部分将有 $P-1$ 个空值。

*   **因子解读**:
    *   角度值通常在 -90度 到 +90度 之间。
    *   正角度表示上升趋势，角度越大，上升趋势越陡峭。
    *   负角度表示下降趋势，角度绝对值越大，下降趋势越陡峭。
    *   接近0度的角度表示趋势平缓或横盘整理。

*   **$\pi$ 的值**:
    在计算中，$\pi$ (PI) 通常取 `atan(1.0) * 4.0` 或高精度数值如 `3.14159265358979323846`。

【因子信息结束】===============================================================