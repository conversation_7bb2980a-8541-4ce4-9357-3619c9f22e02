【因子信息开始】===============================================================

【因子编号和名称】

因子编号: F_TALIB_001_TANH
因子中文名称: 双曲正切 (Hyperbolic Tangent, TANH)

【1. 因子名称详情】

F_TALIB_001_TANH: 双曲正切 (Hyperbolic Tangent, TANH)

【2. 核心公式】

对于时间点 \( t \) 的输入值 \( X_t \)，其双曲正切值 \( \text{TANH}_t \) 计算如下：

\( \text{TANH}_t(X_t) = \frac{e^{X_t} - e^{-X_t}}{e^{X_t} + e^{-X_t}} \)

或者等价地表示为：

\( \text{TANH}_t(X_t) = \frac{e^{2X_t} - 1}{e^{2X_t} + 1} \)

该公式对输入序列中的每一个数据点独立应用。

【3. 变量定义】

*   \( X_t \): 在时间点 \( t \) 的输入数据值。这可以是一个价格序列、收益率序列或其他任何数值型序列中的一个元素。
*   \( e \): 自然对数的底数，约等于 2.71828。
*   \( \text{TANH}_t(X_t) \): 在时间点 \( t \) 对输入值 \( X_t \) 计算得到的双曲正切值。

【4. 函数与方法说明】

*   **双曲正切函数 (Hyperbolic Tangent Function, tanh(x))**:
    *   定义：对于任意实数 \( x \)，其双曲正切函数定义为 \( \tanh(x) = \frac{\sinh(x)}{\cosh(x)} = \frac{e^x - e^{-x}}{e^x + e^{-x}} \)。
    *   性质：该函数将输入值映射到 \( (-1, 1) \) 的开区间内。它是一个奇函数，即 \( \tanh(-x) = -\tanh(x) \)。当 \( x \to \infty \)，\( \tanh(x) \to 1 \)；当 \( x \to -\infty \)，\( \tanh(x) \to -1 \)；\( \tanh(0) = 0 \)。
*   **指数函数 (Exponential Function, \(e^x\))**:
    *   定义：底数为 \( e \) (欧拉数)的指数函数。

【5. 计算步骤】

假设有一个输入的时间序列 \( X = (X_1, X_2, \ldots, X_N) \)。

1.  **数据准备**:
    *   获取输入数据序列 L: \( X_0, X_1, \ldots, X_{N-1} \)，其中 N 是数据点的总数。

2.  **逐点计算**:
    *   对于序列 L 中的每一个数据点 \( X_i \) (其中 \( i \) 从 0 到 \( N-1 \)):
        a.  计算 \( e^{X_i} \)。
        b.  计算 \( e^{-X_i} \)。
        c.  计算分子: \( \text{Numerator}_i = e^{X_i} - e^{-X_i} \)。
        d.  计算分母: \( \text{Denominator}_i = e^{X_i} + e^{-X_i} \)。
        e.  计算当前点的双曲正切值: \( \text{TANH}_i = \frac{\text{Numerator}_i}{\text{Denominator}_i} \)。
        f.  如果分母 \( \text{Denominator}_i \) 极小或为零（理论上 \(e^x + e^{-x}\) 恒大于0，但在浮点数计算中可能出现极端情况，或输入为无穷大），则需按实际数学库对 `tanh` 的实现来处理。通常，标准数学库会妥善处理这些边界情况，例如对于非常大的正数输入，结果趋近于1；对于非常大的负数输入，结果趋近于-1。

3.  **输出**:
    *   得到一个新的序列 \( \text{TANH_OUT} = (\text{TANH}_0, \text{TANH}_1, \ldots, \text{TANH}_{N-1}) \)，该序列中的每个值是对应输入值的双曲正切。

【6. 备注与参数说明】

*   **输入数据**: 该因子直接作用于输入数据序列的每个点，输入数据可以是任何实数。
*   **参数**: 此因子本身没有可调参数（如窗口期）。它是一个逐点变换函数。
*   **回看期 (Lookback Period)**: 计算当前点的 TANH 值不需要历史数据，仅依赖于当前点自身的输入值。因此，其回看期为0。
*   **输出范围**: 双曲正切函数的输出值域为 (-1, 1)。这意味着无论输入值有多大或多小，输出都将被“压缩”到这个范围内。
*   **应用**: 双曲正切函数常用于需要将数据归一化或限制在特定范围的场景，例如在神经网络中作为激活函数，或在某些信号处理应用中。
*   **实现**: 在C代码中，这通常通过调用标准数学库中的 `tanh()` (for double precision) 或 `tanhf()` (for single precision) 函数来实现。这些库函数已经优化并能处理各种边界条件。

【因子信息结束】===============================================================