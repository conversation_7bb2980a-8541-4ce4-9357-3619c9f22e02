【因子信息开始】===============================================================

【因子编号和名称】

因子编号: HTTM001
因子中文名称: 希尔伯特变换 - 趋势周期模式 (Hilbert Transform - Trend vs Cycle Mode, HTTM)

【1. 因子名称详情】

因子1: 希尔伯特变换 - 趋势周期模式 (Hilbert Transform - Trend vs Cycle Mode, HTTM)
该指标利用希尔伯特变换的组件来判断当前市场是处于趋势模式中还是周期（震荡）模式中。输出为0或1，其中1代表趋势模式，0代表周期模式。

【2. 核心公式】

该因子计算涉及多个步骤和中间变量。核心逻辑围绕价格平滑、希尔伯特变换、主导周期和相位计算、以及基于这些组件的趋势/周期模式判断。

**1. 价格平滑 (Smoothed Price, SP):**
   $SP_t = \text{WMA}(Price, 4)_t = \frac{4 \cdot Price_t + 3 \cdot Price_{t-1} + 2 \cdot Price_{t-2} + 1 \cdot Price_{t-3}}{10}$

**2. 希尔伯特变换组件:**
   令 $a = 0.0962$，$b = 0.5769$。
   调整因子 $\alpha_t = (0.075 \cdot Period_{t-1} + 0.54)$

   *   **高通滤波器输出 (Detrender):**
       $Detrender_t = (a \cdot SP_t + b \cdot SP_{t-2} - b \cdot SP_{t-4} - a \cdot SP_{t-6}) \cdot \alpha_t$
       (注意: $SP_t$ 表示当前周期的平滑价格，$SP_{t-k}$ 表示 $t-k$ 周期的平滑价格。若 $t < 6$，则相应的项为0或使用历史数据填充。)

   *   **Q1 分量:**
       $Q1_t = (a \cdot Detrender_t + b \cdot Detrender_{t-2} - b \cdot Detrender_{t-4} - a \cdot Detrender_{t-6}) \cdot \alpha_t$

   *   **I1 分量 (延迟的Detrender):**
       $I1_t = Detrender_{t-3}$

   *   **jI 分量 (Q1的同相分量):**
       $jI_t = (a \cdot I1_t + b \cdot I1_{t-2} - b \cdot I1_{t-4} - a \cdot I1_{t-6}) \cdot \alpha_t$

   *   **jQ 分量 (I1的正交分量):**
       $jQ_t = (a \cdot Q1_t + b \cdot Q1_{t-2} - b \cdot Q1_{t-4} - a \cdot Q1_{t-6}) \cdot \alpha_t$

   *   **平滑的同相分量 (I2) 和正交分量 (Q2):**
       $I2_t = 0.2 \cdot (I1_{t-3} - jQ_t) + 0.8 \cdot I2_{t-1}$
       $Q2_t = 0.2 \cdot (Q1_t + jI_t) + 0.8 \cdot Q2_{t-1}$

**3. 主导周期长度 (Dominant Cycle Period):**
   *   **瞬时周期分量 (Re, Im):**
       $Re_t = 0.2 \cdot (I2_t \cdot I2_{t-1} + Q2_t \cdot Q2_{t-1}) + 0.8 \cdot Re_{t-1}$
       $Im_t = 0.2 \cdot (I2_t \cdot Q2_{t-1} - Q2_t \cdot I2_{t-1}) + 0.8 \cdot Im_{t-1}$

   *   **瞬时周期 (PeriodRaw):**
       若 $Re_t \neq 0$ 且 $Im_t \neq 0$:
       $PeriodRaw_t = \frac{360}{\operatorname{atan}(Im_t / Re_t) \cdot (180/\pi)}$
       (注意：实际计算中通常使用 `atan2(Im_t, Re_t)` 以处理 $Re_t=0$ 的情况并得到正确象限的角度。上述公式为简化表达。角度单位为度。)

   *   **周期调整与平滑 (Period, SmoothPeriod):**
       $Period_t = PeriodRaw_t$
       If $Period_t > 1.5 \cdot Period_{t-1} \implies Period_t = 1.5 \cdot Period_{t-1}$
       If $Period_t < 0.67 \cdot Period_{t-1} \implies Period_t = 0.67 \cdot Period_{t-1}$
       If $Period_t < 6 \implies Period_t = 6$
       If $Period_t > 50 \implies Period_t = 50$
       $Period_t = 0.2 \cdot Period_t + 0.8 \cdot Period_{t-1}$ (EMA 平滑)
       $SmoothPeriod_t = 0.33 \cdot Period_t + 0.67 \cdot SmoothPeriod_{t-1}$ (EMA 平滑)

**4. 主导周期相位 (Dominant Cycle Phase, DCPhase):**
   $N = \lfloor SmoothPeriod_t + 0.5 \rfloor$ (取整作为计算周期)
   $RealPart_t = \sum_{k=0}^{N-1} \sin\left(\frac{k \cdot 2\pi}{N}\right) \cdot SP_{t-k}$
   $ImagPart_t = \sum_{k=0}^{N-1} \cos\left(\frac{k \cdot 2\pi}{N}\right) \cdot SP_{t-k}$

   $DCPhase_t = \operatorname{atan2}(RealPart_t, ImagPart_t) \cdot (180/\pi)$ (角度单位为度)
   $DCPhase_t \leftarrow DCPhase_t + 90^\circ$
   $DCPhase_t \leftarrow DCPhase_t + \frac{360^\circ}{SmoothPeriod_t}$  (WMA滞后补偿)
   If $ImagPart_t < 0 \implies DCPhase_t \leftarrow DCPhase_t + 180^\circ$
   If $DCPhase_t > 315^\circ \implies DCPhase_t \leftarrow DCPhase_t - 360^\circ$ (归一化到 [-45, 315] 区间附近)

**5. 正弦波指标 (Sine Wave Indicators):**
   $Sine_t = \sin(DCPhase_t \cdot \pi/180)$
   $LeadSine_t = \sin((DCPhase_t + 45^\circ) \cdot \pi/180)$

**6. 趋势线 (Trendline):**
   $N = \lfloor SmoothPeriod_t + 0.5 \rfloor$
   $AvgPrice_t = \frac{1}{N} \sum_{k=0}^{N-1} Price_{t-k}$ (N周期的简单移动平均)
   $Trendline_t = \frac{4 \cdot AvgPrice_t + 3 \cdot AvgPrice_{t-1} + 2 \cdot AvgPrice_{t-2} + 1 \cdot AvgPrice_{t-3}}{10}$
   (即 $Trendline_t = \text{WMA}(AvgPrice, 4)_t$)

**7. 趋势/周期模式判断 (HT\_TRENDMODE):**
   *   初始化 $HT\_TRENDMODE_t = 1$ (趋势模式)
   *   初始化 $DaysInTrend_0 = 0$
   *   **条件1 (正弦波交叉):**
       If $(Sine_t > LeadSine_t \text{ AND } Sine_{t-1} \le LeadSine_{t-1})$ OR $(Sine_t < LeadSine_t \text{ AND } Sine_{t-1} \ge LeadSine_{t-1})$:
       Then $DaysInTrend_t = 0$, $HT\_TRENDMODE_t = 0$ (周期模式)
       Else $DaysInTrend_t = DaysInTrend_{t-1} + 1$
   *   **条件2 (趋势持续天数不足):**
       If $DaysInTrend_t < 0.5 \cdot SmoothPeriod_t$:
       Then $HT\_TRENDMODE_t = 0$ (周期模式)
   *   **条件3 (相位变化率):**
       $\Delta Phase_t = DCPhase_t - DCPhase_{t-1}$
       If $SmoothPeriod_t \ne 0$ AND $(0.67 \cdot \frac{360^\circ}{SmoothPeriod_t} < \Delta Phase_t < 1.5 \cdot \frac{360^\circ}{SmoothPeriod_t})$:
       Then $HT\_TRENDMODE_t = 0$ (周期模式)
       (注意：$\Delta Phase_t$ 可能需要处理跨越 $360^\circ$ 的情况，确保其代表最小角度差。)
   *   **条件4 (价格与趋势线偏离):**
       If $Trendline_t \ne 0$ AND $\left| \frac{SP_t - Trendline_t}{Trendline_t} \right| \ge 0.015$:
       Then $HT\_TRENDMODE_t = 1$ (趋势模式)

【3. 变量定义】

*   $Price_t$: $t$ 时刻的输入价格序列 (例如：收盘价)。
*   $SP_t$: $t$ 时刻的4周期加权移动平均价格。
*   $Period_t$: $t$ 时刻计算并平滑后的周期长度。初始值为0。
*   $Detrender_t, Q1_t, I1_t, jI_t, jQ_t$: $t$ 时刻希尔伯特变换的中间分量。
*   $I2_t, Q2_t$: $t$ 时刻平滑后的同相和正交分量。初始值为0。
*   $Re_t, Im_t$: $t$ 时刻用于计算瞬时周期的实部和虚部。初始值为0。
*   $PeriodRaw_t$: $t$ 时刻未经限制和平滑的原始周期长度。
*   $SmoothPeriod_t$: $t$ 时刻进一步平滑后的主导周期长度。初始值为0。
*   $N$: 根据 $SmoothPeriod_t$ 计算得到的用于求和的周期长度。
*   $RealPart_t, ImagPart_t$: $t$ 时刻计算主导周期相位的中间累加值。
*   $DCPhase_t$: $t$ 时刻的主导周期相位 (角度制)。初始值为0。
*   $Sine_t$: $t$ 时刻的相位正弦波。初始值为0。
*   $LeadSine_t$: $t$ 时刻的领先45度相位正弦波。初始值为0。
*   $AvgPrice_t$: $t$ 时刻基于 $N$ 周期的原始价格简单移动平均。
*   $Trendline_t$: $t$ 时刻的趋势线值。
*   $iTrend1_t, iTrend2_t, iTrend3_t$: 分别是 $AvgPrice_{t-1}, AvgPrice_{t-2}, AvgPrice_{t-3}$。初始值为0。
*   $DaysInTrend_t$: $t$ 时刻处于趋势中的天数。初始值为0。
*   $HT\_TRENDMODE_t$: $t$ 时刻的趋势周期模式输出 (1为趋势, 0为周期)。

【4. 函数与方法说明】

*   $\text{WMA}(Data, P)_t$: 对数据序列 `Data` 计算 $P$ 周期的加权移动平均。权重从最近的 $P$ 到最远的 $1$。
    $\text{WMA}(Data, P)_t = \frac{P \cdot Data_t + (P-1) \cdot Data_{t-1} + \dots + 1 \cdot Data_{t-(P-1)}}{P + (P-1) + \dots + 1}$
*   $\operatorname{atan}(y/x)$: 反正切函数，返回 $y/x$ 的角度。
*   $\operatorname{atan2}(y, x)$: 双参数反正切函数，返回点 $(x,y)$ 与x轴正向的夹角，能正确处理 $x=0$ 及不同象限。
*   $\sin(rad)$, $\cos(rad)$: 标准正弦和余弦函数，参数为弧度。
*   $\lfloor x \rfloor$: 向下取整函数。
*   $\sum_{k=c}^{d} f(k)$: 求和函数，从 $k=c$ 到 $k=d$ 对 $f(k)$ 进行累加。
*   $\pi$: 圆周率，约等于3.1415926535。
*   希尔伯特变换组件的计算公式: $Y_t = (a \cdot X_t + b \cdot X_{t-2} - b \cdot X_{t-4} - a \cdot X_{t-6}) \cdot \alpha_t$ 是一个特定的数字滤波器结构，用于从输入序列 $X$ 产生输出序列 $Y$。其中 $a=0.0962, b=0.5769$ 是滤波器系数, $\alpha_t$ 是动态调整因子。

【5. 计算步骤】

1.  **数据准备**: 获取输入价格序列 $Price_t$。
2.  **初始化**:
    *   初始化所有延迟变量（如 $I2_{t-1}, Q2_{t-1}, Re_{t-1}, Im_{t-1}, Period_{t-1}, SmoothPeriod_{t-1}, DCPhase_{t-1}, Sine_{t-1}, LeadSine_{t-1}, AvgPrice_{t-1}, \dots, AvgPrice_{t-3}, DaysInTrend_{t-1}$）为0或根据历史数据填充。
    *   希尔伯特变换相关的循环缓冲区（detrender, Q1, jI, jQ等历史值）需要至少6个周期的历史数据才能完整计算，SP也需要3个历史Price。因此，实际计算开始前需要足够的启动数据（源码中为63个数据点）。
3.  **迭代计算 (对每个时间点 t):**
    a.  **计算平滑价格 ($SP_t$)**: 使用公式 $\text{WMA}(Price, 4)_t$。
    b.  **计算调整因子 ($\alpha_t$)**: 使用 $Period_{t-1}$。
    c.  **计算希尔伯特变换组件**:
        i.  $Detrender_t$ (依赖 $SP_t, SP_{t-2}, SP_{t-4}, SP_{t-6}, \alpha_t$)
        ii. $Q1_t$ (依赖 $Detrender_t, Detrender_{t-2}, Detrender_{t-4}, Detrender_{t-6}, \alpha_t$)
        iii. $I1_t = Detrender_{t-3}$
        iv. $jI_t$ (依赖 $I1_t, I1_{t-2}, I1_{t-4}, I1_{t-6}, \alpha_t$)
        v.  $jQ_t$ (依赖 $Q1_t, Q1_{t-2}, Q1_{t-4}, Q1_{t-6}, \alpha_t$)
        vi. $I2_t$ (依赖 $I1_{t-3}, jQ_t, I2_{t-1}$)
        vii.$Q2_t$ (依赖 $Q1_t, jI_t, Q2_{t-1}$)
    d.  **计算主导周期长度**:
        i.  $Re_t$ (依赖 $I2_t, I2_{t-1}, Q2_t, Q2_{t-1}, Re_{t-1}$)
        ii. $Im_t$ (依赖 $I2_t, I2_{t-1}, Q2_t, Q2_{t-1}, Im_{t-1}$)
        iii.$PeriodRaw_t$ (依赖 $Im_t, Re_t$)
        iv. 调整 $Period_t$ (限制在[6, 50] 区间，并与 $Period_{t-1}$比较调整幅度)
        v.  平滑 $Period_t$ (EMA)
        vi. 平滑 $SmoothPeriod_t$ (EMA)
    e.  **计算主导周期相位 ($DCPhase_t$)**:
        i.  $N = \lfloor SmoothPeriod_t + 0.5 \rfloor$
        ii. $RealPart_t$, $ImagPart_t$ (对过去N个 $SP$ 值进行加权求和)
        iii.计算初始 $DCPhase_t$ (使用 $\operatorname{atan2}$)
        iv. 调整 $DCPhase_t$ (+90, WMA滞后补偿, +180条件性补偿, 归一化)
    f.  **计算正弦波指标**: $Sine_t$, $LeadSine_t$ (基于 $DCPhase_t$)
    g.  **计算趋势线 ($Trendline_t$)**:
        i.  $AvgPrice_t$ (SMA of $Price_t$ over $N$ periods)
        ii. WMA of $AvgPrice_t$ over 4 periods
    h.  **判断趋势/周期模式 ($HT\_TRENDMODE_t$)**:
        i.  根据 $Sine_t, LeadSine_t$ 及其前值更新 $DaysInTrend_t$ 和 $HT\_TRENDMODE_t$。
        ii. 根据 $DaysInTrend_t$ 和 $SmoothPeriod_t$ 更新 $HT\_TRENDMODE_t$。
        iii.根据 $\Delta Phase_t$ 和 $SmoothPeriod_t$ 更新 $HT\_TRENDMODE_t$。
        iv. 根据 $SP_t$ 与 $Trendline_t$ 的偏离程度更新 $HT\_TRENDMODE_t$。
    i.  **更新状态**: 将当前计算的 $I2_t, Q2_t, Re_t, Im_t, Period_t, SmoothPeriod_t, DCPhase_t, Sine_t, LeadSine_t, AvgPrice_t, DaysInTrend_t$ 等保存为下一个时间点的 $t-1$ 值。

【6. 备注与参数说明】

*   **窗口期/Lookback**: 该指标需要较长的历史数据进行初始化。TA-Lib中指定的Lookback为63个周期，另加不稳定周期。这意味着前63+ 个数据点无法产生有效输出。
*   **参数**:
    *   希尔伯特滤波器系数: $a=0.0962$, $b=0.5769$。
    *   EMA平滑系数: $0.2/0.8$ 和 $0.33/0.67$。
    *   WMA周期: 4周期 (用于价格平滑和趋势线平滑)。
    *   周期长度限制: 最小6周期，最大50周期。
    *   相位调整: 包含固定的 $90^\circ$ 调整，以及基于 $ImagPart$ 符号的 $180^\circ$ 调整。
    *   WMA滞后补偿: $360^\circ / SmoothPeriod_t$。
    *   领先正弦波相位: $45^\circ$。
    *   趋势持续天数阈值: $0.5 \cdot SmoothPeriod_t$。
    *   相位变化率窗口: $(0.67 \cdot \frac{360}{SmoothPeriod_t}, 1.5 \cdot \frac{360}{SmoothPeriod_t})$。
    *   价格与趋势线偏离阈值: $1.5\%$ (即0.015)。
*   **数据预处理**: 通常使用收盘价作为输入价格序列。
*   **实现细节**: 源码中使用循环缓冲区来高效管理历史数据，并区分奇偶数数据点进行计算优化，这些是实现层面的考虑，核心逻辑如上述公式和步骤所示。
*   **输出**: 0表示周期模式（震荡市），1表示趋势模式。

【因子信息结束】===============================================================