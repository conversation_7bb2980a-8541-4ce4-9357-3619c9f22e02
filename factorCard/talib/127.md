【因子信息开始】===============================================================

【因子编号和名称】

因子编号: C001: 加权收盘价 (Weighted Close Price, WCLPRICE)

【1. 因子名称详情】

因子C001: 加权收盘价 (Weighted Close Price, WCLPRICE)。该指标计算每个时间周期的加权平均价格，其中收盘价被赋予双倍权重。

【2. 核心公式】

对于任意给定的时间周期 $t$，其加权收盘价 $WCLPRICE_t$ 的计算公式如下：

$WCLPRICE_t = \frac{H_t + L_t + 2 \times C_t}{4}$

公式解释：
*   $H_t$: 代表在时间周期 $t$ 内的最高价。
*   $L_t$: 代表在时间周期 $t$ 内的最低价。
*   $C_t$: 代表在时间周期 $t$ 内的收盘价。
*   该公式将最高价、最低价和两倍的收盘价相加，然后除以4，从而得到一个对收盘价赋予更高权重的价格值。

【3. 变量定义】

*   $WCLPRICE_t$: 在时间周期 $t$ 的加权收盘价。
*   $H_t$: 在时间周期 $t$ 的最高价 (High Price)。
*   $L_t$: 在时间周期 $t$ 的最低价 (Low Price)。
*   $C_t$: 在时间周期 $t$ 的收盘价 (Close Price)。
*   $t$: 时间指标，代表当前计算的K线周期（例如，日、小时等）。

【4. 函数与方法说明】

该因子的计算主要涉及基本的算术运算：
*   **加法 (+)**: 用于将不同价格（最高价、最低价、处理后的收盘价）加总。
*   **乘法 (*)**: 用于将收盘价乘以2，以实现其双倍权重。
*   **除法 (/)**: 用于将加总后的价格除以4，以完成加权平均计算。
该因子不涉及复杂的特殊函数或统计方法。

【5. 计算步骤】

1.  **数据准备**: 对于每一个需要计算因子的时间周期 $t$，获取该周期的最高价 $H_t$、最低价 $L_t$ 和收盘价 $C_t$。
2.  **计算加权收盘价**:
    a.  取当前时间周期 $t$ 的收盘价 $C_t$，将其乘以 2，得到 $2 \times C_t$。
    b.  将当前时间周期 $t$ 的最高价 $H_t$、最低价 $L_t$ 与步骤 a 中得到的 $2 \times C_t$ 相加，得到总和 $S_t = H_t + L_t + 2 \times C_t$。
    c.  将步骤 b 中得到的总和 $S_t$ 除以 4，即可得到当前时间周期 $t$ 的加权收盘价 $WCLPRICE_t = \frac{S_t}{4}$。
3.  对每个时间周期重复步骤1和步骤2，即可得到一系列的加权收盘价。

该因子的计算不依赖于其他因子的计算结果。

【6. 备注与参数说明】

*   **参数**: 该因子本身没有可调参数（如窗口期）。其计算方法是固定的。
*   **窗口期**: 该因子是逐点计算的，即每个时间周期的加权收盘价仅依赖于该周期自身的最高价、最低价和收盘价。因此，它没有回看窗口 (Lookback Period) 的概念，或者说其回看窗口为0（仅当前数据点）。
*   **数据预处理**: 输入的最高价、最低价和收盘价数据应保持其原始精度。不需要特殊的数据预处理，但应确保价格数据的有效性（例如，最高价 >= 收盘价，最高价 >= 最低价，收盘价 >= 最低价）。
*   **应用**: 加权收盘价常被用作其他技术指标计算中的价格输入，因为它被认为比单独使用收盘价更能代表一个交易周期的“典型”价格，因为它考虑了周期内价格波动范围（最高价和最低价）并给予收盘价更高的重要性。
*   **灵活性**: 源码中展示了处理双精度 (`double`) 和单精度 (`float`) 输入数据的版本，但核心计算逻辑完全相同。通常在实际应用中，会根据系统要求和数据精度选择合适的类型。

【因子信息结束】===============================================================