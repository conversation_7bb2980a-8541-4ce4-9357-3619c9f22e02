【因子信息开始】===============================================================

【因子编号和名称】

因子编号: MFI001
因子中文名称: 资金流量指标 (Money Flow Index, MFI)

【1. 因子名称详情】

因子MFI001: 资金流量指标 (Money Flow Index, MFI)。该指标通过结合价格和成交量数据，衡量资金流入和流出的压力，常用于判断市场的超买超卖状态或趋势的强度。

【2. 核心公式】

资金流量指标 (MFI) 的计算基于特定周期内的正资金流和负资金流。

首先定义几个基础概念：

1.  典型价格 (Typical Price, TP):
    `TP_t = (High_t + Low_t + Close_t) / 3`

2.  原始资金流 (Raw Money Flow, RMF):
    `RMF_t = TP_t \times Volume_t`

3.  正资金流 (Positive Money Flow, PMF) 与 负资金流 (Negative Money Flow, NMF):
    *   如果 `TP_t > TP_{t-1}`，则：
        `PMF_t = RMF_t`
        `NMF_t = 0`
    *   如果 `TP_t < TP_{t-1}`，则：
        `PMF_t = 0`
        `NMF_t = RMF_t`
    *   如果 `TP_t = TP_{t-1}`，则：
        `PMF_t = 0`
        `NMF_t = 0`

然后，计算周期 `N` 内的正负资金流总和：

`TotalPositiveMoneyFlow_N = \sum_{i=t-N+1}^{t} PMF_i`

`TotalNegativeMoneyFlow_N = \sum_{i=t-N+1}^{t} NMF_i`

最后，计算资金流量指标 (MFI)：

`MFI_t = 100 \times \frac{TotalPositiveMoneyFlow_N}{TotalPositiveMoneyFlow_N + TotalNegativeMoneyFlow_N}`

特殊情况处理：
如果 `(TotalPositiveMoneyFlow_N + TotalNegativeMoneyFlow_N)` 非常接近0 (例如小于1.0，如源码中处理)，则 `MFI_t = 0`。
如果 `TotalNegativeMoneyFlow_N = 0` 且 `TotalPositiveMoneyFlow_N > 0`，则 `MFI_t = 100`。

【3. 变量定义】

*   `MFI_t`: 第 `t` 期计算得到的资金流量指标值。
*   `N`: 计算MFI的时间周期长度（例如，14天）。
*   `High_t`: 第 `t` 期的最高价。
*   `Low_t`: 第 `t` 期的最低价。
*   `Close_t`: 第 `t` 期的收盘价。
*   `Volume_t`: 第 `t` 期的成交量。
*   `TP_t`: 第 `t` 期的典型价格。
*   `TP_{t-1}`: 第 `t-1` 期（即上一期）的典型价格。
*   `RMF_t`: 第 `t` 期的原始资金流。
*   `PMF_t`: 第 `t` 期的正资金流。
*   `NMF_t`: 第 `t` 期的负资金流。
*   `TotalPositiveMoneyFlow_N`: 最近 `N` 期内所有正资金流 `PMF` 的总和。
*   `TotalNegativeMoneyFlow_N`: 最近 `N` 期内所有负资金流 `NMF` 的总和。

【4. 函数与方法说明】

1.  **典型价格 (Typical Price, TP) 计算**:
    典型价格是一天中高、低、收盘价的平均值，代表了当天的平均价格水平。
    计算公式: `TP_t = (High_t + Low_t + Close_t) / 3`

2.  **原始资金流 (Raw Money Flow, RMF) 计算**:
    原始资金流是将典型价格与当日成交量相乘得到的值，代表了当日参与交易的资金规模。
    计算公式: `RMF_t = TP_t \times Volume_t`

3.  **正负资金流 (PMF, NMF) 的判定与计算**:
    通过比较当日典型价格 `TP_t` 与前一日典型价格 `TP_{t-1}` 来确定资金流的方向：
    *   **价格上涨 (TP_t > TP_{t-1})**: 当日资金被视为正资金流。`PMF_t` 等于当日的 `RMF_t`，`NMF_t` 为0。
    *   **价格下跌 (TP_t < TP_{t-1})**: 当日资金被视为负资金流。`NMF_t` 等于当日的 `RMF_t`，`PMF_t` 为0。
    *   **价格不变 (TP_t = TP_{t-1})**: 当日既无正资金流也无负资金流。`PMF_t` 和 `NMF_t` 均为0。

4.  **资金流的累加 (Summation of Money Flows)**:
    在选定的时间周期 `N` 内，分别将每日的 `PMF_t` 和 `NMF_t` 进行累加，得到该周期内的总正资金流 `TotalPositiveMoneyFlow_N` 和总负资金流 `TotalNegativeMoneyFlow_N`。

【5. 计算步骤】

1.  **数据准备**: 收集至少 `N+1` 期（因为需要前一期的典型价格来计算第一天的资金流方向）的每日最高价 (`High`)、最低价 (`Low`)、收盘价 (`Close`) 和成交量 (`Volume`) 数据。
2.  **计算每日典型价格 (TP)**: 对于从第1期到最新一期的每一天 `t`，使用公式 `TP_t = (High_t + Low_t + Close_t) / 3` 计算典型价格。
3.  **计算每日原始资金流 (RMF)**: 对于从第1期到最新一期的每一天 `t`，使用公式 `RMF_t = TP_t \times Volume_t` 计算原始资金流。
4.  **确定每日正负资金流 (PMF, NMF)**:
    *   从第2期开始（因为需要 `TP_1` 来与 `TP_2` 比较）：
    *   对于每一天 `t`（其中 `t >= 2`），比较 `TP_t` 和 `TP_{t-1}`：
        *   若 `TP_t > TP_{t-1}`，则 `PMF_t = RMF_t`，`NMF_t = 0`。
        *   若 `TP_t < TP_{t-1}`，则 `NMF_t = RMF_t`，`PMF_t = 0`。
        *   若 `TP_t = TP_{t-1}`，则 `PMF_t = 0`，`NMF_t = 0`。
    *   这样，我们会得到从第2期开始的 `PMF` 和 `NMF` 序列。
5.  **计算N期资金流总和**:
    *   MFI的第一个有效值需要 `N` 天的 `PMF` 和 `NMF` 数据。因此，第一个可计算MFI的日期点是原始数据的第 `N+1` 天（它使用第2天到第 `N+1` 天的 `PMF/NMF` 数据）。
    *   对于要计算MFI的当前期 `t`（假设 `t` 已经有了至少 `N` 个前序的 `PMF` 和 `NMF` 值）：
        *   `TotalPositiveMoneyFlow_N = PMF_{t} + PMF_{t-1} + ... + PMF_{t-N+1}`
        *   `TotalNegativeMoneyFlow_N = NMF_{t} + NMF_{t-1} + ... + NMF_{t-N+1}`
6.  **计算资金流量指标 (MFI)**:
    *   计算资金流总和 `SumMF = TotalPositiveMoneyFlow_N + TotalNegativeMoneyFlow_N`。
    *   如果 `SumMF < 1.0` (或一个非常小的正数，源码中的判断条件)，则 `MFI_t = 0.0`。
    *   否则，`MFI_t = 100 \times (TotalPositiveMoneyFlow_N / SumMF)`。
7.  **迭代计算**:
    *   สำหรับวันถัดไป `t+1`:
        *   从 `TotalPositiveMoneyFlow_N` 中减去最旧一期 (`t-N+1`) 的 `PMF` 值，并加上最新一期 (`t+1`) 的 `PMF` 值。
        *   从 `TotalNegativeMoneyFlow_N` 中减去最旧一期 (`t-N+1`) 的 `NMF` 值，并加上最新一期 (`t+1`) 的 `NMF` 值。
    *   重复步骤6来计算新的 `MFI_{t+1}`。此滑动窗口方法用于持续计算MFI序列。

【6. 备注与参数说明】

*   **时间周期 (N)**: `optInTimePeriod` 参数，通常默认值为14。用户可以根据交易策略和分析的资产特性选择不同的周期长度，较短周期对价格变动更敏感，较长周期则更平滑。
*   **数据有效性**: 计算MFI至少需要 `N+1` 个交易日的数据。第一个MFI值将在第 `N+1` 个数据点之后产生（因为第1个 `TP` 用于计算第2个 `PMF/NMF`，然后需要 `N` 个 `PMF/NMF`）。TA-Lib中的实现会考虑这个初始数据积累期（lookback period），确保输出的MFI值是基于足够数据计算得到的。
*   **极端值处理**:
    *   当一个周期内总的正资金流和总的负资金流之和 (`TotalPositiveMoneyFlow_N + TotalNegativeMoneyFlow_N`) 几乎为零时（例如小于1.0），MFI被设为0，以防止除以零的错误并保持指标的稳定性。
    *   如果周期内总负资金流为零 (`TotalNegativeMoneyFlow_N = 0`) 而总正资金流大于零，MFI将为100。
    *   如果周期内总正资金流为零 (`TotalPositiveMoneyFlow_N = 0`) 而总负资金流大于零 (或两者皆为零，已由上一条覆盖)，MFI将为0。
*   **指标范围**: MFI的值域在0到100之间。
*   **与RSI的比较**: MFI在概念上类似于相对强弱指数 (RSI)，但MFI在其计算中加入了成交量，这使其成为一个量价结合的振荡器。RSI仅基于价格变动。
*   **无价格变动**: 如果典型价格连续多日保持不变，则对应的 `PMF` 和 `NMF` 均为0。

【因子信息结束】===============================================================