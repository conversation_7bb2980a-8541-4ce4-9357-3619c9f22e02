【因子信息开始】===============================================================

【因子编号和名称】

因子编号: 001A: 随机相对强弱指数K值 (Stochastic RSI FastK, StochRSIK)

【1. 因子名称详情】

随机相对强弱指数K值 (Stochastic Relative Strength Index FastK, StochRSIK)，该指标首先计算价格的相对强弱指数（RSI），然后将RSI值作为输入，计算其在一定周期内的随机震荡指标（Stochastic Oscillator）的%K值。

【2. 核心公式】

计算 StochRSIK 分为两步：
1.  计算相对强弱指数 (RSI):
    a.  计算价格变化:
        $ \text{Change}_t = P_t - P_{t-1} $
    b.  分离上涨和下跌:
        $ U_t = \begin{cases} \text{Change}_t & \text{if } \text{Change}_t > 0 \\ 0 & \text{if } \text{Change}_t \le 0 \end{cases} $
        $ D_t = \begin{cases} |\text{Change}_t| & \text{if } \text{Change}_t < 0 \\ 0 & \text{if } \text{Change}_t \ge 0 \end{cases} $
    c.  计算平均上涨和平均下跌 (通常使用 Wilder's 平滑法):
        对于第一个周期 $N_{RSI}$（例如14天）：
        $ \text{AvgU}_{N_{RSI}} = \frac{1}{N_{RSI}} \sum_{i=1}^{N_{RSI}} U_i $
        $ \text{AvgD}_{N_{RSI}} = \frac{1}{N_{RSI}} \sum_{i=1}^{N_{RSI}} D_i $
        对于后续周期 $t > N_{RSI}$:
        $ \text{AvgU}_t = \frac{(\text{AvgU}_{t-1} \times (N_{RSI}-1)) + U_t}{N_{RSI}} $
        $ \text{AvgD}_t = \frac{(\text{AvgD}_{t-1} \times (N_{RSI}-1)) + D_t}{N_{RSI}} $
    d.  计算相对强度 (RS):
        $ RS_t = \frac{\text{AvgU}_t}{\text{AvgD}_t} $
        (如果 $ \text{AvgD}_t = 0 $, 则 $RS_t$ 视为极大值，通常导致 $RSI_t = 100$)
    e.  计算 RSI:
        $ RSI_t = 100 - \frac{100}{1 + RS_t} $
        (如果 $ \text{AvgD}_t = 0 $, 则 $RSI_t = 100$)

2.  基于 RSI 值计算随机震荡指标的 %K 值 (StochRSIK):
    $ \text{StochRSIK}_t = 100 \times \frac{RSI_t - \min(RSI_{t-N_K+1}, \dots, RSI_t)}{\max(RSI_{t-N_K+1}, \dots, RSI_t) - \min(RSI_{t-N_K+1}, \dots, RSI_t)} $
    (如果分母为0, 即 $ \max(RSI) = \min(RSI) $, 则 $ \text{StochRSIK}_t = 0 $)

【3. 变量定义】
*   $ P_t $: 在时间点 $t$ 的价格 (通常为收盘价)。
*   $ P_{t-1} $: 在时间点 $t-1$ 的价格。
*   $ \text{Change}_t $: 时间点 $t$ 相对于 $t-1$ 的价格变化。
*   $ U_t $: 时间点 $t$ 的价格上涨幅度。
*   $ D_t $: 时间点 $t$ 的价格下跌幅度 (取绝对值)。
*   $ N_{RSI} $: 计算 RSI 的时间周期长度 (例如14)。
*   $ \text{AvgU}_t $: 在时间点 $t$ 的 $N_{RSI}$ 周期平均上涨幅度。
*   $ \text{AvgD}_t $: 在时间点 $t$ 的 $N_{RSI}$ 周期平均下跌幅度。
*   $ RS_t $: 在时间点 $t$ 的相对强度。
*   $ RSI_t $: 在时间点 $t$ 的相对强弱指数值。
*   $ N_K $: 计算 StochRSIK 时，用于确定RSI值高低点范围的时间周期长度 (例如5)。
*   $ \min(RSI_{t-N_K+1}, \dots, RSI_t) $: 在最近 $N_K$ 个周期内 (包括当前周期 $t$) 的最低 RSI 值。
*   $ \max(RSI_{t-N_K+1}, \dots, RSI_t) $: 在最近 $N_K$ 个周期内 (包括当前周期 $t$) 的最高 RSI 值。
*   $ \text{StochRSIK}_t $: 在时间点 $t$ 的随机相对强弱指数K值。

【4. 函数与方法说明】
*   **Wilder's 平滑法 (Wilder's Smoothing / Exponential Moving Average variant)**:
    一种特殊的指数移动平均，平滑系数 $\alpha = 1/N$。
    $ EMA_t = \frac{Value_t + (EMA_{t-1} \times (N-1))}{N} $
    或等效形式 $ EMA_t = \alpha \cdot Value_t + (1-\alpha) \cdot EMA_{t-1} $, 其中 $ \alpha = 1/N $。
    初始值通常采用简单移动平均 (SMA)。
*   **$\min(\text{data series}, N)$**: 查找给定数据序列在过去 N 个周期内的最小值。
*   **$\max(\text{data series}, N)$**: 查找给定数据序列在过去 N 个周期内的最大值。

【5. 计算步骤】
1.  **数据准备**: 获取一段时间的价格序列 $P_0, P_1, \dots, P_T$。
2.  **计算RSI (第一阶段)**:
    a.  对于每个时间点 $t$ (从 $t=1$ 开始)，计算价格变化 $ \text{Change}_t = P_t - P_{t-1} $。
    b.  根据 $ \text{Change}_t $ 分离上涨 $U_t$ 和下跌 $D_t$。
    c.  选择 RSI 的计算周期 $N_{RSI}$。
    d.  计算初始的平均上涨 $ \text{AvgU}_{N_{RSI}} $ 和平均下跌 $ \text{AvgD}_{N_{RSI}} $：
        $ \text{AvgU}_{N_{RSI}} = \frac{1}{N_{RSI}} \sum_{i=1}^{N_{RSI}} U_i $
        $ \text{AvgD}_{N_{RSI}} = \frac{1}{N_{RSI}} \sum_{i=1}^{N_{RSI}} D_i $
    e.  对于 $ t > N_{RSI} $，使用 Wilder's 平滑法递归计算 $ \text{AvgU}_t $ 和 $ \text{AvgD}_t $:
        $ \text{AvgU}_t = ((\text{AvgU}_{t-1} \times (N_{RSI}-1)) + U_t) / N_{RSI} $
        $ \text{AvgD}_t = ((\text{AvgD}_{t-1} \times (N_{RSI}-1)) + D_t) / N_{RSI} $
    f.  计算相对强度 $ RS_t = \text{AvgU}_t / \text{AvgD}_t $。若 $ \text{AvgD}_t = 0 $, 则 $RS_t$ 无穷大。
    g.  计算 $ RSI_t = 100 - (100 / (1 + RS_t)) $。若 $ \text{AvgD}_t = 0 $, 则 $RSI_t = 100 $。
    h.  由此得到一个 RSI 的时间序列: $RSI_{N_{RSI}}, RSI_{N_{RSI}+1}, \dots, RSI_T$。
3.  **计算StochRSIK (第二阶段)**:
    a.  选择 StochRSIK 的计算周期 $N_K$。
    b.  对于 RSI 序列中的每个值 $RSI_t$ (从有足够历史数据开始，即 $t \ge N_{RSI} + N_K - 1$):
        i.  确定当前 $RSI_t$。
        ii. 找到过去 $N_K$ 个周期内的最低 RSI 值: $RSI_{\min, N_K} = \min(RSI_{t-N_K+1}, \dots, RSI_t)$。
        iii.找到过去 $N_K$ 个周期内的最高 RSI 值: $RSI_{\max, N_K} = \max(RSI_{t-N_K+1}, \dots, RSI_t)$。
        iv. 计算 StochRSIK 值:
            如果 $RSI_{\max, N_K} - RSI_{\min, N_K} = 0$，则 $ \text{StochRSIK}_t = 0 $。
            否则，$ \text{StochRSIK}_t = 100 \times (RSI_t - RSI_{\min, N_K}) / (RSI_{\max, N_K} - RSI_{\min, N_K}) $

【6. 备注与参数说明】
*   **输入数据**: 通常使用收盘价序列。
*   **RSI 周期 ($N_{RSI}$)**: 常用值为14。较短周期使RSI更敏感，较长周期使其更平滑。TA-Lib默认值为14，范围为2至100000。
*   **StochRSIK 周期 ($N_K$)**: 常用值为5或14。此参数决定了在RSI序列上计算随机指标时回看的时间窗口。TA-Lib默认值为5，范围为1至100000。
*   **对称性**: 如果 $N_{RSI}$ 和 $N_K$ 设置为相同的值（例如都为14），StochRSIK 可以被视为“未平滑的随机RSI”，这与Tushar Chande和Stanley Kroll在其文章中描述的原始概念相似。
*   **解释**: StochRSIK 将RSI值置于0到100的范围内，衡量当前RSI值相对于其在特定周期内高低点的水平。值接近100表示RSI接近其周期高点，值接近0表示RSI接近其周期低点。
*   **数据预处理**: 计算需要足够长的历史数据。StochRSIK的第一个有效值需要至少 $N_{RSI} + N_K - 1$ 个价格数据点 (如果RSI的第一个值需要 $N_{RSI}$ 个内部点来平滑，则RSI的第一个有效输出是在 $N_{RSI}$ 之后，那么总共需要 $N_{RSI} + N_K -1$ 个数据点)。



【7. 关联因子补充说明】

【关联因子信息开始】===============================================================

【因子编号和名称】

因子编号: 001A: 随机相对强弱指数K值 (Stochastic RSI FastK, StochRSIK)

【1. 因子名称详情】

随机相对强弱指数K值 (Stochastic Relative Strength Index FastK, StochRSIK)，该指标首先计算价格的相对强弱指数（RSI），然后将RSI值作为输入，计算其在一定周期内的随机震荡指标（Stochastic Oscillator）的%K值。

【2. 核心公式】

计算 StochRSIK 分为两步：
1.  计算相对强弱指数 (RSI):
    a.  计算价格变化:
        $ \text{Change}_t = P_t - P_{t-1} $
    b.  分离上涨和下跌:
        $ U_t = \begin{cases} \text{Change}_t & \text{if } \text{Change}_t > 0 \\ 0 & \text{if } \text{Change}_t \le 0 \end{cases} $
        $ D_t = \begin{cases} |\text{Change}_t| & \text{if } \text{Change}_t < 0 \\ 0 & \text{if } \text{Change}_t \ge 0 \end{cases} $
    c.  计算平均上涨和平均下跌 (通常使用 Wilder's 平滑法):
        对于第一个周期 $N_{RSI}$（例如14天）：
        $ \text{AvgU}_{N_{RSI}} = \frac{1}{N_{RSI}} \sum_{i=1}^{N_{RSI}} U_i $
        $ \text{AvgD}_{N_{RSI}} = \frac{1}{N_{RSI}} \sum_{i=1}^{N_{RSI}} D_i $
        对于后续周期 $t > N_{RSI}$:
        $ \text{AvgU}_t = \frac{(\text{AvgU}_{t-1} \times (N_{RSI}-1)) + U_t}{N_{RSI}} $
        $ \text{AvgD}_t = \frac{(\text{AvgD}_{t-1} \times (N_{RSI}-1)) + D_t}{N_{RSI}} $
    d.  计算相对强度 (RS):
        $ RS_t = \frac{\text{AvgU}_t}{\text{AvgD}_t} $
        (如果 $ \text{AvgD}_t = 0 $, 则 $RS_t$ 视为极大值，通常导致 $RSI_t = 100$)
    e.  计算 RSI:
        $ RSI_t = 100 - \frac{100}{1 + RS_t} $
        (如果 $ \text{AvgD}_t = 0 $, 则 $RSI_t = 100$)

2.  基于 RSI 值计算随机震荡指标的 %K 值 (StochRSIK):
    $ \text{StochRSIK}_t = 100 \times \frac{RSI_t - \min(RSI_{t-N_K+1}, \dots, RSI_t)}{\max(RSI_{t-N_K+1}, \dots, RSI_t) - \min(RSI_{t-N_K+1}, \dots, RSI_t)} $
    (如果分母为0, 即 $ \max(RSI) = \min(RSI) $, 则 $ \text{StochRSIK}_t = 0 $)

【3. 变量定义】
*   $ P_t $: 在时间点 $t$ 的价格 (通常为收盘价)。
*   $ P_{t-1} $: 在时间点 $t-1$ 的价格。
*   $ \text{Change}_t $: 时间点 $t$ 相对于 $t-1$ 的价格变化。
*   $ U_t $: 时间点 $t$ 的价格上涨幅度。
*   $ D_t $: 时间点 $t$ 的价格下跌幅度 (取绝对值)。
*   $ N_{RSI} $: 计算 RSI 的时间周期长度 (例如14)。
*   $ \text{AvgU}_t $: 在时间点 $t$ 的 $N_{RSI}$ 周期平均上涨幅度。
*   $ \text{AvgD}_t $: 在时间点 $t$ 的 $N_{RSI}$ 周期平均下跌幅度。
*   $ RS_t $: 在时间点 $t$ 的相对强度。
*   $ RSI_t $: 在时间点 $t$ 的相对强弱指数值。
*   $ N_K $: 计算 StochRSIK 时，用于确定RSI值高低点范围的时间周期长度 (例如5)。
*   $ \min(RSI_{t-N_K+1}, \dots, RSI_t) $: 在最近 $N_K$ 个周期内 (包括当前周期 $t$) 的最低 RSI 值。
*   $ \max(RSI_{t-N_K+1}, \dots, RSI_t) $: 在最近 $N_K$ 个周期内 (包括当前周期 $t$) 的最高 RSI 值。
*   $ \text{StochRSIK}_t $: 在时间点 $t$ 的随机相对强弱指数K值。

【4. 函数与方法说明】
*   **Wilder's 平滑法 (Wilder's Smoothing / Exponential Moving Average variant)**:
    一种特殊的指数移动平均，平滑系数 $\alpha = 1/N$。
    $ EMA_t = \frac{Value_t + (EMA_{t-1} \times (N-1))}{N} $
    或等效形式 $ EMA_t = \alpha \cdot Value_t + (1-\alpha) \cdot EMA_{t-1} $，其中 $ \alpha = 1/N $。
    初始值通常采用简单移动平均 (SMA)。
*   **$\min(\text{data series}, N)$**: 查找给定数据序列在过去 N 个周期内的最小值。
*   **$\max(\text{data series}, N)$**: 查找给定数据序列在过去 N 个周期内的最大值。

【5. 计算步骤】
1.  **数据准备**: 获取一段时间的价格序列 $P_0, P_1, \dots, P_T$。
2.  **计算RSI (第一阶段)**:
    a.  对于每个时间点 $t$ (从 $t=1$ 开始)，计算价格变化 $ \text{Change}_t = P_t - P_{t-1} $。
    b.  根据 $ \text{Change}_t $ 分离上涨 $U_t$ 和下跌 $D_t$。
    c.  选择 RSI 的计算周期 $N_{RSI}$。
    d.  计算初始的平均上涨 $ \text{AvgU}_{N_{RSI}} $ 和平均下跌 $ \text{AvgD}_{N_{RSI}} $：
        $ \text{AvgU}_{N_{RSI}} = \frac{1}{N_{RSI}} \sum_{i=1}^{N_{RSI}} U_i $
        $ \text{AvgD}_{N_{RSI}} = \frac{1}{N_{RSI}} \sum_{i=1}^{N_{RSI}} D_i $
    e.  对于 $ t > N_{RSI} $，使用 Wilder's 平滑法递归计算 $ \text{AvgU}_t $ 和 $ \text{AvgD}_t $:
        $ \text{AvgU}_t = ((\text{AvgU}_{t-1} \times (N_{RSI}-1)) + U_t) / N_{RSI} $
        $ \text{AvgD}_t = ((\text{AvgD}_{t-1} \times (N_{RSI}-1)) + D_t) / N_{RSI} $
    f.  计算相对强度 $ RS_t = \text{AvgU}_t / \text{AvgD}_t $。若 $ \text{AvgD}_t = 0 $，则 $RS_t$ 无穷大。
    g.  计算 $ RSI_t = 100 - (100 / (1 + RS_t)) $。若 $ \text{AvgD}_t = 0 $，则 $RSI_t = 100 $。
    h.  由此得到一个 RSI 的时间序列: $RSI_{N_{RSI}}, RSI_{N_{RSI}+1}, \dots, RSI_T$。
3.  **计算StochRSIK (第二阶段)**:
    a.  选择 StochRSIK 的计算周期 $N_K$。
    b.  对于 RSI 序列中的每个值 $RSI_t$ (从有足够历史数据开始，即 $t \ge N_{RSI} + N_K - 1$):
        i.  确定当前 $RSI_t$。
        ii. 找到过去 $N_K$ 个周期内的最低 RSI 值: $RSI_{\min, N_K} = \min(RSI_{t-N_K+1}, \dots, RSI_t)$。
        iii.找到过去 $N_K$ 个周期内的最高 RSI 值: $RSI_{\max, N_K} = \max(RSI_{t-N_K+1}, \dots, RSI_t)$。
        iv. 计算 StochRSIK 值:
            如果 $RSI_{\max, N_K} - RSI_{\min, N_K} = 0$，则 $ \text{StochRSIK}_t = 0 $。
            否则，$ \text{StochRSIK}_t = 100 \times (RSI_t - RSI_{\min, N_K}) / (RSI_{\max, N_K} - RSI_{\min, N_K}) $。

【6. 备注与参数说明】
*   **输入数据**: 通常使用收盘价序列。
*   **RSI 周期 ($N_{RSI}$)**: 常用值为14。较短周期使RSI更敏感，较长周期使其更平滑。TA-Lib默认值为14，范围为2至100000。
*   **StochRSIK 周期 ($N_K$)**: 常用值为5或14。此参数决定了在RSI序列上计算随机指标时回看的时间窗口。TA-Lib默认值为5，范围为1至100000。
*   **对称性**: 如果 $N_{RSI}$ 和 $N_K$ 设置为相同的值（例如都为14），StochRSIK 可以被视为“未平滑的随机RSI”，这与Tushar Chande和Stanley Kroll在其文章中描述的原始概念相似。
*   **解释**: StochRSIK 将RSI值置于0到100的范围内，衡量当前RSI值相对于其在特定周期内高低点的水平。值接近100表示RSI接近其周期高点，值接近0表示RSI接近其周期低点。
*   **数据预处理**: 计算需要足够长的历史数据。StochRSIK的第一个有效值需要至少 $N_{RSI} + N_K - 1$ 个价格数据点 (如果RSI的第一个值需要 $N_{RSI}$ 个内部点来平滑，则RSI的第一个有效输出是在 $N_{RSI}$ 之后，那么总共需要 $N_{RSI} + N_K -1$ 个数据点)。

【关联因子信息结束】===============================================================

【关联因子信息开始】===============================================================

【因子编号和名称】

因子编号: 001B: 随机相对强弱指数D值 (Stochastic RSI FastD, StochRSID)

【1. 因子名称详情】

随机相对强弱指数D值 (Stochastic Relative Strength Index FastD, StochRSID)，该指标是对随机相对强弱指数K值 (StochRSIK) 进行移动平均平滑处理后得到的指标。

【2. 核心公式】

1.  **计算 StochRSIK (依赖因子001A)**:
    (此处公式与因子001A中的公式完全相同，为完整性重复列出)
    a.  计算价格变化: $ \text{Change}_t = P_t - P_{t-1} $
    b.  分离上涨和下跌:
        $ U_t = \begin{cases} \text{Change}_t & \text{if } \text{Change}_t > 0 \\ 0 & \text{if } \text{Change}_t \le 0 \end{cases} $
        $ D_t = \begin{cases} |\text{Change}_t| & \text{if } \text{Change}_t < 0 \\ 0 & \text{if } \text{Change}_t \ge 0 \end{cases} $
    c.  计算平均上涨和平均下跌 (Wilder's 平滑法):
        初始: $ \text{AvgU}_{N_{RSI}} = \frac{1}{N_{RSI}} \sum_{i=1}^{N_{RSI}} U_i $, $ \text{AvgD}_{N_{RSI}} = \frac{1}{N_{RSI}} \sum_{i=1}^{N_{RSI}} D_i $
        后续: $ \text{AvgU}_t = \frac{(\text{AvgU}_{t-1} \times (N_{RSI}-1)) + U_t}{N_{RSI}} $, $ \text{AvgD}_t = \frac{(\text{AvgD}_{t-1} \times (N_{RSI}-1)) + D_t}{N_{RSI}} $
    d.  计算相对强度 (RS): $ RS_t = \frac{\text{AvgU}_t}{\text{AvgD}_t} $
    e.  计算 RSI: $ RSI_t = 100 - \frac{100}{1 + RS_t} $
    f.  计算 StochRSIK:
        $ \text{StochRSIK}_t = 100 \times \frac{RSI_t - \min(RSI_{t-N_K+1}, \dots, RSI_t)}{\max(RSI_{t-N_K+1}, \dots, RSI_t) - \min(RSI_{t-N_K+1}, \dots, RSI_t)} $
        (如果分母为0, 则 $ \text{StochRSIK}_t = 0 $)

2.  **计算 StochRSID**:
    $ \text{StochRSID}_t = MA(\text{StochRSIK}, N_D, \text{Type}_{MA})_t $
    其中，最常见的 $Type_{MA}$ 是简单移动平均 (SMA):
    $ \text{StochRSID}_t = \text{SMA}(\text{StochRSIK}, N_D)_t = \frac{1}{N_D} \sum_{i=0}^{N_D-1} \text{StochRSIK}_{t-i} $

【3. 变量定义】
*   $ P_t $: 在时间点 $t$ 的价格 (通常为收盘价)。
*   $ N_{RSI} $: 计算 RSI 的时间周期长度。
*   $ RSI_t $: 在时间点 $t$ 的相对强弱指数值。
*   $ N_K $: 计算 StochRSIK 时，用于确定RSI值高低点范围的时间周期长度。
*   $ \text{StochRSIK}_t $: 在时间点 $t$ 的随机相对强弱指数K值 (因子001A的输出)。
*   $ N_D $: 计算 StochRSID 时，对 StochRSIK 进行移动平均的周期长度 (例如3)。
*   $ \text{Type}_{MA} $: 对 StochRSIK进行平滑时所使用的移动平均类型 (例如 SMA, EMA 等)。TA-Lib 默认为 SMA。
*   $ MA(\text{Series}, N, \text{Type})_t $: 对指定序列 `Series` 在时间点 $t$ 计算周期为 $N$ 的、类型为 `Type` 的移动平均值。
*   $ \text{StochRSID}_t $: 在时间点 $t$ 的随机相对强弱指数D值。
*   (其他 $P_{t-1}, \text{Change}_t, U_t, D_t, \text{AvgU}_t, \text{AvgD}_t, RS_t, \min(RSI), \max(RSI)$ 的定义同因子001A)

【4. 函数与方法说明】
*   **Wilder's 平滑法**: (同因子001A)
*   **$\min(\text{data series}, N)$**: (同因子001A)
*   **$\max(\text{data series}, N)$**: (同因子001A)
*   **简单移动平均 (SMA, Simple Moving Average)**:
    $ SMA_t(X, N) = \frac{X_t + X_{t-1} + \dots + X_{t-N+1}}{N} = \frac{1}{N} \sum_{i=0}^{N-1} X_{t-i} $
    其中 $X$ 是数据序列，$N$ 是平均周期。
*   **指数移动平均 (EMA, Exponential Moving Average)** (作为MA的一种可能类型):
    $ EMA_t(X, N) = \alpha \cdot X_t + (1-\alpha) \cdot EMA_{t-1}(X, N) $
    其中 $\alpha = 2 / (N+1)$ (常见定义) 或 $\alpha = 1/N$ (如Wilder's)。初始 $EMA$ 通常是SMA。

【5. 计算步骤】
1.  **数据准备**: 获取一段时间的价格序列 $P_0, P_1, \dots, P_T$。
2.  **计算RSI (第一阶段，同因子001A)**:
    a.  计算价格变化 $ \text{Change}_t $。
    b.  分离上涨 $U_t$ 和下跌 $D_t$。
    c.  选择 RSI 周期 $N_{RSI}$。
    d.  计算初始及后续的 $ \text{AvgU}_t $ 和 $ \text{AvgD}_t $ (使用 Wilder's 平滑)。
    e.  计算 $ RS_t $。
    f.  计算 $ RSI_t $，得到 RSI 时间序列。
3.  **计算StochRSIK (第二阶段，同因子001A)**:
    a.  选择 StochRSIK 周期 $N_K$。
    b.  对于 RSI 序列中的每个值 $RSI_t$:
        i.  确定当前 $RSI_t$。
        ii. 找到过去 $N_K$ 周期的 $RSI_{\min, N_K}$。
        iii.找到过去 $N_K$ 周期的 $RSI_{\max, N_K}$。
        iv. 计算 $ \text{StochRSIK}_t $。如果 $RSI_{\max, N_K} - RSI_{\min, N_K} = 0$，则 $ \text{StochRSIK}_t = 0 $。否则，$ \text{StochRSIK}_t = 100 \times (RSI_t - RSI_{\min, N_K}) / (RSI_{\max, N_K} - RSI_{\min, N_K}) $。
    c.  由此得到一个 StochRSIK 的时间序列。
4.  **计算StochRSID (第三阶段)**:
    a.  选择 StochRSID 的平滑周期 $N_D$ 和移动平均类型 $\text{Type}_{MA}$ (TA-Lib默认为SMA)。
    b.  对步骤3中得到的 StochRSIK 序列，应用所选的移动平均方法。例如，如果使用 SMA:
        $ \text{StochRSID}_t = \frac{1}{N_D} \sum_{i=0}^{N_D-1} \text{StochRSIK}_{t-i} $
        (需要 StochRSIK 序列有至少 $N_D$ 个数据点才能开始计算 StochRSID)。

【6. 备注与参数说明】
*   **输入数据**: 通常使用收盘价序列。
*   **RSI 周期 ($N_{RSI}$)**: 常用值为14。TA-Lib默认14。
*   **StochRSIK 周期 ($N_K$)**: 常用值为5或14。TA-Lib默认5。
*   **StochRSID 周期 ($N_D$)**: 常用值为3。此参数决定了对 StochRSIK 的平滑程度。较长周期产生更平滑的 StochRSID 线。TA-Lib默认值为3，范围为1至100000。
*   **StochRSID 移动平均类型 ($\text{Type}_{MA}$)**: TA-Lib 允许选择多种移动平均类型，包括 SMA (默认)、EMA、WMA、DEMA、TEMA 等。本卡片以 SMA 为例进行了详细说明。选择不同的MA类型会影响 StochRSID 的响应速度和形态。
*   **依赖性**: StochRSID 的计算完全依赖于 StochRSIK 的结果。
*   **解释**: StochRSID 是 StochRSIK 的平滑线，通常与 StochRSIK 一起用于产生交易信号，例如通过它们的交叉或 StochRSID 进入超买/超卖区域。
*   **数据预处理**: 计算需要足够长的历史数据。StochRSID 的第一个有效值需要至少 $N_{RSI} + N_K - 1 + N_D - 1$ 个价格数据点 (大致估算，取决于EMA和平滑的具体实现)。

【关联因子信息结束】===============================================================

【因子信息结束】===============================================================