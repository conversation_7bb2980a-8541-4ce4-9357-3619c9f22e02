【因子信息开始】===============================================================

【因子编号和名称】

因子编号: F001: 下降方向线 (Minus Directional Indicator, -DI)

【1. 因子名称详情】

因子全称: 下降方向线 (Minus Directional Indicator, 简称 -DI 或 DMI-)。该指标是平均趋向指数(ADX)系统的一部分，用于衡量价格向下的动能。

【2. 核心公式】

$-DI_{N,t} = \frac{-DM_{N,t}}{TR_{N,t}} \times 100$

其中：
*   $-DI_{N,t}$ 是在时间点 $t$、周期为 $N$ 的下降方向线。
*   $-DM_{N,t}$ 是在时间点 $t$、经过 $N$ 周期平滑的下降动向值。
*   $TR_{N,t}$ 是在时间点 $t$、经过 $N$ 周期平滑的真实波幅。

【3. 变量定义】

*   $H_t$: 时间点 $t$ 的最高价。
*   $L_t$: 时间点 $t$ 的最低价。
*   $C_t$: 时间点 $t$ 的收盘价。
*   $H_{t-1}$: 时间点 $t-1$ (上一周期) 的最高价。
*   $L_{t-1}$: 时间点 $t-1$ (上一周期) 的最低价。
*   $C_{t-1}$: 时间点 $t-1$ (上一周期) 的收盘价。
*   $N$: 计算指标所选用的时间周期参数 (例如：14天)。
*   $TR_{1,t}$: 时间点 $t$ 的单周期真实波幅。
*   $-DM_{1,t}$: 时间点 $t$ 的单周期下降动向值。
*   $TR_{N,t}$: 时间点 $t$ 的N周期平滑真实波幅 (根据Wilder平滑法计算得到的累积和)。
*   $-DM_{N,t}$: 时间点 $t$ 的N周期平滑下降动向值 (根据Wilder平滑法计算得到的累积和)。

【4. 函数与方法说明】

1.  **真实波幅 (True Range, $TR_{1,t}$)**:
    表示当前周期的价格波动幅度。
    $TR_{1,t} = \max( (H_t - L_t), |H_t - C_{t-1}|, |L_t - C_{t-1}| )$
    其中 $|x|$ 表示 $x$ 的绝对值。

2.  **单周期下降动向值 (Minus Directional Movement, $-DM_{1,t}$)**:
    表示当前周期价格向下的动能。
    首先计算两个值：
    $UpMove_t = H_t - H_{t-1}$ (向上变化量)
    $DownMove_t = L_{t-1} - L_t$ (向下变化量)

    然后确定 $-DM_{1,t}$:
    如果 $DownMove_t > 0$ 且 $DownMove_t > UpMove_t$，则:
    $-DM_{1,t} = DownMove_t$
    否则:
    $-DM_{1,t} = 0$

3.  **Wilder平滑法 (用于计算 $TR_{N,t}$ 和 $-DM_{N,t}$)**:
    这是一种特殊的指数移动平均，用于平滑数据序列。对于一个序列 $X_t$ (例如 $TR_{1,t}$ 或 $-DM_{1,t}$)，其N周期Wilder平滑累积和 $S_N(X)_t$ 计算如下：
    *   **初始累积和**:
        第一个 $S_N(X)$ 值（记为 $S_N(X)_{\text{initial}}$）是序列 $X_t$ 的前 $N-1$ 个值的简单总和。 (例如，如果 $N=14$，则累加前13个 $X_t$ 值)。
        $S_N(X)_{\text{initial}} = \sum_{i=1}^{N-1} X_i$
        (注意：$X_i$ 是指 $TR_{1,i}$ 或 $-DM_{1,i}$，这些值本身从第二个价格数据点开始计算)。
    *   **后续平滑累积和**:
        对于后续的第 $t$ 个 $X_t$ 值 (即原序列中的第 $N$ 个 $X_t$ 值开始):
        $S_N(X)_t = S_N(X)_{t-1} - \frac{S_N(X)_{t-1}}{N} + X_t$
        其中 $S_N(X)_{t-1}$ 是前一期的平滑累积和。
        因此，$TR_{N,t}$ 即为 $S_N(TR_{1})_t$，$ -DM_{N,t}$ 即为 $S_N(-DM_{1})_t$。

【5. 计算步骤】

1.  **数据准备**:
    获取历史价格数据，包括每个周期的最高价 ($H_t$)、最低价 ($L_t$) 和收盘价 ($C_t$)。选择参数 $N$ (例如14)。

2.  **计算单周期真实波幅 ($TR_{1,t}$)**:
    从第二个数据点开始，对于每个时间点 $t$，根据公式计算 $TR_{1,t}$：
    $TR_{1,t} = \max( (H_t - L_t), |H_t - C_{t-1}|, |L_t - C_{t-1}| )$

3.  **计算单周期下降动向值 ($-DM_{1,t}$)**:
    从第二个数据点开始，对于每个时间点 $t$，根据公式计算 $-DM_{1,t}$：
    $UpMove_t = H_t - H_{t-1}$
    $DownMove_t = L_{t-1} - L_t$
    若 $DownMove_t > 0$ 且 $DownMove_t > UpMove_t$，则 $-DM_{1,t} = DownMove_t$，否则 $-DM_{1,t} = 0$。

4.  **计算N周期平滑下降动向值 ($-DM_{N,t}$) 和N周期平滑真实波幅 ($TR_{N,t}$)**:
    a.  **初始化**:
        计算前 $N-1$ 个 $-DM_{1,t}$ 值的总和，作为初始的 $-DM_{N}$ (记为 $prev\_MinusDM\_sum$)。
        $prev\_MinusDM\_sum = \sum_{i=1}^{N-1} -DM_{1,i}$ (这里的 $i$ 是 $-DM_{1}$ 序列的索引)
        计算前 $N-1$ 个 $TR_{1,t}$ 值的总和，作为初始的 $TR_{N}$ (记为 $prev\_TR\_sum$)。
        $prev\_TR\_sum = \sum_{i=1}^{N-1} TR_{1,i}$ (这里的 $i$ 是 $TR_{1}$ 序列的索引)
        （这些初始总和对应于使用价格数据直到第 $N$ 个价格点所计算出的 $N-1$ 个 $-DM_1$ 和 $TR_1$ 值。）

    b.  **迭代平滑**:
        从第 $N$ 个 $-DM_{1,t}$ 和 $TR_{1,t}$ 值开始 (即使用到第 $N+1$ 个价格数据点)，对于每个后续的时间点 $t$：
        获取当前的单周期值 $-DM_{1,t}$ 和 $TR_{1,t}$。
        $-DM_{N,t} = prev\_MinusDM\_sum - \frac{prev\_MinusDM\_sum}{N} + (-DM_{1,t})$
        $TR_{N,t} = prev\_TR\_sum - \frac{prev\_TR\_sum}{N} + TR_{1,t}$
        更新 $prev\_MinusDM\_sum = -DM_{N,t}$ 和 $prev\_TR\_sum = TR_{N,t}$ 以供下一周期计算使用。
        (注意: 第一个可计算的 $-DI_{N,t}$ 值是在完成上述初始化和平滑过程特定次数后得到的，以确保数值的稳定性。J. Welles Wilder Jr. 的原始方法中，通常在 $(2N-1)$ 个价格周期后或经过 $N$ 个 $TR_1/DM_1$ 值后，开始计算 $DI_N$。)

5.  **计算下降方向线 ($-DI_{N,t}$)**:
    对于每个计算出有效的 $-DM_{N,t}$ 和 $TR_{N,t}$ 的时间点 $t$:
    如果 $TR_{N,t} \neq 0$:
    $-DI_{N,t} = \frac{-DM_{N,t}}{TR_{N,t}} \times 100$
    否则 (如果 $TR_{N,t} = 0$):
    $-DI_{N,t} = 0$

6.  **特殊情况 (当周期 $N \le 1$ 时)**:
    如果周期 $N$ 被设为1（或小于1，但通常最小为1）：
    $-DM_{N,t}$ 直接使用 $-DM_{1,t}$ (但按照代码逻辑，如果 $DownMove_t > 0$ 且 $DownMove_t > UpMove_t$，则为 $DownMove_t$，否则为0)。
    $TR_{N,t}$ 直接使用 $TR_{1,t}$。
    $-DI_{1,t} = \frac{-DM_{1,t}}{TR_{1,t}} \times 100$ (若 $-DM_{1,t}$ 是指当期 $DownMove_t$ 且满足条件，则为 $DownMove_t / TR_{1,t} \times 100$；若 $TR_{1,t} = 0$，则 $-DI_{1,t} = 0$)
    (在这种情况下，代码逻辑简化为：如果 $DownMove_t > 0$ 且 $UpMove_t < DownMove_t$，则 $-DI_{1,t} = (DownMove_t / TR_{1,t})$，否则 $-DI_{1,t} = 0$。注意这里没有乘以100，与 $N>1$ 的情况不同。但通常DI指标会乘以100，因此为了统一性，建议也乘以100。)
    修正上述特殊情况的 $-DI_{1,t}$ 描述：根据C代码，当 $N \le 1$ 时，`outReal[outIdx++] = diffM/tempReal;` (其中 `diffM` 是 $DownMove_t$, `tempReal` 是 $TR_{1,t}$), 这意味着结果并未乘以100。如果需要与 $N>1$ 的尺度一致，应乘以100。这里我们按照C代码原始逻辑不乘100。
    $-DI_{1,t} = \frac{DownMove_t}{TR_{1,t}}$ (仅当 $DownMove_t > 0$ 且 $UpMove_t < DownMove_t$ 时，且 $TR_{1,t} \neq 0$)
    否则 $-DI_{1,t} = 0$

【6. 备注与参数说明】

*   **时间周期 (N)**: 最常用的参数值为14。不同的市场和分析目标可能需要调整此参数。较短的周期使指标更敏感，较长的周期则更平滑。
*   **起始期**: Wilder平滑法在计算初期需要一定数量的数据进行“预热”才能产生稳定的指标值。因此，序列头部的几个指标值可能不太可靠或不被计算。
*   **数据预处理**: 通常需要确保输入的高、低、收价格数据是有效的。
*   **用途**: -DI 通常与 +DI (上升方向线) 和 ADX (平均趋向指数) 一同使用，构成完整的DMI动向指标系统，用于判断趋势的强度和方向。-DI 衡量下降趋势的强度。

【因子信息结束】===============================================================