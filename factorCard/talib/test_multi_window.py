import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 为了在图表中正确显示中文
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def calculate_final_model(
    w1_inputs: list,
    alpha: float = 1.0,
    w_max: float = 300.0,
    lambda_rate: float = 0.1,
    **window_configs: float
) -> pd.DataFrame:
    """
    最终版：“三段式混合”模型
    - w1 < min_base: 线性缩放
    - w1 == min_base: 返回初始值 (锚点)
    - w1 > min_base: 动态范围归一化
    """
    if not window_configs:
        raise ValueError("请至少传入一个窗口参数。")

    min_base = min(window_configs.values())
    max_base = max(window_configs.values())
    
    if max_base == min_base:
        # 特殊情况处理... (逻辑不变)
        base_val = min_base
        results_list = []
        for w1_raw in w1_inputs:
            w1 = max(1.0, w1_raw)
            row_data = {'w1_input': w1_raw}
            for name in window_configs:
                row_data[name] = min(max(base_val, w1), w_max)
            results_list.append(row_data)
        return pd.DataFrame(results_list).set_index('w1_input')

    results_list = []
    for w1_raw in w1_inputs:
        w1 = max(1.0, w1_raw)
        row_data = {'w1_input': w1_raw} 
        
        # --- 根据w1的值，选择不同的计算模式 ---
        if w1 < min_base:
            # **情景A: 线性缩放模式**
            max_window_current_val = w1 * (max_base / min_base)
            for name, base_value in window_configs.items():
                final_value = max_window_current_val * (base_value / max_base)
                row_data[name] = final_value
        elif w1 == min_base:
            # **情景B: 锚点时刻，直接返回初始值**
            for name, base_value in window_configs.items():
                row_data[name] = base_value
        else: # w1 > min_base
            # **情景C: 动态范围归一化模式**
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
            dynamic_max = min(dynamic_max, w_max)
            for name, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w1)
                final_value = w1 + position * target_range_size
                final_value = min(max(final_value, w1), w_max)
                row_data[name] = final_value
            
        results_list.append(row_data)
        
    df = pd.DataFrame(results_list).set_index('w1_input')
    return df

# --- 如何使用与画新图 ---
if __name__ == '__main__':
    default_windows = {
        'w1': 5,
    }
    
    # 为了清晰地看到w1=2这个锚点的效果，我们使用更密集的点
    w1_scenarios = list(np.arange(1, 100, 5))

    results_df = calculate_final_model(
        w1_scenarios, 
        alpha=1.2, 
        **default_windows
    )

    sorted_window_names = sorted(default_windows, key=default_windows.get)
    sorted_df = results_df[sorted_window_names]

    print("“三段式混合”模型的最终计算结果:")
    # 打印w1在1.5, 2.0, 2.5附近的结果，来验证锚点
    print(sorted_df.loc[1.5:2.5].round(2))
    
    # 画图
    ax = sorted_df.plot(figsize=(14, 8), grid=True, style='-o', ms=4)
    
    # 增加一条垂直线，清晰地标出模式切换点/锚点
    min_base_val = min(default_windows.values())
    plt.axvline(x=min_base_val, color='purple', linestyle='-.', linewidth=2, label=f'锚点 (w1={min_base_val})')
    
    # 准备并画出默认值参考点 (不再画线，只画点，因为只有在锚点处才精确相等)
    default_series = pd.Series(default_windows)[sorted_window_names]
    plt.scatter(x=[min_base_val]*len(default_series), y=default_series.values, color='black', marker='*', s=250, label='默认参数初始值', zorder=10)

    plt.title(f'最终模型：“三段式混合”模型效果图 (alpha={1.2})', fontsize=16)
    plt.xlabel('外部输入变量 (w1)', fontsize=12)
    plt.ylabel('计算出的最终窗口值', fontsize=12)
    plt.legend(title='图例')
    plt.show()
    
    