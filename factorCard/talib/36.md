【因子信息开始】===============================================================

【因子编号和名称】

因子编号: HTDCP001
因子中文名称: 希尔伯特变换 - 主导周期 (Hilbert Transform - Dominant Cycle Period, HT_DCPERIOD)

【1. 因子名称详情】

因子1: 希尔伯特变换 - 主导周期 (Hilbert Transform - Dominant Cycle Period, HT_DCPERIOD)
该因子通过希尔伯特变换技术来识别输入时间序列（通常是价格）中的主导周期长度。

【2. 核心公式】

该因子的计算涉及多个步骤，最终输出的是平滑后的主导周期 `SmoothPeriod_t`。其核心计算流程可以概括为：
1.  价格平滑
2.  趋势去除与希尔伯特变换分量计算 (I1, Q1)
3.  辅助希尔伯特变换分量计算 (jI, jQ)
4.  同相与正交分量的进一步平滑 (I2, Q2)
5.  相位信息的提取 (Re, Im)
6.  瞬时周期的计算与约束
7.  周期的平滑

最终输出 `SmoothPeriod_t` 的计算公式为：
`SmoothPeriod_t = \alpha_{sp} \cdot Period_t + (1 - \alpha_{sp}) \cdot SmoothPeriod_{t-1}`
其中 `\alpha_{sp} = 0.33`

而 `Period_t` 的计算为：
`Period_t = \alpha_{p} \cdot LimitedRawPeriod_t + (1 - \alpha_{p}) \cdot Period_{t-1}`
其中 `\alpha_{p} = 0.2`

`LimitedRawPeriod_t` 是对原始计算周期 `RawPeriod_t` 进行边界约束和突变限制后的结果。
`RawPeriod_t = \frac{360^\circ}{|\text{phaseChangeDegree}_t|}` (如果 `\text{phaseChangeDegree}_t \neq 0`)
其中 `\text{phaseChangeDegree}_t = \operatorname{atan2}(Im_t, Re_t) \cdot \frac{180^\circ}{\pi}` (实际代码中使用 `atan(Im_t/Re_t)`, 但 `atan2` 更鲁棒)

`Im_t` 和 `Re_t` 是通过对 `I2` 和 `Q2` 分量进行特定计算并平滑得到的。

【3. 变量定义】

*   `P_t`: 在时间点 `t` 的输入价格（或其他序列值）。
*   `S_t`: 在时间点 `t` 的平滑价格，是 `P_t` 的4周期加权移动平均。
*   `AdjustedFactor_t`: 在时间点 `t` 的调整因子， `AdjustedFactor_t = (0.075 \cdot Period_{t-1}) + 0.54`。
*   `D_t`: 在时间点 `t` 的趋势去除量 (Detrender)。
*   `I1_t`: 在时间点 `t` 的第一同相分量 (In-phase component)， `I1_t = D_{t-3}`。
*   `Q1_t`: 在时间点 `t` 的第一正交分量 (Quadrature component)。
*   `jI_t`: 在时间点 `t` 的辅助同相分量，基于 `I1` 计算。
*   `jQ_t`: 在时间点 `t` 的辅助正交分量，基于 `Q1` 计算。
*   `I2_t`: 在时间点 `t` 的平滑后第二同相分量。
*   `Q2_t`: 在时间点 `t` 的平滑后第二正交分量。
*   `Re_t`: 在时间点 `t` 的周期信号的实部。
*   `Im_t`: 在时间点 `t` 的周期信号的虚部。
*   `RawPeriod_t`: 在时间点 `t` 计算得到的原始周期长度。
*   `LimitedRawPeriod_t`: 对 `RawPeriod_t` 应用了上下限及变化速率约束后的周期长度。
*   `Period_t`: 在时间点 `t` 的第一次平滑后的周期长度。
*   `SmoothPeriod_t`: 在时间点 `t` 的最终平滑后的主导周期长度 (因子输出值)。
*   `a`: 常数，`a = 0.0962`。
*   `b`: 常数，`b = 0.5769`。
*   `\alpha_1`: EMA平滑系数，`\alpha_1 = 0.2` (用于 `I2, Q2, Re, Im, Period`)。
*   `\alpha_{sp}`: EMA平滑系数，`\alpha_{sp} = 0.33` (用于 `SmoothPeriod`)。

【4. 函数与方法说明】

1.  **4周期加权移动平均 (WMA4):**
    `S_t = \frac{4 \cdot P_t + 3 \cdot P_{t-1} + 2 \cdot P_{t-2} + 1 \cdot P_{t-3}}{4+3+2+1}`
    在代码中，这是通过一个迭代过程高效实现的：
    `periodWMASum_t = periodWMASum_{t-1} - periodWMASub_{t-1} + P_t \cdot 4`
    `periodWMASub_t = periodWMASub_{t-1} - P_{t-4} + P_t`
    `S_t = periodWMASum_t \cdot 0.1` (因为 `1/(4+3+2+1) = 0.1`)

2.  **希尔伯特变换滤波器 (Hilbert Transform Filter):**
    这是一种数字滤波器，用于从输入序列 `X` 中计算输出 `F`。
    `F_t = (a \cdot X_t + b \cdot X_{t-2} - b \cdot X_{t-4} - a \cdot X_{t-6}) \cdot AdjustedFactor_t`
    其中 `a = 0.0962`, `b = 0.5769`。`AdjustedFactor_t` 使得滤波器具有自适应性。
    该滤波器结构用于计算 `D_t` (输入 `S_t`), `Q1_t` (输入 `D_t`), `jI_t` (输入 `I1_{t-k}` 即 `D_{t-k-3}`), `jQ_t` (输入 `Q1_t`)。

3.  **指数移动平均 (EMA):**
    `EMA_t = \alpha \cdot \text{Value}_t + (1 - \alpha) \cdot EMA_{t-1}`
    其中 `\alpha` 是平滑常数。

4.  **反正切函数 (Arctangent):**
    `\operatorname{atan2}(y, x)` 返回 `y/x` 的反正切值，结果为弧度，并能正确处理 `x=0` 的情况和不同象限。
    代码中使用 `std_atan(Im_t/Re_t)`，这等价于 `atan(Im/Re)`.
    将弧度转换为角度： `\text{angle_degrees} = \text{angle_radians} \cdot \frac{180^\circ}{\pi}`。

【5. 计算步骤】

1.  **数据准备**:
    获取输入价格序列 `P_t`。
    初始化历史状态变量，如 `Period_0` (可设为0或一个经验值，如10-20), `SmoothPeriod_0` 等。由于计算中包含多级滞后和平滑，需要足够的历史数据进行预热（TA-Lib中为32周期 + 不稳定周期长度）。

2.  **价格平滑**:
    对于每个时间点 `t`，计算当前价格 `P_t` 的4周期加权移动平均 `S_t`：
    `S_t = \text{WMA4}(P_t)`

3.  **迭代计算主导周期**:
    对于每个时间点 `t` (从预热期结束后开始):

    a.  **计算调整因子**:
        `AdjustedFactor_t = (0.075 \cdot Period_{t-1}) + 0.54`
        (对于第一个有效计算点，`Period_{t-1}` 使用初始化值或前一个迭代的稳定值)。

    b.  **计算趋势去除量 (Detrender)**:
        `D_t = (a \cdot S_t + b \cdot S_{t-2} - b \cdot S_{t-4} - a \cdot S_{t-6}) \cdot AdjustedFactor_t`

    c.  **计算第一同相分量 (I1)**:
        `I1_t = D_{t-3}` (即3个周期前的趋势去除量)

    d.  **计算第一正交分量 (Q1)**:
        `Q1_t = (a \cdot D_t + b \cdot D_{t-2} - b \cdot D_{t-4} - a \cdot D_{t-6}) \cdot AdjustedFactor_t`

    e.  **计算辅助同相分量 (jI)**:
        `jI_t = (a \cdot I1_{t-3} + b \cdot I1_{t-5} - b \cdot I1_{t-7} - a \cdot I1_{t-9}) \cdot AdjustedFactor_t`
        (这实际上是对 `D_{t-6}, D_{t-8}, D_{t-10}, D_{t-12}` 进行滤波)

    f.  **计算辅助正交分量 (jQ)**:
        `jQ_t = (a \cdot Q1_t + b \cdot Q1_{t-2} - b \cdot Q1_{t-4} - a \cdot Q1_{t-6}) \cdot AdjustedFactor_t`

    g.  **计算平滑后的第二同相和正交分量 (I2, Q2)** (使用 `\alpha_1 = 0.2` 进行EMA):
        `Q2_t = \alpha_1 \cdot (Q1_t + jI_t) + (1 - \alpha_1) \cdot Q2_{t-1}`
        `I2_t = \alpha_1 \cdot (I1_t - jQ_t) + (1 - \alpha_1) \cdot I2_{t-1}`

    h.  **计算周期信号的实部 (Re) 和虚部 (Im)** (使用 `\alpha_1 = 0.2` 进行EMA):
        `Re_t = \alpha_1 \cdot (I2_t \cdot I2_{t-1} + Q2_t \cdot Q2_{t-1}) + (1 - \alpha_1) \cdot Re_{t-1}`
        `Im_t = \alpha_1 \cdot (I2_t \cdot Q2_{t-1} - Q2_t \cdot I2_{t-1}) + (1 - \alpha_1) \cdot Im_{t-1}`

    i.  **计算原始周期 (RawPeriod)**:
        如果 `Im_t` 和 `Re_t` 均不为零：
        `\text{phaseChangeRadian}_t = \operatorname{atan}(Im_t / Re_t)`
        `\text{phaseChangeDegree}_t = \text{phaseChangeRadian}_t \cdot (180^\circ / \pi)`
        如果 `\text{phaseChangeDegree}_t \neq 0`:
            `RawPeriod_t = 360^\circ / |\text{phaseChangeDegree}_t|` (代码中没有取绝对值，但周期通常为正)
        否则，`RawPeriod_t` 保持前值或设为默认值。

    j.  **约束和限制原始周期**:
        `tempPeriod = Period_{t-1}` (这里的 `Period_{t-1}` 是上一步迭代得到的、经过约束和平滑的周期)
        如果 `RawPeriod_t > 1.5 \cdot tempPeriod`，则 `RawPeriod_t = 1.5 \cdot tempPeriod`。
        如果 `RawPeriod_t < 0.67 \cdot tempPeriod`，则 `RawPeriod_t = 0.67 \cdot tempPeriod`。
        如果 `RawPeriod_t < 6`，则 `RawPeriod_t = 6`。
        如果 `RawPeriod_t > 50`，则 `RawPeriod_t = 50`。
        令 `LimitedRawPeriod_t` 为此步骤的结果。

    k.  **第一次平滑周期 (Period)** (使用 `\alpha_1 = 0.2` 进行EMA):
        `Period_t = \alpha_1 \cdot LimitedRawPeriod_t + (1 - \alpha_1) \cdot Period_{t-1}`
        (注意：TA-Lib C代码中 `Period_{t-1}` 在此步骤中实际是 `tempPeriod`，即上一步迭代的 `Period_t`。)

    l.  **最终平滑周期 (SmoothPeriod)** (使用 `\alpha_{sp} = 0.33` 进行EMA):
        `SmoothPeriod_t = \alpha_{sp} \cdot Period_t + (1 - \alpha_{sp}) \cdot SmoothPeriod_{t-1}`
        此 `SmoothPeriod_t` 即为当前时间点 `t` 的因子输出值。

4.  **更新状态**:
    保存当前计算的 `Period_t`, `SmoothPeriod_t`, `I2_t`, `Q2_t`, `Re_t`, `Im_t` 及其他需要滞后值的变量，供下一时间点计算使用。

【6. 备注与参数说明】

*   **参数**:
    *   希尔伯特滤波器系数: `a = 0.0962`, `b = 0.5769`。
    *   调整因子中的周期敏感度: `0.075`。
    *   调整因子中的固定偏移: `0.54`。
    *   EMA平滑系数1 (用于I2, Q2, Re, Im, Period):  `\alpha_1 = 0.2`。
    *   EMA平滑系数2 (用于SmoothPeriod): `\alpha_{sp} = 0.33`。
    *   周期下限: 6 个数据点。
    *   周期上限: 50 个数据点。
    *   周期相对变化上限: 1.5 倍前一周期。
    *   周期相对变化下限: 0.67 倍前一周期。
*   **数据预处理**:
    该因子对输入数据的质量较为敏感。建议使用平滑后的价格序列，尽管因子内部已包含一个4周期WMA平滑步骤。
*   **窗口期设定 (Lookback)**:
    由于多级滤波和滞后项的使用（最深的滞后达到12个周期，如jI的计算），以及多重EMA平滑，该因子需要较长的初始化/预热期。TA-Lib的实现指定了 `32 + 不稳定周期` 的回顾期。在实际应用中，应确保有足够历史数据（例如至少60-100个数据点）以获得较稳定的输出。
*   **自适应性**:
    通过 `AdjustedFactor_t = (0.075 \cdot Period_{t-1}) + 0.54`，希尔伯特滤波器的响应会根据先前检测到的周期长度动态调整，这是该算法的一个关键特性，使其能够适应市场周期的变化。
*   **奇偶日处理**:
    TA-Lib C源码中包含对奇数天和偶数天数据的区分处理（`DO_HILBERT_EVEN`, `DO_HILBERT_ODD`），这主要是为了优化圆形缓冲区的管理和计算效率，核心滤波思想对于奇偶日是相同的。在概念层面，可以理解为统一的滤波操作。

【因子信息结束】===============================================================