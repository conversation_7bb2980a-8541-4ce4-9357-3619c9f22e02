【因子信息开始】===============================================================

【因子编号和名称】

因子编号: 001
因子中文名称: 双序列数据乘积 (Vector Arithmetic Multiplication, MULT)

【1. 因子名称详情】

因子001: 双序列数据乘积 (Vector Arithmetic Multiplication, MULT)。该因子计算两个时间序列数据在对应时间点上的乘积。

【2. 核心公式】

假设有两个输入时间序列 \(S_1\) 和 \(S_2\)，在时间点 \(t\) 的值分别为 \(S_{1,t}\) 和 \(S_{2,t}\)。则该因子在时间点 \(t\) 的输出值 \(F_t\) 计算如下：

\[ F_t = S_{1,t} \times S_{2,t} \]

计算逻辑：
对于每一个时间点 \(t\)，取第一个输入序列在该点的值 \(S_{1,t}\) 和第二个输入序列在该点的值 \(S_{2,t}\)，将这两个值相乘，得到该时间点 \(t\) 的因子值 \(F_t\)。

【3. 变量定义】

*   \(S_{1,t}\)：第一个输入时间序列在时间点 \(t\) 的值。这可以代表任何数值型数据，如价格、成交量、或其他指标的计算结果。
*   \(S_{2,t}\)：第二个输入时间序列在时间点 \(t\) 的值。同样可以代表任何数值型数据。
*   \(F_t\)：因子在时间点 \(t\) 的输出值。
*   \(t\)：时间点或数据序列中的索引位置。

【4. 函数与方法说明】

*   \(\times\) (乘法)：标准的算术乘法运算。将两个数值相乘得到它们的积。

【5. 计算步骤】

1.  **数据准备**：
    *   获取第一个输入时间序列 \(S_1 = \{S_{1,1}, S_{1,2}, ..., S_{1,N}\}\)。
    *   获取第二个输入时间序列 \(S_2 = \{S_{2,1}, S_{2,2}, ..., S_{2,N}\}\)。
    *   确保两个序列具有相同的长度 \(N\)，并且每个对应位置 \(t\) 的数据点 (\(S_{1,t}\) 和 \(S_{2,t}\)) 是时间同步的。

2.  **逐元素计算乘积**：
    *   遍历时间序列中的每一个位置，从 \(t=1\) 到 \(t=N\)。
    *   对于每一个时间点 \(t\)：
        *   取出第一个序列的值 \(S_{1,t}\)。
        *   取出第二个序列的值 \(S_{2,t}\)。
        *   计算两者的乘积：\(F_t = S_{1,t} \times S_{2,t}\)。

3.  **输出因子序列**：
    *   将所有计算得到的 \(F_t\) 值组合起来，形成输出因子序列 \(F = \{F_1, F_2, ..., F_N\}\)。

**重要说明**：该因子本身不依赖于其他特定因子的计算结果，它是一个基础的算术运算，可以直接应用于任意两个数值型输入序列。如果输入序列 \(S_1\) 或 \(S_2\) 是其他因子的计算结果，则应先完成那些因子的计算。

【6. 备注与参数说明】

*   **输入数据类型**：输入序列 \(S_1\) 和 \(S_2\) 中的元素可以是任何实数（如价格、成交量、收益率或其他指标值）。
*   **窗口期 (Lookback Period)**：此因子本身没有窗口期的概念，因为它是一个逐点运算（pointwise operation）。计算 \(F_t\) 仅需要 \(S_{1,t}\) 和 \(S_{2,t}\)，不需要历史数据。因此，其“回溯期”为0。
*   **数据对齐**：至关重要的是，两个输入序列必须在时间上正确对齐。即 \(S_{1,t}\) 和 \(S_{2,t}\) 必须是同一时间周期的数据。
*   **应用场景**：此因子可用于多种目的，例如：
    *   计算金额（例如：价格序列 \(\times\) 成交量序列）。
    *   组合不同的信号（例如：一个趋势信号强度序列 \(\times\) 一个波动率调整因子序列）。
    *   对一个序列进行缩放或加权。
*   **参数**：该因子本身没有可调参数。其行为完全由输入的两个数据序列决定。

【因子信息结束】===============================================================