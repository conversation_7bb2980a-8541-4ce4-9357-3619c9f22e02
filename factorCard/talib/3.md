【因子信息开始】===============================================================

【因子编号和名称】

因子编号: (用户自定义) 加速带下轨 (Acceleration Bands Lower, ACCBANDS_L)

【1. 因子名称详情】

本因子描述加速带中的下轨部分，反映经过波动调整及SMA平滑后的下界价格水平，对识别潜在的卖出信号具有参考意义。

【2. 核心公式】

对于每个时间点 t：

1. 计算波动调整因子 (AF):
   先计算 TempAF_t = (H_t - L_t)/(H_t + L_t), 再得 AF_t = 4 × TempAF_t.

2. 初步下轨 (PLB):
   若 H_t + L_t ≠ 0, 则 PLB_t = L_t × (1 - AF_t);
   若 H_t + L_t = 0, 则 PLB_t = L_t.

3. 最终下轨 (LB):
   LB_t = SMA(PLB_t, N)

【3. 变量定义】

• LB_t: 在时间点 t 的加速带下轨值。
• L_t: 时间 t 的最低价。
• H_t: 时间 t 的最高价。
• N: 用于SMA计算的周期数 (optInTimePeriod，默认值为20)；
• AF_t: 波动调整因子；
• PLB_t: 经波动调整后得到的初步下轨值；
• SMA(X, N)_t: 对 X 在过去 N 个周期的简单移动平均值。

【4. 函数与方法说明】

• SMA(X, N): 通过对 N 个数据点进行平均计算，公式为
  SMA(X, N)_t = (X_t + X_{t-1} + … + X_{t-N+1})/N.

【5. 计算步骤】

1. 数据准备：收集每周期的 H_t, L_t, C_t 数据并确定 N.
2. 对每个时间点 t:
   a. 计算 SumHL_t = H_t + L_t.
   b. 若 SumHL_t ≠ 0, 计算 AF_t = 4*(H_t - L_t)/(H_t + L_t) 后，得到 PLB_t = L_t × (1 - AF_t);
      若 SumHL_t = 0, 则直接令 PLB_t = L_t.
3. 计算最终下轨 LB_t = SMA(PLB_t, N)（需足够数据点支持SMA计算）。

【6. 备注与参数说明】

• 参数 N 的选择影响SMA平滑效果，数据预处理需确保 H_t, L_t 数值有效。
• 本因子与上轨类似，在极端情况下（H_t+L_t=0）采用特殊的退化处理方式。



【7. 关联因子补充说明】

【关联因子信息开始】===============================================================

【因子编号和名称】

因子编号: (用户自定义) 加速带 (Acceleration Bands, ACCBANDS) - 上轨、中轨、下轨

【1. 因子名称详情】

该指标包含三个因子（或一条指标带的三个组成部分）：
*   加速带上轨 (Acceleration Bands Upper, ACCBANDS_U)
*   加速带中轨 (Acceleration Bands Middle, ACCBANDS_M)
*   加速带下轨 (Acceleration Bands Lower, ACCBANDS_L)

加速带（ACCBANDS）是由Price Headley在其著作《Big Trends in Trading》中提出的波动性指标，用于衡量市场的波动性并识别潜在的突破。它由上、中、下三条轨道线组成。

【2. 核心公式】

对于每个时间点 `t`：

1.  **中轨 (Middle Band, MB)**:
    `MB_t = SMA(C_t, N)`

2.  **波动调整因子 (Volatility Adjustment Factor, AF)**:
    首先计算一个临时的波动调整比例 `TempAF_t`：
    `TempAF_t = \frac{H_t - L_t}{H_t + L_t}`
    然后，波动调整因子 `AF_t` 为：
    `AF_t = 4 \times TempAF_t = \frac{4 \times (H_t - L_t)}{H_t + L_t}`
    *   特殊处理：如果 `H_t + L_t = 0`，则该周期的初步上轨直接取 `H_t`，初步下轨直接取 `L_t`，相当于 `AF_t` 在此步骤中的乘数效应为0。

3.  **初步上轨 (Preliminary Upper Band, PUB) 和初步下轨 (Preliminary Lower Band, PLB)**:
    *   如果 `H_t + L_t \neq 0`:
        `PUB_t = H_t \times (1 + AF_t)`
        `PLB_t = L_t \times (1 - AF_t)`
    *   如果 `H_t + L_t = 0`:
        `PUB_t = H_t`
        `PLB_t = L_t`

4.  **最终上轨 (Upper Band, UB)**:
    `UB_t = SMA(PUB_t, N)`

5.  **最终下轨 (Lower Band, LB)**:
    `LB_t = SMA(PLB_t, N)`

【3. 变量定义】

*   `UB_t`: 在时间点 `t` 的加速带上轨值。
*   `MB_t`: 在时间点 `t` 的加速带中轨值。
*   `LB_t`: 在时间点 `t` 的加速带下轨值。
*   `H_t`: 在时间点 `t` 的最高价 (High Price)。
*   `L_t`: 在时间点 `t` 的最低价 (Low Price)。
*   `C_t`: 在时间点 `t` 的收盘价 (Close Price)。
*   `N`: 时间周期参数，用于计算简单移动平均。在TA-LIB中对应 `optInTimePeriod`。
*   `AF_t`: 在时间点 `t` 计算得出的波动调整因子。
*   `PUB_t`: 在时间点 `t` 的初步上轨值（应用波动调整因子后，但未进行SMA平滑的值）。
*   `PLB_t`: 在时间点 `t` 的初步下轨值（应用波动调整因子后，但未进行SMA平滑的值）。
*   `SMA(X, N)_t`: 对时间序列 `X` 在时间点 `t` 计算的过去 `N`个周期的简单移动平均值。

【4. 函数与方法说明】

*   **SMA(X, N): 简单移动平均 (Simple Moving Average)**
    简单移动平均是在指定周期 `N` 内，对数据点 `X` 的算术平均值。对于时间点 `t`，其计算公式为：
    `SMA(X, N)_t = \frac{X_t + X_{t-1} + \dots + X_{t-N+1}}{N} = \frac{1}{N} \sum_{i=0}^{N-1} X_{t-i}`
    其中 `X_{t-i}` 表示从当前时间点 `t` 往前回溯 `i` 个周期的数据点值。计算SMA至少需要 `N` 个数据点。

【5. 计算步骤】

1.  **数据准备**:
    收集或准备时间序列数据，包括每个周期的最高价 (`H_t`)、最低价 (`L_t`) 和收盘价 (`C_t`)。
    确定参数 `N`（时间周期）。

2.  **计算中轨 (MB_t)**:
    对于每个时间点 `t`（从第 `N` 个数据点开始），计算收盘价 `C_t` 的 `N` 周期简单移动平均值：
    `MB_t = SMA(C_t, N) = \frac{1}{N} \sum_{i=0}^{N-1} C_{t-i}`

3.  **计算初步上轨序列 (PUB) 和初步下轨序列 (PLB)**:
    对于每个时间点 `t`：
    a.  计算当日最高价与最低价之和 `SumHL_t = H_t + L_t`。
    b.  如果 `SumHL_t` 不为零 (或非极小值，取决于具体实现对零的判断)：
        i.  计算波动调整因子 `AF_t = \frac{4 \times (H_t - L_t)}{H_t + L_t}`。
        ii. 计算初步上轨值 `PUB_t = H_t \times (1 + AF_t)`。
        iii.计算初步下轨值 `PLB_t = L_t \times (1 - AF_t)`。
    c.  如果 `SumHL_t` 为零：
        i.  初步上轨值 `PUB_t = H_t`。
        ii. 初步下轨值 `PLB_t = L_t`。
    将所有计算得到的 `PUB_t` 和 `PLB_t` 值按时间顺序排列，形成初步上轨时间序列和初步下轨时间序列。

4.  **计算最终上轨 (UB_t)**:
    对于每个时间点 `t`（从有效的初步上轨序列的第 `N` 个数据点开始），计算初步上轨序列 `PUB_t` 的 `N` 周期简单移动平均值：
    `UB_t = SMA(PUB_t, N) = \frac{1}{N} \sum_{i=0}^{N-1} PUB_{t-i}`

5.  **计算最终下轨 (LB_t)**:
    对于每个时间点 `t`（从有效的初步下轨序列的第 `N` 个数据点开始），计算初步下轨序列 `PLB_t` 的 `N` 周期简单移动平均值：
    `LB_t = SMA(PLB_t, N) = \frac{1}{N} \sum_{i=0}^{N-1} PLB_{t-i}`

    注意：实际计算中，为了使输出的上轨、中轨、下轨在时间上对齐，通常会确保有足够的回溯期数据。例如，要计算第 `k` 个点的ACCBANDS值，需要 `k` 点之前的 `N-1` 个价格数据，以及 `k` 点之前 `N-1` 个 `PUB` 和 `PLB` 数据（而 `PUB` 和 `PLB` 本身依赖于当日价格）。因此，ACCBANDS的第一个有效输出点会比原始价格序列的起始点晚 `N-1` 个周期（因SMA计算）。

【6. 备注与参数说明】

*   **时间周期 (N, `optInTimePeriod`)**:
    *   此参数用于所有三个轨道的简单移动平均计算。
    *   TA-LIB中该参数的默认值为20。
    *   可接受范围通常为2到100000。较短的周期使指标对价格变化更敏感，较长的周期则更平滑。
*   **数据预处理**:
    *   确保输入的价格数据（高、低、收）是有效的，没有缺失或异常值。
    *   指标的计算通常需要至少 `N` 个周期的数据才能产生第一个有效输出值。
*   **应用解读**:
    *   加速带的扩张通常表示市场波动性增加，收缩则表示波动性降低。
    *   价格突破上轨可能被视为买入信号，跌破下轨可能被视为卖出信号，尤其是在波动性增加（带宽变宽）的背景下。
    *   中轨可以作为趋势的参考线。
*   **零值处理**:
    *   当 `H_t + L_t = 0` 时（这在实际股票价格中几乎不可能发生，除非价格本身为0或数据错误），特定周期的初步上轨和下轨被简化为当时的最高价和最低价。这避免了除以零的错误，并提供了一个合理的退化行为。

【关联因子信息结束】===============================================================

【因子信息结束】===============================================================