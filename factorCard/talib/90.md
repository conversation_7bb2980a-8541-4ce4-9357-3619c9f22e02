【因子信息开始】===============================================================

【因子编号和名称】

因子编号: (待定) 负成交量指标 (Negative Volume Index, NVI)

【1. 因子名称详情】

因子1: 负成交量指标 (Negative Volume Index, NVI)

【2. 核心公式】

负成交量指标 (NVI) 的计算基于昨日的NVI值、今日和昨日的收盘价以及今日和昨日的成交量。

初始设定：$NVI_0 = \text{InitialValue}$ (通常为 1000)

对于后续的每一天 $t$ ($t > 0$):
$$
NVI_t =
\begin{cases}
NVI_{t-1} \times \left( 1 + \frac{C_t - C_{t-1}}{C_{t-1}} \right) & \text{如果 } V_t < V_{t-1} \\
NVI_{t-1} & \text{如果 } V_t \ge V_{t-1}
\end{cases}
$$
其中，如果 $C_{t-1}$ 为零，则当日价格变动百分比 $\frac{C_t - C_{t-1}}{C_{t-1}}$ 项的处理需要特别注意，通常这种情况应该避免或认为价格无变化（即百分比为0）。
公式也可以简化为：
$$
NVI_t =
\begin{cases}
NVI_{t-1} \times \frac{C_t}{C_{t-1}} & \text{如果 } V_t < V_{t-1} \text{ 且 } C_{t-1} \neq 0 \\
NVI_{t-1} & \text{如果 } V_t \ge V_{t-1} \text{ 或 } C_{t-1} = 0
\end{cases}
$$

【3. 变量定义】

*   $NVI_t$: 第 $t$ 期的负成交量指标值。
*   $NVI_{t-1}$: 第 $t-1$ 期的负成交量指标值。
*   $C_t$: 第 $t$ 期的收盘价。
*   $C_{t-1}$: 第 $t-1$ 期的收盘价。
*   $V_t$: 第 $t$ 期的成交量。
*   $V_{t-1}$: 第 $t-1$ 期的成交量。
*   $\text{InitialValue}$: NVI的初始值，通常设为1000。

【4. 函数与方法说明】

*   **价格百分比变化 (Percentage Price Change)**: 计算公式为 $\frac{C_t - C_{t-1}}{C_{t-1}}$。它衡量了从第 $t-1$ 期到第 $t$ 期收盘价的变化幅度。如果 $C_{t-1}$ 为0，这个比率是未定义的。在实际应用中，若 $C_{t-1}=0$，则通常认为价格变化为0。
*   **累积计算**: NVI的计算是累积的，每一期的NVI值都依赖于前一期的NVI值。
*   **条件判断**: 核心逻辑在于比较当前成交量 $V_t$ 和前一期成交量 $V_{t-1}$。只有当成交量下降时，NVI才会根据价格变动进行调整。

【5. 计算步骤】

1.  **数据准备**: 获取资产的每日收盘价 ($C$) 和成交量 ($V$) 的时间序列数据。
2.  **初始化**:
    *   设定NVI的初始值。对于时间序列的第一个有效数据点（或序列开始，通常是 $t=0$），设置 $NVI_0 = 1000$。在TA-Lib的实现中，通常第一个有输出的NVI值就是这个初始值，对应第一个输入数据点。然而，由于计算需要前一日的数据进行比较，严格意义上第一个可计算的NVI（即 $NVI_1$）是基于 $NVI_0$, $C_1, C_0, V_1, V_0$的。此处我们遵循常见的NVI定义，即第一个NVI值是基准值，从第二个周期开始应用条件公式。
    *   因此，对于输入的第一个周期 $(C_0, V_0)$，输出 $NVI_0 = 1000$。
3.  **迭代计算**: 从时间序列的第二个数据点开始 ($t=1, 2, 3, \dots, N-1$，其中N为数据点总数)：
    a.  获取当前期的收盘价 $C_t$ 和成交量 $V_t$。
    b.  获取上一期的收盘价 $C_{t-1}$、成交量 $V_{t-1}$ 和已计算的负成交量指标 $NVI_{t-1}$。
    c.  **对比成交量**: 比较 $V_t$ 和 $V_{t-1}$。
        i.  **如果 $V_t < V_{t-1}$ (当前成交量小于上一期成交量)**:
            *   确保 $C_{t-1} \neq 0$。
            *   如果 $C_{t-1} == 0$，则 $NVI_t = NVI_{t-1}$ （避免除零错误，认为价格无变化）。
            *   如果 $C_{t-1} \neq 0$，计算价格变动对NVI的调整：
                $NVI_t = NVI_{t-1} \times \left( 1 + \frac{C_t - C_{t-1}}{C_{t-1}} \right)$
                或等效地：
                $NVI_t = NVI_{t-1} \times \frac{C_t}{C_{t-1}}$
        ii. **如果 $V_t \ge V_{t-1}$ (当前成交量大于或等于上一期成交量)**:
            $NVI_t = NVI_{t-1}$
4.  **输出**: 记录每个计算周期 $t$ 的 $NVI_t$ 值，形成NVI的时间序列。

【6. 备注与参数说明】

*   **初始值**: NVI的起始值（例如1000）的选择是任意的，因为它主要用于观察其相对变化和趋势，而不是绝对数值。改变初始值会按比例缩放整个NVI序列，但不会改变其形态。
*   **数据有效性**: 需要确保输入数据（收盘价和成交量）的质量。特别地，如果出现前一期收盘价 $C_{t-1}$ 为零的情况，应有明确的处理逻辑以避免计算错误，通常处理为价格无变化。
*   **应用**: NVI 主要用于识别“聪明钱”的动向。理论上，当成交量萎缩时，主要是专业投资者或机构在活动，而散户参与较少。如果此时价格上涨，NVI随之上涨，可能预示着牛市；反之，如果价格下跌，NVI随之下跌，可能预示着熊市。NVI经常与其自身的移动平均线（例如255日均线）结合使用，NVI上穿其均线被视为买入信号，下穿则为卖出信号。
*   **无额外参数**: NVI本身没有可调的窗口期参数，其计算依赖于逐日的历史数据累积。
*   **TA-Lib Lookback**: 源码中 `TA_NVI_Lookback` 返回0，表明该函数计算第一个值不需要预热期，从提供的`startIdx`开始的第一个数据就可以产生输出（理论上需要前一个数据进行比较，但TA-Lib通常将第一个NVI值设为初始值，从第二个数据点开始应用完整公式）。

【因子信息结束】===============================================================