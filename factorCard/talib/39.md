【因子信息开始】===============================================================

【因子编号和名称】

因子编号: HT_PHASOR_02: 希尔伯特变换正交分量 (Hilbert Transform Quadrature Component, HT_QUAD)

【1. 因子名称详情】

因子2: 希尔伯特变换正交分量 (Hilbert Transform Quadrature Component, HT_QUAD)
该因子通过希尔伯特变换计算得到周期信号的正交分量，代表了相位偏移90度的部分。

【2. 核心公式】

关键步骤包括：
A. 价格平滑：
   SmoothedPrice_t = 0.1 × (4×Price_t + 3×Price_{t-1} + 2×Price_{t-2} + 1×Price_{t-3})
B. 动态调整因子：
   AdjustedPeriodFactor_t = 0.075 × Period_{t-1} + 0.54
C. 去趋势分量（Detrender）：
   Detrender_t = (a × SmoothedPrice_t + b × SmoothedPrice_{t-2} - b × SmoothedPrice_{t-4} - a × SmoothedPrice_{t-6}) × AdjustedPeriodFactor_t
D. 正交分量（HT_QUAD）：
   HT_QUAD_t = (a × Detrender_t + b × Detrender_{t-2} - b × Detrender_{t-4} - a × Detrender_{t-6}) × AdjustedPeriodFactor_t

【3. 变量定义】

• Price_t：t时刻的价格序列（例如收盘价）。
• SmoothedPrice_t：经过4周期加权移动平均处理的平滑价格。
• AdjustedPeriodFactor_t：动态调整因子，用于校正希尔伯特滤波器的响应。
• Period_t：当前周期值，经平滑更新。
• Detrender_t：经过第一次希尔伯特滤波后的去趋势分量。
• a, b：希尔伯特滤波器常数，分别定为0.0962和0.5769。

【4. 函数与方法说明】

包含如下说明：
– 加权移动平均（WMA）：依据4,3,2,1权重计算价格平滑
– 希尔伯特变换滤波器：实现90度相移，生成正交信号
– 指数平滑：用于中间变量的平滑更新
– 反正切函数：用于角度计算，结果以弧度表示

【5. 计算步骤】

1. 准备数据及初始化历史变量（包括Price_t、Period_t、I2, Q2, Re, Im等）
2. 计算价格平滑 SmoothedPrice_t
3. 根据历史周期计算动态调整因子 AdjustedPeriodFactor_t
4. 计算去趋势分量 Detrender_t
5. 计算正交分量：
   HT_QUAD_t = (a × Detrender_t + b × Detrender_{t-2} - b × Detrender_{t-4} - a × Detrender_{t-6}) × AdjustedPeriodFactor_t
6. 更新余下平滑和状态变量以便于下一时刻计算

【6. 备注与参数说明】

• 建议至少使用60个周期的数据初始化，以满足所有滞后及平滑条件。
• 参数（a, b, α_smooth等）为固定值，不建议调整。
• 本因子专注于捕获价格序列中正交成分，是后续周期分析与信号构造的重要输入。



【7. 关联因子补充说明】

【关联因子信息开始】===============================================================

【因子编号和名称】

因子编号: HT_PHASOR_01: 希尔伯特变换瞬时相位 (Hilbert Transform InPhase Component, HT_INPHASE)
因子编号: HT_PHASOR_02: 希尔伯特变换正交分量 (Hilbert Transform Quadrature Component, HT_QUAD)

【1. 因子名称详情】

*   因子1: 希尔伯特变换瞬时相位 (Hilbert Transform InPhase Component, HT_INPHASE)
*   因子2: 希尔伯特变换正交分量 (Hilbert Transform Quadrature Component, HT_QUAD)

这两个因子是通过希尔伯特变换计算得到的周期信号的两个主要分量，用于分析市场周期。瞬时相位分量代表了与原始信号同相的部分，而正交分量则代表了相位偏移90度的部分。

【2. 核心公式】

这两个因子共享一套核心计算逻辑，最终输出两个不同的序列。其计算过程涉及到价格平滑、多级希尔伯特变换滤波以及周期自适应调整。

**核心步骤常量:**
*   $a = 0.0962$
*   $b = 0.5769$
*   $\alpha_{smooth} = 0.2$ (用于周期、I2, Q2, Re, Im 的平滑)
*   $rad2Deg = \frac{180}{\pi}$ (弧度转角度的系数，$\pi \approx 4 \cdot \operatorname{atan}(1.0)$)

**A. 价格平滑 (4周期加权移动平均):**
$SmoothedPrice_t = 0.1 \times (4 \cdot Price_t + 3 \cdot Price_{t-1} + 2 \cdot Price_{t-2} + 1 \cdot Price_{t-3})$
(实际计算中通常采用增量方式以提高效率，但原理等同于此加权平均)

**B. 动态调整因子计算:**
$AdjustedPeriodFactor_t = (0.075 \times Period_{t-1}) + 0.54$
其中 $Period_{t-1}$ 是上一时刻计算得到的平滑主周期。

**C. 希尔伯特变换滤波 (通用形式):**
对于输入序列 $X_t$，其希尔伯特变换滤波后的输出 $Y_t$ 为：
$Y_t = (a \cdot X_t + b \cdot X_{t-2} - b \cdot X_{t-4} - a \cdot X_{t-6}) \times AdjustedPeriodFactor_t$

**D. 多级滤波计算:**
1.  **去趋势分量 (Detrender):**
    $Detrender_t = (a \cdot SmoothedPrice_t + b \cdot SmoothedPrice_{t-2} - b \cdot SmoothedPrice_{t-4} - a \cdot SmoothedPrice_{t-6}) \times AdjustedPeriodFactor_t$

2.  **瞬时相位分量 (HT_INPHASE - 因子1输出):**
    $HT\_INPHASE_t = Detrender_{t-3}$

3.  **正交分量 (HT_QUAD - 因子2输出):**
    $HT\_QUAD_t = (a \cdot Detrender_t + b \cdot Detrender_{t-2} - b \cdot Detrender_{t-4} - a \cdot Detrender_{t-6}) \times AdjustedPeriodFactor_t$

**E. 中间希尔伯特分量 (用于周期计算):**
1.  **jI 分量 (I1的希尔伯特变换):**
    $jI_t = (a \cdot HT\_INPHASE_t + b \cdot HT\_INPHASE_{t-2} - b \cdot HT\_INPHASE_{t-4} - a \cdot HT\_INPHASE_{t-6}) \times AdjustedPeriodFactor_t$

2.  **jQ 分量 (Q1的希尔伯特变换):**
    $jQ_t = (a \cdot HT\_QUAD_t + b \cdot HT\_QUAD_{t-2} - b \cdot HT\_QUAD_{t-4} - a \cdot HT\_QUAD_{t-6}) \times AdjustedPeriodFactor_t$

**F. 相位分量平滑 (I2, Q2):**
$I2_t = \alpha_{smooth} \cdot (HT\_INPHASE_t - jQ_t) + (1-\alpha_{smooth}) \cdot I2_{t-1}$
$Q2_t = \alpha_{smooth} \cdot (HT\_QUAD_t + jI_t) + (1-\alpha_{smooth}) \cdot Q2_{t-1}$

**G. 实部与虚部计算 (Re, Im - 用于周期测量):**
$Re_t = \alpha_{smooth} \cdot (I2_t \cdot I2_{t-1} + Q2_t \cdot Q2_{t-1}) + (1-\alpha_{smooth}) \cdot Re_{t-1}$
$Im_t = \alpha_{smooth} \cdot (I2_t \cdot Q2_{t-1} - Q2_t \cdot I2_{t-1}) + (1-\alpha_{smooth}) \cdot Im_{t-1}$

**H. 主周期计算与平滑 (Period):**
1.  **原始周期计算:**
    If $Im_t \neq 0$ AND $Re_t \neq 0$:
    $PeriodRaw_t = \frac{360}{\operatorname{atan}(Im_t / Re_t) \cdot rad2Deg}$
    Else:
    $PeriodRaw_t = Period_{t-1}$

2.  **周期边界限制与调整:**
    Let $PrevPeriod = Period_{t-1}$.
    $UpperLimit = 1.5 \times PrevPeriod$
    $LowerLimit = 0.67 \times PrevPeriod$
    $PeriodCandidate_t = \min(\max(PeriodRaw_t, LowerLimit), UpperLimit)$
    $PeriodCandidate_t = \min(\max(PeriodCandidate_t, 6), 50)$ (绝对上下限)

3.  **周期平滑:**
    $Period_t = \alpha_{smooth} \cdot PeriodCandidate_t + (1-\alpha_{smooth}) \cdot PrevPeriod$

【3. 变量定义】

*   $Price_t$: $t$时刻的输入价格序列 (例如收盘价)。
*   $SmoothedPrice_t$: $t$时刻的平滑价格。
*   $AdjustedPeriodFactor_t$: $t$时刻的周期调整因子，用于动态调整希尔伯特滤波器的响应。
*   $Period_t$: $t$时刻计算得到的平滑主导周期。
*   $Detrender_t$: $t$时刻的去趋势分量，是平滑价格经过第一级希尔伯特变换滤波的结果。
*   $HT\_INPHASE_t$: $t$时刻的瞬时相位分量，是 $Detrender_{t-3}$。这是**因子1的输出值**。
*   $HT\_QUAD_t$: $t$时刻的正交分量，是 $Detrender_t$ 经过第二级希尔伯特变换滤波的结果。这是**因子2的输出值**。
*   $jI_t$: $t$时刻 $HT\_INPHASE_t$ (即 $I1_t$) 的希尔伯特变换结果。
*   $jQ_t$: $t$时刻 $HT\_QUAD_t$ (即 $Q1_t$) 的希尔伯特变换结果。
*   $I2_t, Q2_t$: $t$时刻经过平滑的相位分量。
*   $Re_t, Im_t$: $t$时刻用于计算主周期的实部和虚部。
*   $a, b$: 希尔伯特变换滤波器的固定系数。
*   $\alpha_{smooth}$: 指数平滑系数。
*   $rad2Deg$: 弧度转换为角度的系数。
*   $\operatorname{atan}(\cdot)$: 反正切函数。

【4. 函数与方法说明】

1.  **加权移动平均 (Weighted Moving Average, WMA):**
    一个 N 周期的 WMA 计算公式为：
    $WMA_N(X)_t = \frac{\sum_{i=0}^{N-1} (N-i) \cdot X_{t-i}}{\sum_{i=0}^{N-1} (N-i)}$
    在本文中，使用的是4周期WMA，其权重为4, 3, 2, 1，分母为 $4+3+2+1=10$。
    $WMA_4(Price)_t = \frac{4 \cdot Price_t + 3 \cdot Price_{t-1} + 2 \cdot Price_{t-2} + 1 \cdot Price_{t-3}}{10}$

2.  **希尔伯特变换滤波器 (Hilbert Transform Filter):**
    这是一种数字滤波器，用于产生原始信号的近似90度相移版本。本文中采用的是一种特定的FIR（Finite Impulse Response，有限脉冲响应）滤波器结构，其形式为：
    $Output_t = (a \cdot Input_t + b \cdot Input_{t-2} - b \cdot Input_{t-4} - a \cdot Input_{t-6}) \times AdjustmentFactor_t$
    其中 $Input_t$ 是当前输入序列，$Input_{t-k}$ 是 $k$ 个周期前的输入值，$a$ 和 $b$ 是固定系数，$AdjustmentFactor_t$ 是一个动态调整因子。

3.  **指数平滑 (Exponential Smoothing):**
    一种递归计算方法，新值是当前值和前一期平滑值的加权平均：
    $SmoothedValue_t = \alpha \cdot CurrentValue_t + (1-\alpha) \cdot SmoothedValue_{t-1}$
    其中 $\alpha$ 是平滑系数，本文中 $\alpha_{smooth} = 0.2$。

4.  **反正切函数 ($\operatorname{atan}$):**
    标准数学函数，计算给定比率的角度。通常返回弧度值。

【5. 计算步骤】

1.  **数据准备与初始化:**
    *   获取输入价格序列 $Price_t$。
    *   设定希尔伯特变换系数 $a=0.0962$, $b=0.5769$。
    *   设定平滑系数 $\alpha_{smooth}=0.2$。
    *   计算 $rad2Deg = 180.0 / (4.0 \cdot \operatorname{atan}(1.0))$。
    *   初始化历史状态变量：
        *   $Period_0$ (初始主周期，例如可以设为0或一个经验值如20)。
        *   $I2_0, Q2_0, Re_0, Im_0$ 均设为0。
        *   需要足够的历史 $Price_t$ 数据来满足滤波器最大滞后项（如 $Price_{t-6}$、$Detrender_{t-6}$ 等）和 $Detrender_{t-3}$ 的计算。通常需要至少 `期数 + 6 (最大FIR滞后) + 3 (I1滞后) + 4 (WMA窗口) + 不稳定期调整` 个数据点作为起始计算的缓冲区。一个实践上的最小数据长度可能在30-60个周期以上。

2.  **迭代计算 (对于每个时间点 $t$ 从满足计算条件开始):**
    a. **价格平滑:**
       计算 $SmoothedPrice_t = 0.1 \times (4 \cdot Price_t + 3 \cdot Price_{t-1} + 2 \cdot Price_{t-2} + 1 \cdot Price_{t-3})$。确保有足够的历史 $Price$ 值。

    b. **周期调整因子:**
       $AdjustedPeriodFactor_t = (0.075 \times Period_{t-1}) + 0.54$。

    c. **计算去趋势分量 (Detrender):**
       $Detrender_t = (a \cdot SmoothedPrice_t + b \cdot SmoothedPrice_{t-2} - b \cdot SmoothedPrice_{t-4} - a \cdot SmoothedPrice_{t-6}) \times AdjustedPeriodFactor_t$。 (需要 $SmoothedPrice$ 历史值)。

    d. **计算瞬时相位分量 (HT_INPHASE - 因子1输出):**
       $HT\_INPHASE_t = Detrender_{t-3}$。 (需要 $Detrender$ 历史值)。这是因子 HT_PHASOR_01 在 $t$ 时刻的值。

    e. **计算正交分量 (HT_QUAD - 因子2输出):**
       $HT\_QUAD_t = (a \cdot Detrender_t + b \cdot Detrender_{t-2} - b \cdot Detrender_{t-4} - a \cdot Detrender_{t-6}) \times AdjustedPeriodFactor_t$。 (需要 $Detrender$ 历史值)。这是因子 HT_PHASOR_02 在 $t$ 时刻的值。

    f. **计算 jI 分量:**
       $jI_t = (a \cdot HT\_INPHASE_t + b \cdot HT\_INPHASE_{t-2} - b \cdot HT\_INPHASE_{t-4} - a \cdot HT\_INPHASE_{t-6}) \times AdjustedPeriodFactor_t$。 (需要 $HT\_INPHASE$ 历史值)。

    g. **计算 jQ 分量:**
       $jQ_t = (a \cdot HT\_QUAD_t + b \cdot HT\_QUAD_{t-2} - b \cdot HT\_QUAD_{t-4} - a \cdot HT\_QUAD_{t-6}) \times AdjustedPeriodFactor_t$。 (需要 $HT\_QUAD$ 历史值)。

    h. **平滑相位分量 I2, Q2:**
       $I2_t = \alpha_{smooth} \cdot (HT\_INPHASE_t - jQ_t) + (1-\alpha_{smooth}) \cdot I2_{t-1}$
       $Q2_t = \alpha_{smooth} \cdot (HT\_QUAD_t + jI_t) + (1-\alpha_{smooth}) \cdot Q2_{t-1}$

    i. **计算实部 Re 和虚部 Im:**
       $Re_t = \alpha_{smooth} \cdot (I2_t \cdot I2_{t-1} + Q2_t \cdot Q2_{t-1}) + (1-\alpha_{smooth}) \cdot Re_{t-1}$
       $Im_t = \alpha_{smooth} \cdot (I2_t \cdot Q2_{t-1} - Q2_t \cdot I2_{t-1}) + (1-\alpha_{smooth}) \cdot Im_{t-1}$

    j. **计算主周期 Period:**
       i.  If $Im_t \neq 0$ AND $Re_t \neq 0$: $PeriodRaw_t = \frac{360}{\operatorname{atan}(Im_t / Re_t) \cdot rad2Deg}$
           Else: $PeriodRaw_t = Period_{t-1}$
       ii. $UpperLimit = 1.5 \times Period_{t-1}$; $LowerLimit = 0.67 \times Period_{t-1}$
           $PeriodCandidate_t = \min(\max(PeriodRaw_t, LowerLimit), UpperLimit)$
           $PeriodCandidate_t = \min(\max(PeriodCandidate_t, 6), 50)$
       iii.$Period_t = \alpha_{smooth} \cdot PeriodCandidate_t + (1-\alpha_{smooth}) \cdot Period_{t-1}$

    k. **存储并更新状态:**
       记录 $HT\_INPHASE_t$ 和 $HT\_QUAD_t$。
       将当前计算的 $Period_t, I2_t, Q2_t, Re_t, Im_t$ 以及各级滤波器的输出值 (如 $SmoothedPrice_t, Detrender_t$) 保存，供下一时刻 $t+1$ 计算使用。

【6. 备注与参数说明】

*   **数据预处理与窗口期:** 该因子计算复杂，依赖多个历史数据点。建议使用至少60个以上的数据点进行初始化，以确保所有滤波器和平滑计算趋于稳定。其中，4周期WMA需要至少4个点，希尔伯特滤波器滞后6个点，瞬时相位分量 $I1$ 滞后3个点。这些加起来需要一个显著的回看窗口。原始TALib代码中提及的 `lookbackTotal = 32 + TA_GLOBALS_UNSTABLE_PERIOD`，其中32是固定部分，另一部分是不稳定期（通常用于EMA等平滑指标的稳定）。
*   **参数固定性:** 本因子描述中的希尔伯特变换系数 ($a, b$)，平滑系数 ($\alpha_{smooth}$)，周期限制 (6到50天)，以及周期调整公式中的系数 (0.075, 0.54) 均源自经典实现，通常不进行调整。
*   **核心思想:** 该方法旨在通过希尔伯特变换分解价格序列，得到其同相和正交分量，进而分析市场的周期性行为。周期测量部分是自适应的，这意味着滤波器的特性会根据市场当前估计的周期进行调整。
*   **输出解读:**
    *   $HT\_INPHASE_t$ (瞬时相位分量) 可以视为一个去趋势并经过滤波的价格序列。
    *   $HT\_QUAD_t$ (正交分量) 是 $Detrender_t$ 的90度相移版本。
    *   这两个分量可以用于构建更复杂的交易信号，例如通过它们的相对关系或相位角来判断趋势和转折点。

【关联因子信息结束】===============================================================

【因子信息结束】===============================================================