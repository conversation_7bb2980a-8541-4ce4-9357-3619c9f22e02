【因子信息开始】===============================================================

【因子编号和名称】

因子编号: FC026
因子中文名称: 周期内最小值索引 (Minimum Index, MININDEX)

【1. 因子名称详情】

因子中文名称: 周期内最小值索引
英文名称: Minimum Index
简称: MININDEX
功能: 在指定的时间周期（窗口）内，找出输入数据序列中的最小值，并返回该最小值在原始输入序列中的绝对索引位置。

【2. 核心公式】

对于时间序列 $Input$ 和给定的周期长度 $P$，在每个时间点 $t$（当 $t \ge P-1$ 时），计算 $MININDEX_t$ 的值：

$MININDEX_t = \underset{k \in [t-P+1, t]}{\arg\min} (Input_k)$

其中：
*   $\arg\min$ 表示取使表达式 $Input_k$ 达到最小值的参数 $k$（即索引）。
*   如果存在多个相同的最小值，则通常取最后一个（即索引最大者，对应于时间点 $t$ 或离 $t$ 最近的那个）最小值对应的索引。在所提供的源码实现中，如果当前值 `tmp` 与已记录的最低值 `lowest` 相等 (`tmp <= lowest`)，则当前索引 `today` 会成为新的 `lowestIdx`，因此它会优先选择时间上更近的索引。

【3. 变量定义】

*   $Input_t$: 输入时间序列在时刻 $t$ 的值（例如：每日收盘价）。这是一个一维数组或序列。
*   $P$: 计算周期长度（例如：14天，30天）。这是一个正整数，且 $P \ge 2$。
*   $t$: 当前数据点的时间索引（从0开始计数）。
*   $Input_k$: 输入时间序列在时刻 $k$ 的值。
*   $MININDEX_t$: 在时刻 $t$ 计算得到的，在包含当前时刻 $t$ 的、长度为 $P$ 的回顾窗口内，$Input$ 序列最小值的原始索引。

【4. 函数与方法说明】

*   $\arg\min_{x \in S} f(x)$: 此函数返回使函数 $f(x)$ 在集合 $S$ 中取得最小值的参数 $x$。在当前因子中，$f(x)$ 是输入序列的值 $Input_k$，$S$ 是时间窗口 $[t-P+1, t]$ 内的索引集合，返回的是使得 $Input_k$ 最小的那个索引 $k$。

【5. 计算步骤】

假设输入数据序列为 $Input = \{Input_0, Input_1, ..., Input_N\}$，周期长度为 $P$。

1.  **数据准备**:
    *   获取输入时间序列 $Input$。
    *   确定周期参数 $P$。

2.  **初始化**:
    *   计算需要 $P$ 个数据点才能产生第一个有效输出。因此，第一个可以计算 $MININDEX$ 的时间点是 $t = P-1$。
    *   输出序列 $MININDEX$ 的第一个有效索引对应输入序列的 $P-1$。
    *   `trailingIdx`: 滑动窗口的起始索引，初始为 $0$ (当 $t=P-1$ 时，`trailingIdx = (P-1) - P + 1 = 0`)。
    *   `lowestIdx`: 用于存储当前窗口内最小值的索引，初始时可以设为一个无效值或窗口内第一个元素的索引。
    *   `lowestVal`: 用于存储当前窗口内的最小值，初始时可以设为一个极大值或窗口内第一个元素的值。

3.  **迭代计算**: 对于从 $t = P-1$ 到 $N$ 的每个时间点（其中 $N$ 是输入序列的最后一个索引）：
    a.  **确定当前窗口**: 当前的计算窗口覆盖输入序列的索引范围是 $[trailingIdx, t]$，其中 $trailingIdx = t - P + 1$。
    b.  **更新最小值及其索引**:
        i.   **检查先前最小值的有效性**:
            如果上一个时间点记录的最小值的索引 `lowestIdx` 小于当前窗口的起始索引 `trailingIdx`（意味着上一个最小值已经滑出当前窗口），则需要在当前整个窗口内（从 `trailingIdx` 到 `t`）重新搜索最小值：
            1.  将 `lowestVal` 初始化为 $Input_{trailingIdx}$，`lowestIdx` 初始化为 `trailingIdx`。
            2.  遍历从 `trailingIdx + 1` 到 `t` 的所有索引 $i$。
            3.  如果 $Input_i < lowestVal$，则更新 $lowestVal = Input_i$ 且 $lowestIdx = i$。
                （注意：源码实现中，如果 $Input_i \le lowestVal$，则更新，这意味着如果出现相同最小值，会取较新的索引。）

        ii.  **与当前值比较**:
            如果先前记录的最小值仍在当前窗口内（即 `lowestIdx \ge trailingIdx`），则只需将当前时间点 $t$ 的值 $Input_t$ 与已记录的 `lowestVal` 进行比较：
            1.  如果 $Input_t \le lowestVal$，则更新 $lowestVal = Input_t$ 且 $lowestIdx = t$。

    c.  **存储结果**: 当前时间点 $t$ 的 $MININDEX_t$ 值为当前找到的 `lowestIdx`。
    d.  **滑动窗口**: 准备计算下一个时间点 $t+1$：`trailingIdx` 递增1（可以理解为在下一个 $t$ 计算时，自动变为 $(t+1)-P+1$）。

4.  **输出**: $MININDEX_t$ 序列包含了每个计算点对应的周期内最小值的原始索引。

**示例 (P=3):**
Input: `[10, 12, 8, 15, 7, 9]`

*   t=0: (不足P个点)
*   t=1: (不足P个点)
*   t=2: $Input_s = \{10, 12, 8\}$. Window $[0, 2]$. Min is 8 at index 2. $MININDEX_2 = 2$. `lowestVal=8`, `lowestIdx=2`.
*   t=3: $Input_s = \{12, 8, 15\}$. Window $[1, 3]$.
    *   `lowestIdx=2` is within window $[1, 3]$.
    *   $Input_3 = 15$. $15 \not\le 8$.
    *   Min remains 8 at index 2. $MININDEX_3 = 2$. `lowestVal=8`, `lowestIdx=2`.
*   t=4: $Input_s = \{8, 15, 7\}$. Window $[2, 4]$.
    *   `lowestIdx=2` is within window $[2, 4]$.
    *   $Input_4 = 7$. $7 \le 8$.
    *   Min is 7 at index 4. $MININDEX_4 = 4$. `lowestVal=7`, `lowestIdx=4`.
*   t=5: $Input_s = \{15, 7, 9\}$. Window $[3, 5]$.
    *   `lowestIdx=4` is within window $[3, 5]$.
    *   $Input_5 = 9$. $9 \not\le 7$.
    *   Min remains 7 at index 4. $MININDEX_5 = 4$. `lowestVal=7`, `lowestIdx=4`.

Output $MININDEX$: `[_, _, 2, 2, 4, 4]` (下划线表示无有效输出)

【6. 备注与参数说明】

*   **周期长度 (optInTimePeriod, P)**:
    *   此参数定义了回顾窗口的大小。
    *   源码中建议的范围是 2 到 100000。
    *   默认值为 30。
    *   选择不同的 $P$ 会影响因子的敏感度。较小的 $P$ 对近期变化更敏感，较大的 $P$ 则更平滑，反映长期趋势中的极值点位置。

*   **输出值**:
    *   因子输出的是索引值，而非数据本身的最小值。这个索引是相对于原始输入数据序列的绝对位置。
    *   第一个有效的输出值出现在输入序列的第 $P-1$ 个索引位置（假设索引从0开始）。

*   **数据预处理**:
    *   输入数据应为数值型。对于存在缺失值 (NaN) 的情况，该实现未明确定义行为，通常在实际应用中需要预先处理，例如插值填充或在计算最小值时忽略NaN。如果NaN被视为一个极小值或极大值，会影响结果。源码本身不处理NaN，依赖于浮点数比较的默认行为。

*   **与MIN函数的区别**:
    *   `MIN` 函数返回周期内的最小值本身。
    *   `MININDEX` 函数返回该最小值在原始序列中的索引。这对于需要定位极值点发生时间的场景非常有用。

【因子信息结束】===============================================================