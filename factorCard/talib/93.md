【因子信息开始】===============================================================

【因子编号和名称】

因子编号: PDM001
因子中文名称: 正向趋向移动 (Plus Directional Movement, +DM)

【1. 因子名称详情】

因子 PDM001: 正向趋向移动 (Plus Directional Movement, +DM)。该指标是 J<PERSON> <PERSON> Jr. 提出的趋向指标系统（Directional Movement System）的一部分，用于衡量价格在特定周期内向上运动的强度。

【2. 核心公式】

首先，定义单周期的向上移动值 (UpMove) 和向下移动值 (DownMove)：
`UpMove_t = H_t - H_{t-1}`
`DownMove_t = L_{t-1} - L_t`

然后，定义单周期的正向趋向移动 (`+DM1_t`)：
`+DM1_t = \\begin{cases} UpMove_t & \\text{if } UpMove_t > DownMove_t \\text{ and } UpMove_t > 0 \\\\ 0 & \\text{otherwise} \\end{cases}`

最后，使用 Wilder 的平滑方法计算 N 周期的正向趋向移动 (`+DM_{N,t}`)：
为了计算第一个 `+DM_{N}` 值 (记为 `+DM_{N, \text{initial}}`) ，通常取最初 N 个周期的 `+DM1` 值的和：
`+DM_{N, \text{initial}} = \\sum_{i=1}^{N} +DM1_i`
(注意: 为了计算第一个`+DM1_i`，需要 `H_i`, `L_i`, `H_{i-1}`, `L_{i-1}`。所以 `+DM1_1` 是使用 `H_1, L_1` 和 `H_0, L_0` 计算的。总共需要 `N+1` 个原始数据点来计算第一个 `+DM_N`。)

对于后续的 `+DM_{N,t}` 值，采用递归公式：
`+DM_{N,t} = +DM_{N,t-1} - \\frac{+DM_{N,t-1}}{N} + +DM1_t`

【3. 变量定义】

*   `H_t`: `t` 时刻的最高价。
*   `L_t`: `t` 时刻的最低价。
*   `H_{t-1}`: `t-1` 时刻（前一个周期）的最高价。
*   `L_{t-1}`: `t-1` 时刻（前一个周期）的最低价。
*   `UpMove_t`: `t` 时刻的向上价格突破幅度。
*   `DownMove_t`: `t` 时刻的向下价格突破幅度。
*   `+DM1_t`: `t` 时刻的单周期正向趋向移动值。
*   `+DM_{N,t}`: `t` 时刻的 N 周期平滑正向趋向移动值。
*   `N`: 计算周期（时间窗口长度），例如14天。
*   `i`: 求和或迭代过程中的计数器。
*   `+DM_{N, \text{initial}}`: N周期平滑正向趋向移动的初始值。
*   `+DM_{N,t-1}`: 前一时刻的N周期平滑正向趋向移动值。

【4. 函数与方法说明】

*   **向上移动值 (UpMove):**
    `UpMove_t = H_t - H_{t-1}`
    计算当前周期的最高价与前一周期最高价的差。如果为负或零，则表示没有向上净移动。

*   **向下移动值 (DownMove):**
    `DownMove_t = L_{t-1} - L_t`
    计算前一周期最低价与当前周期最低价的差。如果为负或零，则表示没有向下净移动。

*   **单周期正向趋向移动 (+DM1):**
    `+DM1_t = \\begin{cases} UpMove_t & \\text{if } UpMove_t > DownMove_t \\text{ and } UpMove_t > 0 \\\\ 0 & \\text{otherwise} \\end{cases}`
    这个值的计算逻辑是：
    1.  比较 `UpMove_t` 和 `DownMove_t`。
    2.  如果 `UpMove_t` 大于 `DownMove_t`，并且 `UpMove_t` 本身大于0（即当前最高价比昨日最高价高），则 `+DM1_t` 取值为 `UpMove_t`。
    3.  在所有其他情况下（包括 `UpMove_t` 不大于 `DownMove_t`，或者 `UpMove_t` 不大于0），`+DM1_t` 均为0。
    这确保了只有当上升幅度超过下降幅度，并且确实存在上升时，才记录正向趋向移动。

*   **Wilder平滑法:**
    这是一种特殊的指数移动平均方法，由 J. Welles Wilder Jr. 提出。其递推公式为：
    `SmoothedValue_t = SmoothedValue_{t-1} - \\frac{SmoothedValue_{t-1}}{N} + NewRawValue_t`
    等价于：
    `SmoothedValue_t = (1 - \\frac{1}{N}) \cdot SmoothedValue_{t-1} + NewRawValue_t` (注意：这与标准EMA的 `(1 - 1/N) \cdot Prev + (1/N) \cdot New` 不同，Wilder的公式中 `NewRawValue_t` 没有 `1/N` 的系数，或者说 `NewRawValue_t` 是直接加在调整后的前值上的)。
    该方法用于平滑 `+DM1_t` 序列，得到 `+DM_{N,t}`。第一个平滑值通常通过对前N个原始值求和（或在某些变体中求平均）来初始化。

【5. 计算步骤】

1.  **数据准备:**
    获取至少 `N+M` 个周期的历史高价 (`H`) 和低价 (`L`) 序列，其中 `N` 是计算周期，`M` 是计算第一个 `+DM1` 所需的额外前序数据点（至少1个，因为 `+DM1` 需要前一周期数据）。建议使用 `N+1` 个有效数据点来计算第一个 `+DM_N`。

2.  **计算单周期趋向移动系列 (`+DM1_t`):**
    对于从第二个数据点开始的每个周期 `t`（即 `t = 1, 2, ..., K`，其中 `K` 是数据点总数减1）：
    a.  计算 `UpMove_t = H_t - H_{t-1}`。
    b.  计算 `DownMove_t = L_{t-1} - L_t`。
    c.  根据条件判断并计算 `+DM1_t`：
        若 `UpMove_t > DownMove_t` 且 `UpMove_t > 0`，则 `+DM1_t = UpMove_t`。
        否则，`+DM1_t = 0`。
    这将产生一个 `+DM1` 的时间序列。

3.  **计算N周期平滑正向趋向移动 (`+DM_{N,t}`):**
    a.  **初始化第一个 `+DM_N` 值:**
        计算前 `N` 个 `+DM1_t` 值的总和 (即 `+DM1_1` 到 `+DM1_N`)。这个和作为第一个 `+DM_N` 值，记为 `+DM_{N,N}` (对应于时间点 `N`，使用了从 `t=0`到`t=N`的原始价格数据)。
        `+DM_{N,N} = \\sum_{j=1}^{N} +DM1_j`
    b.  **递归计算后续的 `+DM_N` 值:**
        对于 `t = N+1, N+2, ...` 的每个后续周期：
        `+DM_{N,t} = +DM_{N,t-1} - \\frac{+DM_{N,t-1}}{N} + +DM1_t`
        其中 `+DM_{N,t-1}` 是上一个周期计算得到的 N 周期平滑正向趋向移动值，`+DM1_t` 是当前周期的单周期正向趋向移动值。

    第一个有效的 `+DM_N` 输出值是在拥有 `N` 个 `+DM1` 值之后（即需要 `N+1` 个原始高低价数据点）。

【6. 备注与参数说明】

*   **参数 `N` (optInTimePeriod):**
    时间周期 `N` 是 +DM 计算中的核心参数。J. Welles Wilder Jr. 在其著作中通常推荐使用14作为周期长度（例如，14天或14个K线周期）。用户可以根据交易品种的特性和分析目的调整此参数。较短的周期会使 +DM 对价格变化更敏感，而较长的周期则会产生更平滑的结果。
*   **数据预处理:**
    输入数据为高价 (`inHigh`) 和低价 (`inLow`) 序列。确保数据清洁，无缺失值或异常值，以保证计算结果的准确性。
*   **用途:**
    +DM 指标通常不单独使用，而是作为构建更复杂指标（如平均趋向指数 ADX 和趋向指标 DI+/DI-）的基础组件。它帮助识别上升趋势的强度。
*   **起始期计算:**
    由于计算涉及前期数据和后续平滑，指标的初始值会有一段“预热期”。实际有效的 `+DM_N` 值会从第 `N` 个 `+DM1` 值计算出来之后开始（即需要 `N+1` 个原始价格数据点才能得到第一个 `+DM_N`）。

【因子信息结束】===============================================================