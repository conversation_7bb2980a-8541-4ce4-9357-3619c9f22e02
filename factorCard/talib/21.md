【因子信息开始】===============================================================

【因子编号和名称】

因子编号: BB003: 布林带下轨 (Bollinger Bands Lower Band, BBANDS_LOWER)

【1. 因子名称详情】

因子3: 布林带下轨 (Bollinger Bands Lower Band, BBANDS_LOWER)。该指标位于中轨之下，其距离由价格的标准差和用户定义的倍数决定。

【2. 核心公式】

\[ LB_t = MB_t - (K_{dn} \times \sigma_t) \]

其中，\( \sigma_t \) 是价格在周期 \( N \) 内的标准差。

【3. 变量定义】

*   \( LB_t \): 时刻 \( t \) 的布林带下轨值。
*   \( MB_t \): 时刻 \( t \) 的布林带中轨值 (计算方法参见因子 BB001)。
*   \( K_{dn} \): 下轨标准差倍数 (例如，2.0)。
*   \( \sigma_t \): 时刻 \( t \) 价格序列 \( P \) 在窗口期 \( N \) 内的标准差 (计算方法参见因子 BB002 的函数说明)。
*   \( P_t \): 时刻 \( t \) 的收盘价（或其他指定价格序列）。
*   \( N \): 计算移动平均和标准差的时间窗口期（与中轨 \( MB_t \) 使用的窗口期相同）。

【4. 函数与方法说明】

*   **布林带中轨 (MB)**: 计算方法参见因子 BB001 的描述。通常为价格在窗口期 \( N \) 内的某种移动平均值（例如SMA）。
    *   若使用简单移动平均 (SMA) 作为中轨：
        \[ MB_t = \text{SMA}(P, N)_t = \frac{1}{N} \sum_{i=0}^{N-1} P_{t-i} \]

*   **标准差 (\(\sigma\))**: 衡量数据点与其平均值之间离散程度的统计量。在此因子中，是针对原始价格序列 \( P \) 在窗口期 \( N \) 内计算的标准差.
    计算步骤（以总体标准差为例）：
    1.  计算窗口期 \( N \) 内价格 \( P \) 的简单移动平均 \( \text{SMA}(P,N)_t \).
        \[ \text{SMA}(P, N)_t = \frac{1}{N} \sum_{j=0}^{N-1} P_{t-j} \]
    2.  计算每个价格点与该SMA的离差平方：\( (P_{t-j} - \text{SMA}(P, N)_t)^2 \)。
    3.  计算离差平方的平均值（方差）：
        \[ \text{Var}_t = \frac{1}{N} \sum_{j=0}^{N-1} (P_{t-j} - \text{SMA}(P, N)_t)^2 \]
    4.  取方差的平方根得到标准差：
        \[ \sigma_t = \sqrt{\text{Var}_t} = \sqrt{\frac{1}{N} \sum_{j=0}^{N-1} (P_{t-j} - \text{SMA}(P, N)_t)^2} \]
    *注意：TA-LIB 计算标准差时，无论中轨 \(MB_t\) 选择何种MA类型，其标准差始终是基于原始价格数据 \(P\) 的 \( N \) 周期简单移动平均来计算偏差，然后求这些偏差的平方和的均值的平方根。

【5. 计算步骤】

1.  **数据准备**: 获取基础价格序列 \( P \) (例如：每日收盘价)。
2.  **参数设定**:
    *   确定时间窗口期 \( N \) (例如，TA-LIB中默认值为5，但经典应用中常用20)。
    *   确定移动平均类型 MAType (例如，选择SMA) 用于计算中轨。
    *   确定下轨标准差倍数 \( K_{dn} \) (例如，TA-LIB中默认值为2.0)。
3.  **计算中轨 (\(MB_t\))**:
    *   对于序列中的每个有效时刻 \( t \)：
        *   根据所选的MAType (例如SMA)，计算价格 \( P \) 在窗口期 \( N \) 内的移动平均值 \( MB_t \)。(详细计算参见 BB001 的步骤3)。
4.  **计算标准差 (\(\sigma_t\))**:
    *   对于序列中的每个有效时刻 \( t \)：
        a.  取当前及之前 \( N-1 \) 个周期的价格数据：\( P_t, P_{t-1}, \dots, P_{t-N+1} \)。
        b.  计算这 \( N \) 个价格数据的简单移动平均值: \( \text{SMA_for_StdDev}_t = \frac{1}{N} \sum_{j=0}^{N-1} P_{t-j} \) 。
        c.  计算每个价格点与 \( \text{SMA_for_StdDev}_t \) 的离差平方：\( (P_{t-j} - \text{SMA_for_StdDev}_t)^2 \) for \( j = 0, \dots, N-1 \) 。
        d.  计算离差平方的平均值 (方差): \( \text{Var}_t = \frac{1}{N} \sum_{j=0}^{N-1} (P_{t-j} - \text{SMA_for_StdDev}_t)^2 \) 。
        e.  标准差 \( \sigma_t = \sqrt{\text{Var}_t} \)。
        *优化说明：如果中轨 \(MB_t\) 本身就是用SMA计算的，则步骤4b中计算的 \( \text{SMA_for_StdDev}_t \) 与 \(MB_t\) 相同，可以直接使用。
5.  **计算下轨 (\(LB_t\))**:
    *   对于序列中的每个有效时刻 \( t \)：
        \[ LB_t = MB_t - (K_{dn} \times \sigma_t) \]

【6. 备注与参数说明】

*   **时间窗口期 (N, `optInTimePeriod`)**: 同中轨BB001。
*   **下轨标准差倍数 (\(K_{dn}\), `optInNbDevDn`)**: 默认值为2.0。此值决定了下轨与中轨的距离。较大的值会使通道变宽。通常 \(K_{up}\) 和 \(K_{dn}\) 取值相同，但也可以不同。
*   **移动平均类型 (MAType, `optInMAType`)**: 同中轨BB001。此参数影响中轨 \(MB_t\) 的计算。标准差 \(\sigma_t\) 的计算方法相对固定。



【7. 关联因子补充说明】

【关联因子信息开始】===============================================================

【因子编号和名称】

因子编号: BB001: 布林带中轨 (Bollinger Bands Middle Band, BBANDS_MIDDLE)
因子编号: BB002: 布林带上轨 (Bollinger Bands Upper Band, BBANDS_UPPER)
因子编号: BB003: 布林带下轨 (Bollinger Bands Lower Band, BBANDS_LOWER)

【关联因子信息结束】===============================================================

我们先来定义布林带中轨（BBANDS_MIDDLE），因为上轨和下轨都依赖于中轨的计算。

【关联因子信息开始】===============================================================

【因子编号和名称】

因子编号: BB001: 布林带中轨 (Bollinger Bands Middle Band, BBANDS_MIDDLE)

【1. 因子名称详情】

因子1: 布林带中轨 (Bollinger Bands Middle Band, BBANDS_MIDDLE)。该指标是布林带指标的中心线，通常为价格的移动平均线。

【2. 核心公式】

布林带中轨 \( MB_t \) 在时刻 \( t \) 的计算公式取决于所选的移动平均类型 (MAType)。最常见的是简单移动平均 (SMA)。

若使用简单移动平均 (SMA)：
\[ MB_t = \text{SMA}(P, N)_t = \frac{1}{N} \sum_{i=0}^{N-1} P_{t-i} \]

【3. 变量定义】

*   \( MB_t \): 时刻 \( t \) 的布林带中轨值。
*   \( P_t \): 时刻 \( t \) 的收盘价（或其他指定价格序列）。
*   \( N \): 计算移动平均的时间窗口期（例如，20天）。
*   \( \text{SMA}(P, N)_t \): 价格序列 \( P \) 在时刻 \( t \) 的 \( N \) 周期简单移动平均值。

【4. 函数与方法说明】

*   **简单移动平均 (Simple Moving Average, SMA)**:
    计算方式：对指定时间窗口期 \( N \) 内的数据点（例如价格）取算术平均值。
    公式：\( \text{SMA}(X, N)_t = \frac{X_t + X_{t-1} + \dots + X_{t-N+1}}{N} = \frac{1}{N} \sum_{i=0}^{N-1} X_{t-i} \)
    其中 \( X \) 是待计算的数据序列，\( N \) 是窗口期，\( X_{t-i} \) 是从当前时刻 \( t \) 往前回溯 \( i \) 个周期的数据点。

    *注意：除了SMA，布林带中轨也可以使用其他类型的移动平均，如指数移动平均 (EMA)、加权移动平均 (WMA) 等。TA-LIB支持多种MA类型，但经典布林带常使用SMA。*

【5. 计算步骤】

1.  **数据准备**: 获取基础价格序列 \( P \) (例如：每日收盘价)。
2.  **参数设定**:
    *   确定时间窗口期 \( N \) (例如，TA-LIB中默认值为5，但经典应用中常用20)。
    *   确定移动平均类型 MAType (例如，选择SMA)。
3.  **计算中轨**:
    *   对于序列中的每个有效时刻 \( t \)（即拥有至少 \( N \) 个前期数据的时刻）：
        *   取当前及之前 \( N-1 \) 个周期的价格数据：\( P_t, P_{t-1}, \dots, P_{t-N+1} \)。
        *   根据所选的MAType（此处以SMA为例）计算这些价格的移动平均值，得到 \( MB_t \)。
        \[ MB_t = \frac{P_t + P_{t-1} + \dots + P_{t-N+1}}{N} \]

【6. 备注与参数说明】

*   **时间窗口期 (N, `optInTimePeriod`)**: 默认值为5周期。实际应用中，20周期是较常用的设置。该值必须大于等于2。
*   **移动平均类型 (MAType, `optInMAType`)**: 默认值为简单移动平均 (SMA)。其他可选类型包括EMA, WMA, DEMA, TEMA, TRIMA, KAMA, MAMA, T3等。不同的移动平均类型会改变中轨的平滑度和响应速度。对于布林带，虽然中轨MA类型可选，但后续标准差的计算在TA-LIB实现中，总是基于价格数据的简单移动平均来计算其偏差。

【关联因子信息结束】===============================================================

接下来是布林带上轨（BBANDS_UPPER）。

【关联因子信息开始】===============================================================

【因子编号和名称】

因子编号: BB002: 布林带上轨 (Bollinger Bands Upper Band, BBANDS_UPPER)

【1. 因子名称详情】

因子2: 布林带上轨 (Bollinger Bands Upper Band, BBANDS_UPPER)。该指标位于中轨之上，其距离由价格的标准差和用户定义的倍数决定。

【2. 核心公式】

\[ UB_t = MB_t + (K_{up} \times \sigma_t) \]

其中，\( \sigma_t \) 是价格在周期 \( N \) 内的标准差。

【3. 变量定义】

*   \( UB_t \): 时刻 \( t \) 的布林带上轨值。
*   \( MB_t \): 时刻 \( t \) 的布林带中轨值 (计算方法参见因子 BB001)。
*   \( K_{up} \): 上轨标准差倍数 (例如，2.0)。
*   \( \sigma_t \): 时刻 \( t \) 价格序列 \( P \) 在窗口期 \( N \) 内的标准差。
*   \( P_t \): 时刻 \( t \) 的收盘价（或其他指定价格序列）。
*   \( N \): 计算移动平均和标准差的时间窗口期（与中轨 \( MB_t \) 使用的窗口期相同）。

【4. 函数与方法说明】

*   **布林带中轨 (MB)**: 计算方法参见因子 BB001 的描述。通常为价格在窗口期 \(N\) 内的某种移动平均值（例如SMA）。
    *   若使用简单移动平均 (SMA) 作为中轨：
        \[ MB_t = \text{SMA}(P, N)_t = \frac{1}{N} \sum_{i=0}^{N-1} P_{t-i} \]

*   **标准差 (\(\sigma\))**: 衡量数据点与其平均值之间离散程度的统计量。在此因子中，是针对原始价格序列 \( P \) 在窗口期 \( N \) 内计算的标准差。
    计算步骤（以总体标准差为例，因金融指标中常对窗口内数据视为一个小总体）：
    1.  计算窗口期 \( N \) 内价格 \( P \) 的简单移动平均 \( \text{SMA}(P,N)_t \)。
        \[ \text{SMA}(P, N)_t = \frac{1}{N} \sum_{j=0}^{N-1} P_{t-j} \]
    2.  计算每个价格点与该SMA的离差平方：\( (P_{t-j} - \text{SMA}(P, N)_t)^2 \)。
    3.  计算离差平方的平均值（方差）：
        \[ \text{Var}_t = \frac{1}{N} \sum_{j=0}^{N-1} (P_{t-j} - \text{SMA}(P, N)_t)^2 \]
    4.  取方差的平方根得到标准差：
        \[ \sigma_t = \sqrt{\text{Var}_t} = \sqrt{\frac{1}{N} \sum_{j=0}^{N-1} (P_{t-j} - \text{SMA}(P, N)_t)^2} \]
    *注意：TA-LIB 计算标准差时，无论中轨 \(MB_t\) 选择何种MA类型，其标准差始终是基于原始价格数据 \(P\) 的 \(N\) 周期简单移动平均来计算偏差，然后求这些偏差的平方和的均值的平方根。这确保了标准差的计算方法是一致的，即使中轨使用了如EMA等不同类型的MA。*

【5. 计算步骤】

1.  **数据准备**: 获取基础价格序列 \( P \) (例如：每日收盘价)。
2.  **参数设定**:
    *   确定时间窗口期 \( N \) (例如，TA-LIB中默认值为5，但经典应用中常用20)。
    *   确定移动平均类型 MAType (例如，选择SMA) 用于计算中轨。
    *   确定上轨标准差倍数 \( K_{up} \) (例如，TA-LIB中默认值为2.0)。
3.  **计算中轨 (\(MB_t\))**:
    *   对于序列中的每个有效时刻 \( t \)：
        *   根据所选的MAType (例如SMA)，计算价格 \( P \) 在窗口期 \( N \) 内的移动平均值 \( MB_t \)。(详细计算参见 BB001 的步骤3)。
4.  **计算标准差 (\(\sigma_t\))**:
    *   对于序列中的每个有效时刻 \( t \)：
        a.  取当前及之前 \( N-1 \) 个周期的价格数据：\( P_t, P_{t-1}, \dots, P_{t-N+1} \)。
        b.  计算这 \( N \) 个价格数据的简单移动平均值: \( \text{SMA_for_StdDev}_t = \frac{1}{N} \sum_{j=0}^{N-1} P_{t-j} \)。
        c.  计算每个价格点与 \( \text{SMA_for_StdDev}_t \) 的离差平方：\( (P_{t-j} - \text{SMA_for_StdDev}_t)^2 \) for \( j = 0, \dots, N-1 \)。
        d.  计算离差平方的平均值 (方差): \( \text{Var}_t = \frac{1}{N} \sum_{j=0}^{N-1} (P_{t-j} - \text{SMA_for_StdDev}_t)^2 \)。
        e.  标准差 \( \sigma_t = \sqrt{\text{Var}_t} \)。
        *优化说明：如果中轨 \(MB_t\) 本身就是用SMA计算的，则步骤4b中计算的 \( \text{SMA_for_StdDev}_t \) 与 \(MB_t\) 相同，可以直接使用。TA-LIB C源码中 `INT_stddev_using_precalc_ma` 利用了此优化。*
5.  **计算上轨 (\(UB_t\))**:
    *   对于序列中的每个有效时刻 \( t \)：
        \[ UB_t = MB_t + (K_{up} \times \sigma_t) \]

【6. 备注与参数说明】

*   **时间窗口期 (N, `optInTimePeriod`)**: 同中轨BB001。
*   **上轨标准差倍数 (\(K_{up}\), `optInNbDevUp`)**: 默认值为2.0。此值决定了上轨与中轨的距离。较大的值会使通道变宽。
*   **移动平均类型 (MAType, `optInMAType`)**: 同中轨BB001。此参数影响中轨 \(MB_t\) 的计算。标准差 \(\sigma_t\) 的计算方法相对固定（基于价格的N周期SMA计算偏差）。

【关联因子信息结束】===============================================================

最后是布林带下轨（BBANDS_LOWER）。

【关联因子信息开始】===============================================================

【因子编号和名称】

因子编号: BB003: 布林带下轨 (Bollinger Bands Lower Band, BBANDS_LOWER)

【1. 因子名称详情】

因子3: 布林带下轨 (Bollinger Bands Lower Band, BBANDS_LOWER)。该指标位于中轨之下，其距离由价格的标准差和用户定义的倍数决定。

【2. 核心公式】

\[ LB_t = MB_t - (K_{dn} \times \sigma_t) \]

其中，\( \sigma_t \) 是价格在周期 \( N \) 内的标准差。

【3. 变量定义】

*   \( LB_t \): 时刻 \( t \) 的布林带下轨值。
*   \( MB_t \): 时刻 \( t \) 的布林带中轨值 (计算方法参见因子 BB001)。
*   \( K_{dn} \): 下轨标准差倍数 (例如，2.0)。
*   \( \sigma_t \): 时刻 \( t \) 价格序列 \( P \) 在窗口期 \( N \) 内的标准差 (计算方法参见因子 BB002 的函数说明)。
*   \( P_t \): 时刻 \( t \) 的收盘价（或其他指定价格序列）。
*   \( N \): 计算移动平均和标准差的时间窗口期（与中轨 \( MB_t \) 使用的窗口期相同）。

【4. 函数与方法说明】

*   **布林带中轨 (MB)**: 计算方法参见因子 BB001 的描述。通常为价格在窗口期 \(N\) 内的某种移动平均值（例如SMA）。
    *   若使用简单移动平均 (SMA) 作为中轨：
        \[ MB_t = \text{SMA}(P, N)_t = \frac{1}{N} \sum_{i=0}^{N-1} P_{t-i} \]

*   **标准差 (\(\sigma\))**: 衡量数据点与其平均值之间离散程度的统计量。在此因子中，是针对原始价格序列 \( P \) 在窗口期 \( N \) 内计算的标准差。
    计算步骤（以总体标准差为例）：
    1.  计算窗口期 \( N \) 内价格 \( P \) 的简单移动平均 \( \text{SMA}(P,N)_t \)。
        \[ \text{SMA}(P, N)_t = \frac{1}{N} \sum_{j=0}^{N-1} P_{t-j} \]
    2.  计算每个价格点与该SMA的离差平方：\( (P_{t-j} - \text{SMA}(P, N)_t)^2 \)。
    3.  计算离差平方的平均值（方差）：
        \[ \text{Var}_t = \frac{1}{N} \sum_{j=0}^{N-1} (P_{t-j} - \text{SMA}(P, N)_t)^2 \]
    4.  取方差的平方根得到标准差：
        \[ \sigma_t = \sqrt{\text{Var}_t} = \sqrt{\frac{1}{N} \sum_{j=0}^{N-1} (P_{t-j} - \text{SMA}(P, N)_t)^2} \]
    *注意：TA-LIB 计算标准差时，无论中轨 \(MB_t\) 选择何种MA类型，其标准差始终是基于原始价格数据 \(P\) 的 \(N\) 周期简单移动平均来计算偏差，然后求这些偏差的平方和的均值的平方根。*

【5. 计算步骤】

1.  **数据准备**: 获取基础价格序列 \( P \) (例如：每日收盘价)。
2.  **参数设定**:
    *   确定时间窗口期 \( N \) (例如，TA-LIB中默认值为5，但经典应用中常用20)。
    *   确定移动平均类型 MAType (例如，选择SMA) 用于计算中轨。
    *   确定下轨标准差倍数 \( K_{dn} \) (例如，TA-LIB中默认值为2.0)。
3.  **计算中轨 (\(MB_t\))**:
    *   对于序列中的每个有效时刻 \( t \)：
        *   根据所选的MAType (例如SMA)，计算价格 \( P \) 在窗口期 \( N \) 内的移动平均值 \( MB_t \)。(详细计算参见 BB001 的步骤3)。
4.  **计算标准差 (\(\sigma_t\))**:
    *   对于序列中的每个有效时刻 \( t \)：
        a.  取当前及之前 \( N-1 \) 个周期的价格数据：\( P_t, P_{t-1}, \dots, P_{t-N+1} \)。
        b.  计算这 \( N \) 个价格数据的简单移动平均值: \( \text{SMA_for_StdDev}_t = \frac{1}{N} \sum_{j=0}^{N-1} P_{t-j} \)。
        c.  计算每个价格点与 \( \text{SMA_for_StdDev}_t \) 的离差平方：\( (P_{t-j} - \text{SMA_for_StdDev}_t)^2 \) for \( j = 0, \dots, N-1 \)。
        d.  计算离差平方的平均值 (方差): \( \text{Var}_t = \frac{1}{N} \sum_{j=0}^{N-1} (P_{t-j} - \text{SMA_for_StdDev}_t)^2 \)。
        e.  标准差 \( \sigma_t = \sqrt{\text{Var}_t} \)。
        *优化说明：如果中轨 \(MB_t\) 本身就是用SMA计算的，则步骤4b中计算的 \( \text{SMA_for_StdDev}_t \) 与 \(MB_t\) 相同，可以直接使用。*
5.  **计算下轨 (\(LB_t\))**:
    *   对于序列中的每个有效时刻 \( t \)：
        \[ LB_t = MB_t - (K_{dn} \times \sigma_t) \]

【6. 备注与参数说明】

*   **时间窗口期 (N, `optInTimePeriod`)**: 同中轨BB001。
*   **下轨标准差倍数 (\(K_{dn}\), `optInNbDevDn`)**: 默认值为2.0。此值决定了下轨与中轨的距离。较大的值会使通道变宽。通常 \(K_{up}\) 和 \(K_{dn}\) 取值相同，但也可以不同。
*   **移动平均类型 (MAType, `optInMAType`)**: 同中轨BB001。此参数影响中轨 \(MB_t\) 的计算。标准差 \(\sigma_t\) 的计算方法相对固定。

【关联因子信息结束】===============================================================

【因子信息结束】===============================================================