【因子信息开始】===============================================================

【因子编号和名称】

因子编号: MA003
因子中文名称: 三角移动平均线 (Triangular Moving Average, TRIMA)

【1. 因子名称详情】

因子1: 三角移动平均线 (Triangular Moving Average, TRIMA)。它是一种加权移动平均线，与赋予最新数据点更大权重的其他移动平均线（如WMA或EMA）不同，TRIMA赋予窗口期内中间部分的数据点最大的权重，权重分布呈三角形。

【2. 核心公式】

三角移动平均线 (TRIMA) 通常被理解为对原始数据进行两次简单移动平均 (SMA) 计算。其计算方式根据周期 $N$ 的奇偶性有所不同：

1.  **当周期 $N$ 为奇数时:**
    设 $M = \frac{N+1}{2}$。
    $TRIMA_t(P, N) = SMA(SMA(P, M)_t, M)$

2.  **当周期 $N$ 为偶数时:**
    设 $M_1 = \frac{N}{2}$ 且 $M_2 = \frac{N}{2} + 1$。
    $TRIMA_t(P, N) = SMA(SMA(P, M_1)_t, M_2)$

其中 $P$ 是输入时间序列数据（例如价格）。

从加权平均的角度来看，TRIMA 的权重分布是三角形的：
*   **当周期 $N$ 为奇数时:**
    令 $k = (N+1)/2$。权重从1开始，线性增加到 $k$，然后线性减少到1。
    $TRIMA_t = \frac{\sum_{i=0}^{N-1} w_{i+1} \cdot P_{t-N+1+i}}{\sum_{j=1}^{k} j + \sum_{j=1}^{k-1} j} = \frac{\sum_{i=0}^{N-1} w_{i+1} \cdot P_{t-N+1+i}}{k^2}$
    权重 $w_j$ 序列为： $1, 2, \ldots, k-1, k, k-1, \ldots, 2, 1$。

*   **当周期 $N$ 为偶数时:**
    令 $k = N/2$。权重从1开始，线性增加到 $k$，保持 $k$ 两次，然后线性减少到1。
    $TRIMA_t = \frac{\sum_{i=0}^{N-1} w_{i+1} \cdot P_{t-N+1+i}}{\sum_{j=1}^{k} j + \sum_{j=1}^{k} j} = \frac{\sum_{i=0}^{N-1} w_{i+1} \cdot P_{t-N+1+i}}{k(k+1)}$
    权重 $w_j$ 序列为： $1, 2, \ldots, k-1, k, k, k-1, \ldots, 2, 1$。

【3. 变量定义】

*   $P_t$: 在时间点 $t$ 的输入数据值（例如，收盘价）。
*   $N$: TRIMA的计算周期（时间窗口长度）。
*   $SMA(X, K)_t$: 时间序列 $X$ 在时间点 $t$ 的 $K$ 周期简单移动平均值。
*   $TRIMA_t(P, N)$: 输入数据 $P$ 在时间点 $t$ 的 $N$ 周期三角移动平均值。
*   $M, M_1, M_2$: 根据 $N$ 的奇偶性计算的内部SMA周期。
*   $w_i$: 应用于第 $i$ 个数据点（在窗口期内）的权重。

【4. 函数与方法说明】

*   **简单移动平均 (Simple Moving Average, SMA)**:
    对于一个时间序列 $X = \{X_1, X_2, \ldots, X_t, \ldots \}$ 和一个周期 $K$，在时间点 $t$ 的SMA计算如下：
    $SMA(X, K)_t = \frac{1}{K} \sum_{i=0}^{K-1} X_{t-i} = \frac{X_t + X_{t-1} + \ldots + X_{t-K+1}}{K}$
    它计算的是最近 $K$ 个数据点的算术平均值。

【5. 计算步骤】

TRIMA的计算通常通过对数据进行两次SMA来实现，这是最经典和公认的方法：

1.  **确定内部SMA周期**:
    *   输入原始时间序列数据 $P = \{P_1, P_2, \ldots, P_T\}$ 和TRIMA周期 $N$。
    *   如果 $N$ 是奇数，则设 $M = (N+1)/2$。第一个SMA的周期 $K_1 = M$，第二个SMA的周期 $K_2 = M$。
    *   如果 $N$ 是偶数，则设 $M_1 = N/2$ 和 $M_2 = N/2 + 1$。第一个SMA的周期 $K_1 = M_1$，第二个SMA的周期 $K_2 = M_2$。

2.  **计算第一个（内部）SMA序列**:
    对原始数据序列 $P$ 应用周期为 $K_1$ 的SMA。
    对于每个时间点 $t$（从第 $K_1$ 个数据点开始），计算：
    $SMA1_t = \frac{1}{K_1} \sum_{i=0}^{K_1-1} P_{t-i}$
    这将生成一个新的时间序列 $SMA1 = \{SMA1_{K_1}, SMA1_{K_1+1}, \ldots, SMA1_T\}$。

3.  **计算第二个（外部）SMA序列**:
    对步骤2中生成的 $SMA1$ 序列应用周期为 $K_2$ 的SMA。
    对于每个时间点 $t$（从 $SMA1$ 序列的第 $K_2$ 个数据点开始，对应原始数据序列的第 $K_1+K_2-1$ 个点），计算：
    $TRIMA_t = SMA(SMA1, K_2)_t = \frac{1}{K_2} \sum_{i=0}^{K_2-1} SMA1_{t-i}$
    这是最终的TRIMA值。

    **例如，计算 $TRIMA(P, 5)$:**
    $N=5$ (奇_数)，所以 $M = (5+1)/2 = 3$。$K_1=3, K_2=3$。
    a. 计算 $P$ 的3周期SMA，得到 $SMA1$。
       $SMA1_3 = (P_1+P_2+P_3)/3$
       $SMA1_4 = (P_2+P_3+P_4)/3$
       $SMA1_5 = (P_3+P_4+P_5)/3$
       ...
    b. 计算 $SMA1$ 的3周期SMA，得到 $TRIMA$。
       $TRIMA_5 = (SMA1_3+SMA1_4+SMA1_5)/3$ (这是第一个TRIMA值，对应原始数据P_5)
       $TRIMA_6 = (SMA1_4+SMA1_5+SMA1_6)/3$
       ...

    第一个有效的TRIMA值需要 $N-1$ 个历史数据点。例如，TRIMA(period=5) 的第一个值在索引4处（0-indexed）或第5个数据点处。

【6. 备注与参数说明】

*   **参数 `optInTimePeriod` ($N$)**: 指TRIMA的计算周期。在TA-Lib中，该值通常介于2到100000之间，默认值为30。
*   **数据预处理**: 计算TRIMA需要至少 $N-1$ 个历史数据点才能产生第一个有效值。因此，在使用时应确保有足够的前置数据。
*   **特性**: TRIMA 对价格数据具有平滑作用，由于其权重设计，它比简单移动平均线 (SMA) 对价格变化的反应更为平缓，并且滞后性也可能更大。中间部分的权重最大，意味着它对窗口期中段的价格变动最为敏感。
*   **实现优化**: 尽管TRIMA的概念是SMA的SMA，实际的计算（如TA-Lib中的实现）可能会采用更直接的加权求和方法，并通过迭代更新来优化计算效率，而不是显式地进行两次SMA计算。这种优化方法通过维护几个累加和（代码中的`numerator`, `numeratorSub`, `numeratorAdd`）来高效地计算每个新时间点的TRIMA值，其数学结果与SMA的SMA定义等价。这种直接加权求和的权重和分母（代码中的`factor`）根据周期的奇偶性有所不同：
    *   奇数周期 $N$: 设 $k = (N+1)/2$。分母（总权重）为 $k^2$。
    *   偶数周期 $N$: 设 $k = N/2$。分母（总权重）为 $k(k+1)$。
    这种优化在输出结果上与经典的SMA套SMA方法是一致的。

【因子信息结束】===============================================================