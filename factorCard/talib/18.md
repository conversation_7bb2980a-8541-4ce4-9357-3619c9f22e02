【因子信息开始】===============================================================

【因子编号和名称】

因子编号: F001: 平均价格 (Average Price, AVGPRICE)

【1. 因子名称详情】

因子F001: 平均价格 (Average Price, AVGPRICE)，该因子计算每个时间周期内开盘价、最高价、最低价和收盘价的算术平均值。

【2. 核心公式】

因子在特定时间点 `t` 的计算公式如下：

`AVGPRICE_t = \frac{O_t + H_t + L_t + C_t}{4}`

其中：
*   `AVGPRICE_t` 代表在 `t` 时刻计算得到的平均价格。
*   `O_t` 代表在 `t` 时刻的开盘价。
*   `H_t` 代表在 `t` 时刻的最高价。
*   `L_t` 代表在 `t` 时刻的最低价。
*   `C_t` 代表在 `t` 时刻的收盘价。

该公式计算的是每个时间周期（例如一天、一小时）内开盘价、最高价、最低价和收盘价这四个价格点的算术平均值。

【3. 变量定义】

*   `AVGPRICE_t`: 因子在 `t` 时刻的输出值，代表该时刻的平均价格。
*   `O_t`: 输入变量，表示 `t` 时刻的资产开盘价格 (Open Price)。
*   `H_t`: 输入变量，表示 `t` 时刻的资产最高价格 (High Price)。
*   `L_t`: 输入变量，表示 `t` 时刻的资产最低价格 (Low Price)。
*   `C_t`: 输入变量，表示 `t` 时刻的资产收盘价格 (Close Price)。
*   `t`: 时间指标，代表计算因子的具体时间点或周期（例如，第 `t` 根K线）。

【4. 函数与方法说明】

该因子计算仅使用了基本的算术运算：
*   **加法 (`+`)**: 用于将指定时间点 `t` 的开盘价、最高价、最低价和收盘价进行累加。
*   **除法 (`/`)**: 用于将四个价格之和除以4，以得到算术平均值。

没有涉及复杂或特殊的统计函数或方法。

【5. 计算步骤】

1.  **数据准备**: 对于分析范围内的每一个时间周期 `t` (例如，日K线、小时K线等)，获取该周期的四个基本价格数据：
    *   开盘价 (`O_t`)
    *   最高价 (`H_t`)
    *   最低价 (`L_t`)
    *   收盘价 (`C_t`)

2.  **价格求和**: 对当前时间周期 `t` 的四个价格数据进行求和：
    `SumPrices_t = O_t + H_t + L_t + C_t`

3.  **计算平均价格**: 将步骤2中得到的总和除以4，得到当前时间周期 `t` 的平均价格 `AVGPRICE_t`：
    `AVGPRICE_t = SumPrices_t / 4`

4.  **输出**: 将计算得到的 `AVGPRICE_t` 作为该时间周期 `t` 的因子值。

该因子的计算是逐点的，即每个时间点 `t` 的平均价格仅依赖于该时间点 `t` 的开盘价、最高价、最低价和收盘价，不依赖于历史数据或其他因子。

【6. 备注与参数说明】

*   **参数**: 此因子本身没有可调参数（如时间窗口长度）。它直接使用每个时间周期的四个基本价格数据进行计算。
*   **窗口期**: 该因子是逐K线（或逐时间周期）计算的，不涉及滑动窗口的概念。其计算仅依赖于当前K线（或当前周期）的OHLC数据。因此，其计算所需的回溯期（lookback period）为0。
*   **数据预处理**: 输入的开盘价、最高价、最低价、收盘价数据应为有效的数值。在实际应用中，如果原始数据存在缺失或明显的异常值，应在计算前进行适当的清洗或插值处理。
*   **适用性**: 平均价格提供了一个周期内价格活动中心的简单度量。它可以被视为该周期内"典型"价格的一种表示。
*   **思想抽离**: 该因子的核心思想是取一个交易周期内四个关键价格点（开、高、低、收）的简单平均，作为该周期价格水平的一个代表值。

【因子信息结束】===============================================================