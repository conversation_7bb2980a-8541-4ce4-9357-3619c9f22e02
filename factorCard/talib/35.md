【因子信息开始】===============================================================

【因子编号和名称】

因子编号: MATH001: 向下取整 (Vector Floor, FLOOR)

【1. 因子名称详情】

因子名称：向下取整 (Vector Floor, FLOOR)。该因子对输入数据序列中的每一个值执行向下取整操作。

【2. 核心公式】

对于输入序列中的每一个值 $X_t$，其对应的因子值为 $Y_t$：

$Y_t = \lfloor X_t \rfloor$

其中：
*   $\lfloor \cdot \rfloor$ 表示向下取整函数（floor function），即返回不大于输入值的最大整数。

【3. 变量定义】

*   $X_t$: 输入时间序列在时刻 $t$ 的原始值。这可以是一个价格序列、另一个指标的计算结果或其他任何数值序列。
*   $Y_t$: 输出时间序列（即因子值）在时刻 $t$ 的值，它是 $X_t$ 向下取整的结果。

【4. 函数与方法说明】

*   **向下取整函数 ($\lfloor x \rfloor$)**:
    *   **定义**: 对于任意实数 $x$，向下取整函数 $\lfloor x \rfloor$ 返回小于或等于 $x$ 的最大整数。
    *   **计算方法**:
        *   如果 $x$ 是整数，则 $\lfloor x \rfloor = x$。
        *   如果 $x$ 是正数但非整数，则 $\lfloor x \rfloor$ 是 $x$ 的整数部分。例如, $\lfloor 3.14 \rfloor = 3$, $\lfloor 7.8 \rfloor = 7$。
        *   如果 $x$ 是负数但非整数，则 $\lfloor x \rfloor$ 是小于 $x$ 的第一个整数。例如, $\lfloor -3.14 \rfloor = -4$, $\lfloor -7.8 \rfloor = -8$。

【5. 计算步骤】

1.  **数据准备**:
    获取一个输入数值序列 $X = \{X_1, X_2, \dots, X_N\}$，其中 $N$ 是序列的长度。

2.  **逐元素计算**:
    遍历输入序列 $X$ 中的每一个元素 $X_t$ (其中 $t$ 从 1 到 $N$):
    对当前的 $X_t$ 应用向下取整函数，计算得到 $Y_t = \lfloor X_t \rfloor$。

3.  **形成输出序列**:
    将所有计算得到的 $Y_t$ 值组合起来，形成输出序列（因子值序列）$Y = \{Y_1, Y_2, \dots, Y_N\}$。

**示例**:
假设输入序列 $X = \{2.3, -1.7, 5.0, -4.0, 0.9\}$。
计算步骤如下：
*   $Y_1 = \lfloor 2.3 \rfloor = 2$
*   $Y_2 = \lfloor -1.7 \rfloor = -2$
*   $Y_3 = \lfloor 5.0 \rfloor = 5$
*   $Y_4 = \lfloor -4.0 \rfloor = -4$
*   $Y_5 = \lfloor 0.9 \rfloor = 0$
最终输出序列 $Y = \{2, -2, 5, -4, 0\}$。

【6. 备注与参数说明】

*   **参数**: 该因子本身没有可配置的参数（如窗口期）。它是一个逐点操作。
*   **窗口期**: 该操作不依赖于历史数据窗口，每个点的计算都是独立的。因此，其计算所需的回溯期 (lookback period) 为 0。
*   **数据类型**: 输入数据可以是任何实数。输出结果将是整数。
*   **应用场景**:
    *   数据预处理：可以将连续型数据转换为离散型整数数据。
    *   特征工程：作为构建更复杂因子的中间步骤，例如，当需要将数值按整数区间划分时。
    *   某些特定算法的需求：某些算法可能要求输入为整数。
*   **与截断 (Truncation) 的区别**:
    *   对于正数，向下取整和截断（直接去掉小数部分）结果相同。例如 $\lfloor 3.7 \rfloor = 3$，trunc(3.7) = 3。
    *   对于负数，两者结果不同。例如 $\lfloor -3.7 \rfloor = -4$，而 trunc(-3.7) = -3。本因子实现的是向下取整。

【因子信息结束】===============================================================