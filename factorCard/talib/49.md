【因子信息开始】===============================================================

【因子编号和名称】

因子编号: F001
因子中文名称: 线性回归斜率 (Linear Regression Slope, LRS)

【1. 因子名称详情】

F001: 线性回归斜率 (Linear Regression Slope, LRS)
该因子计算在给定时间周期内，输入数据（通常为价格）的线性回归线的斜率。它衡量了数据在特定时期内的主要趋势方向和变化速率。

【2. 核心公式】

对于一组 N 个数据点 \( (x_i, y_i) \)，其中 \( y_i \) 是观测值（例如价格），\( x_i \) 是对应的时间索引。简单线性回归模型为 \( y = mx + b \)，其中 \( m \) 是斜率，\( b \) 是截距。
在计算该因子时，我们通常取最近 \(P\) 期的数据。将时间索引设为 \( k = 0, 1, \ldots, P-1 \)，其中 \( k=0 \) 对应最新数据点，\( k=P-1 \) 对应 \(P-1\) 个周期前的数据点。
设在时间点 \(t\)：
  - \( \text{Value}_{t-k} \) 为 \(t-k\) 时刻的输入数据值（例如，收盘价）。
  - 时间索引 \(x_k = k\)。

则在 \(t\) 时刻的线性回归斜率 \( \text{LRS}_t \) 计算公式如下：

$$ \text{LRS}_t = m = \frac{P \sum_{k=0}^{P-1} (k \cdot \text{Value}_{t-k}) - \left(\sum_{k=0}^{P-1} k\right) \left(\sum_{k=0}^{P-1} \text{Value}_{t-k}\right)}{P \sum_{k=0}^{P-1} (k^2) - \left(\sum_{k=0}^{P-1} k\right)^2} $$

【3. 变量定义】

*   \( P \): 时间周期 (对应源码中的 `optInTimePeriod`)，表示用于计算线性回归的观测窗口大小，即K线的数量。
*   \( \text{Value}_{t-k} \): 在 \( t-k \) 时刻的输入数据值。例如，若 \( k=0 \), \( \text{Value}_t \) 是当前周期的收盘价；若 \( k=1 \), \( \text{Value}_{t-1} \) 是前一个周期的收盘价，以此类推。这些是回归分析中的因变量 \( y \)。
*   \( k \): 时间序列的索引，代表从当前数据点 (\(k=0\)) 到窗口中最老数据点 (\(k=P-1\)) 的时间距离（以周期计）。这些 \( k \) 值构成了回归分析中的自变量 \( x \)。
*   \( \text{LRS}_t \): 在 \( t \) 时刻计算得到的线性回归斜率。

【4. 函数与方法说明】

公式中用到的各项求和可以预先计算或有解析解：
*   \( \sum_{k=0}^{P-1} k \): 从 0 到 \( P-1 \) 的整数和。
    $$ \text{SumX} = \sum_{k=0}^{P-1} k = \frac{(P-1)P}{2} $$
    (对应源码中的 `SumX`)

*   \( \sum_{k=0}^{P-1} k^2 \): 从 0 到 \( P-1 \) 的整数平方和。
    $$ \text{SumXSqr} = \sum_{k=0}^{P-1} k^2 = \frac{(P-1)P(2P-1)}{6} $$
    (对应源码中的 `SumXSqr`)

*   \( \sum_{k=0}^{P-1} \text{Value}_{t-k} \): 在时间窗口 \( [t-(P-1), t] \) 内的 \( P \) 个输入数据值的总和。
    $$ \text{SumY} = \sum_{k=0}^{P-1} \text{Value}_{t-k} $$
    (对应源码中的 `SumY`)

*   \( \sum_{k=0}^{P-1} (k \cdot \text{Value}_{t-k}) \): 在时间窗口内，每个时间索引 \( k \) 与其对应输入数据值 \( \text{Value}_{t-k} \) 乘积的总和。
    $$ \text{SumXY} = \sum_{k=0}^{P-1} (k \cdot \text{Value}_{t-k}) $$
    (对应源码中的 `SumXY`)

【5. 计算步骤】

1.  **数据准备**: 获取输入时间序列数据 \( \text{Value} \)。该序列通常是资产的收盘价。
2.  **参数设定**: 确定时间周期 \( P \)。
3.  **计算 \( \text{SumX} \) (时间索引之和)**:
    $$ \text{SumX} = \frac{P(P-1)}{2} $$
4.  **计算 \( \text{SumXSqr} \) (时间索引平方和)**:
    $$ \text{SumXSqr} = \frac{P(P-1)(2P-1)}{6} $$
5.  **迭代计算**: 对于时间序列中的每个有效计算点 \( t \) (即从第 \( P-1 \) 个数据点开始，若数据从0开始索引):
    a.  **选取数据窗口**: 选择从 \( t-(P-1) \) 到 \( t \) 的 \( P \) 个 \( \text{Value} \) 数据点。
    b.  **计算 \( \text{SumY} \) (窗口内数据值之和)**:
        $$ \text{SumY}_t = \sum_{k=0}^{P-1} \text{Value}_{t-k} $$
        (在源码中，循环 `for( i = optInTimePeriod; i-- != 0; ) { SumY += inReal[today - i]; }` 实现此功能，其中 `i` 从 `P-1` 倒数至 `0`，`today-i` 遍历了窗口中的所有价格。)
    c.  **计算 \( \text{SumXY} \) (窗口内时间和数据乘积之和)**:
        $$ \text{SumXY}_t = \sum_{k=0}^{P-1} (k \cdot \text{Value}_{t-k}) $$
        (在源码中，循环 `for( i = optInTimePeriod; i-- != 0; ) { SumXY += (double)i * inReal[today - i]; }` 实现此功能，其中 `i` 扮演了 \( k \) 的角色，`inReal[today-i]` 扮演了 \( \text{Value}_{t-k} \) 的角色。)
    d.  **计算分母项 (\( \text{Denominator} \))**: (源码中使用 `Divisor`)
        $$ \text{Denominator} = P \cdot \text{SumXSqr} - (\text{SumX})^2 $$
        (源码中的 `Divisor = SumX * SumX - optInTimePeriod * SumXSqr` 是标准分母的负值。因此，后续公式分子分母同乘-1与标准形式等价。)
    e.  **计算分子项 (\( \text{Numerator} \))**:
        $$ \text{Numerator} = P \cdot \text{SumXY}_t - \text{SumX} \cdot \text{SumY}_t $$
    f.  **计算斜率 \( \text{LRS}_t \)**:
        $$ \text{LRS}_t = \frac{\text{Numerator}}{\text{Denominator}} $$
        (源码中为 `( optInTimePeriod * SumXY - SumX * SumY) / Divisor`，由于 `Divisor` 是标准分母的负值，这等效于 `( P * SumXY - SumX * SumY) / - (P * SumXSqr - (SumX)^2 )`，即标准公式。)
6.  **输出结果**: 将计算得到的 \( \text{LRS}_t \) 作为该时间点 \(t\) 的因子值。

【6. 备注与参数说明】

*   **时间周期 \( P \)**: 这是因子最重要的参数。
    *   常见的选择有14、20、30、50等。
    *   周期越短，斜率对近期价格变化越敏感，波动性越大。
    *   周期越长，斜率越平滑，更能反映长期趋势方向。
    *   周期 \( P \) 必须至少为2，因为拟合一条直线至少需要两个数据点。
*   **输入数据 (Value)**: 通常使用收盘价。也可以使用开盘价、最高价、最低价、中点价 \(((\text{High} + \text{Low})/2)\)、典型价格 \(((\text{High} + \text{Low} + \text{Close})/3)\) 或加权收盘价 \(((\text{High} + \text{Low} + 2 \cdot \text{Close})/4)\) 等其他时间序列数据。
*   **数据预处理**: 一般不需要特殊的数据预处理。但应注意输入数据中不应包含 NaN（非数字）值，因为它们会干扰计算。
*   **计算起点**: 由于计算 \( P \) 周期的线性回归斜率需要 \( P \) 个数据点，因此因子序列的最初 \( P-1 \) 个周期将不会产生有效的因子值。例如，如果使用日线数据和 \( P=14 \)，则第一个有效的 LRS 值将在第14天计算出来，它代表了从第1天到第14天的价格数据的回归斜率。

【因子信息结束】===============================================================