【因子信息开始】===============================================================

【因子编号和名称】

因子编号: T3_001 (此编号为示例，您可以自行定义)
因子中文名称: 三重指数移动平均 (Triple Exponential Moving Average, T3)

【1. 因子名称详情】

因子的完整名称为“三重指数移动平均”，英文名称为“Triple Exponential Moving Average”，简称“T3”。该指标由 Tim Tillson 提出，旨在提供比传统指数移动平均线（EMA）更平滑且滞后更小的移动平均线。

【2. 核心公式】

T3 指标的计算基于多次指数移动平均（EMA）的组合。其最终计算公式可以表示为：
$T3_t = c_1 \cdot EMA^6_t(P) + c_2 \cdot EMA^5_t(P) + c_3 \cdot EMA^4_t(P) + c_4 \cdot EMA^3_t(P)$

其中，系数 $c_1, c_2, c_3, c_4$ 的计算如下：
令 $v$ 为体积因子 (Volume Factor, `optInVFactor`)
$c_1 = -v^3$
$c_2 = 3v^2(1+v) = 3v^2 + 3v^3$
$c_3 = -3v(1+v)^2 = -3v - 6v^2 - 3v^3$
$c_4 = (1+v)^3 = 1 + 3v + 3v^2 + v^3$

$EMA^k_t(P)$ 表示对价格序列 $P$ 进行 $k$ 次迭代的指数移动平均。
$EMA^1_t(P) = EMA_t(P)$
$EMA^2_t(P) = EMA_t(EMA^1(P))$
$EMA^3_t(P) = EMA_t(EMA^2(P))$
$EMA^4_t(P) = EMA_t(EMA^3(P))$
$EMA^5_t(P) = EMA_t(EMA^4(P))$
$EMA^6_t(P) = EMA_t(EMA^5(P))$

单次指数移动平均（EMA）的计算公式为：
$EMA_t(X) = \alpha \cdot X_t + (1-\alpha) \cdot EMA_{t-1}(X)$
其中 $\alpha = \frac{2}{N+1}$，$N$ 为时间周期。

【3. 变量定义】
*   $P_t$: $t$ 时刻的输入价格（例如收盘价）。
*   $N$: 计算EMA时使用的时间周期 (对应源码中的 `optInTimePeriod`)。
*   $v$: 体积因子 (Volume Factor)，用于调整平滑度和响应度 (对应源码中的 `optInVFactor`)。
*   $\alpha$: EMA的平滑系数，$\alpha = \frac{2}{N+1}$。
*   $X_t$: 在计算EMA时，$t$ 时刻的输入序列值。
*   $EMA_t(X)$: 输入序列 $X$ 在 $t$ 时刻的指数移动平均值。
*   $EMA^k_t(P)$: 价格序列 $P$ 在 $t$ 时刻的第 $k$ 次迭代指数移动平均值。为了方便，下文也用 $e1_t, e2_t, \dots, e6_t$ 分别表示 $EMA^1_t(P), EMA^2_t(P), \dots, EMA^6_t(P)$。
*   $c_1, c_2, c_3, c_4$: T3计算中使用的系数。
*   $T3_t$: $t$ 时刻的T3指标值。

【4. 函数与方法说明】
*   **指数移动平均 (EMA - Exponential Moving Average):**
    *   计算公式: $EMA_t(X) = \alpha \cdot X_t + (1-\alpha) \cdot EMA_{t-1}(X)$。
    *   平滑系数: $\alpha = \frac{2}{N+1}$，其中 $N$ 是时间周期。
    *   **初始化**: 第一个 $EMA$ 值 ($EMA_0(X)$) 通常采用前 $N$ 个 $X$ 值的简单移动平均 (SMA) 来计算：$EMA_0(X) = \frac{1}{N} \sum_{i=1}^{N} X_i$。对于序列中的后续点，则使用递归公式。
    *   在T3的计算中，会进行多次EMA计算，每一次EMA的输入是前一次EMA的结果。例如，$EMA^2(P)$ 是对 $EMA^1(P)$ 序列再进行一次EMA计算。

*   **简单移动平均 (SMA - Simple Moving Average):**
    *   计算公式: $SMA_t(X, N) = \frac{1}{N} \sum_{i=t-N+1}^{t} X_i$
    *   用途: 用于EMA的初始化。

【5. 计算步骤】
1.  **数据准备**: 获取输入价格序列 $P = \{P_1, P_2, \dots, P_M\}$。
2.  **参数设定**:
    *   选择时间周期 $N$ (例如 `optInTimePeriod = 5`)。
    *   选择体积因子 $v$ (例如 `optInVFactor = 0.7`)。
3.  **计算平滑系数**: $\alpha = \frac{2}{N+1}$。
4.  **计算迭代EMA序列**:
    *   **计算 $e1_t = EMA_t(P)$**:
        *   初始化 $e1_0$: 计算 $P$ 序列前 $N$ 个值的SMA。
        *   对于后续的 $t > 0$: $e1_t = \alpha \cdot P_t + (1-\alpha) \cdot e1_{t-1}$。
    *   **计算 $e2_t = EMA_t(e1)$**:
        *   初始化 $e2_0$: 计算 $e1$ 序列前 $N$ 个值的SMA。
        *   对于后续的 $t > 0$: $e2_t = \alpha \cdot e1_t + (1-\alpha) \cdot e2_{t-1}$。
    *   **计算 $e3_t = EMA_t(e2)$**:
        *   初始化 $e3_0$: 计算 $e2$ 序列前 $N$ 个值的SMA。
        *   对于后续的 $t > 0$: $e3_t = \alpha \cdot e2_t + (1-\alpha) \cdot e3_{t-1}$。
    *   **计算 $e4_t = EMA_t(e3)$**:
        *   初始化 $e4_0$: 计算 $e3$ 序列前 $N$ 个值的SMA。
        *   对于后续的 $t > 0$: $e4_t = \alpha \cdot e3_t + (1-\alpha) \cdot e4_{t-1}$。
    *   **计算 $e5_t = EMA_t(e4)$**:
        *   初始化 $e5_0$: 计算 $e4$ 序列前 $N$ 个值的SMA。
        *   对于后续的 $t > 0$: $e5_t = \alpha \cdot e4_t + (1-\alpha) \cdot e5_{t-1}$。
    *   **计算 $e6_t = EMA_t(e5)$**:
        *   初始化 $e6_0$: 计算 $e5$ 序列前 $N$ 个值的SMA。
        *   对于后续的 $t > 0$: $e6_t = \alpha \cdot e5_t + (1-\alpha) \cdot e6_{t-1}$。
    *   *注意*: 每次EMA的计算都需要足够的前序数据点。例如，计算第一个 $e1$ 值需要 $N$ 个 $P$ 值。计算第一个 $e2$ 值需要 $N$ 个 $e1$ 值，这对应于 $N + (N-1)$ 个原始 $P$ 值。依此类推，计算第一个 $e6$ 值需要 $N+5(N-1) = 6N-5$ 个原始 $P$ 值。TA-Lib中的 `lookbackTotal = 6 * (optInTimePeriod - 1)` 考虑了这一点（不包括首次SMA所需的`optInTimePeriod`中的1个，因为它计算的是EMA的滞后，加上不稳定周期）。严格意义上，第一个有效的T3值需要 $6(N-1)+1$ 个基础数据点来完成所有6次EMA的迭代计算（其中第一次SMA是 $(N-1)+1 = N$ 个点，后续5次EMA每次增加 $N-1$ 个所需点）。

5.  **计算T3系数**:
    *   $c_1 = -v^3$
    *   $c_2 = 3v^2 + 3v^3$
    *   $c_3 = -3v - 6v^2 - 3v^3$
    *   $c_4 = 1 + 3v + 3v^2 + v^3$
6.  **计算T3指标值**:
    对于每个有效的时间点 $t$ (即 $e3_t, e4_t, e5_t, e6_t$ 都有有效值之后)：
    $T3_t = c_1 \cdot e6_t + c_2 \cdot e5_t + c_3 \cdot e4_t + c_4 \cdot e3_t$

【6. 备注与参数说明】
*   **时间周期 (N, `optInTimePeriod`)**: 默认值为5。取值范围通常在2到100000之间。较短的周期使T3对价格变化更敏感，较长的周期则更平滑。
*   **体积因子 (v, `optInVFactor`)**: 默认值为0.7。取值范围为0到1。这个因子控制T3的平滑程度和滞后性。
    *   当 $v=0$ 时，T3 退化为 $EMA^3(P)$ (三次指数移动平均，这里的 $EMA^3$ 指的是迭代三次的EMA，而非通常意义上的TEMA指标，TEMA指标是 $3 \cdot EMA(P) - 3 \cdot EMA(EMA(P)) + EMA(EMA(EMA(P)))$)。
    *   当 $v=1$ 时，T3 退化为 $DEMA(DEMA(DEMA(P)))$ (三次应用广义双重指数移动平均GDEMA，其中GDEMA的参数为1)。
    *   Tim Tillson 推荐 $v=0.7$ 作为通用值，以平衡平滑度和滞后。
*   **初始值问题**: 由于多次EMA计算，T3指标在数据序列的早期会有一段“不稳定”或“无值”的时期。所需的最小数据点数量大约为 $6 \times (N-1) + 1$。TA-Lib内部会处理这个lookback期。
*   **与EMA3/TEMA的区别**: T3常被称为“三重指数移动平均”，但其计算方法与另一种也叫“三重指数移动平均”（EMA3或TRIX的组成部分，或者TEMA指标本身）的指标不同。T3是基于Tim Tillson的广义DEMA概念迭代三次得到的。
*   “体积因子”(Volume Factor)的名称可能有些误导，因为它在TA-Lib的这个实现中并不直接使用成交量数据，而是一个用户定义的参数，用于调整EMA的加权行为。

【因子信息结束】===============================================================