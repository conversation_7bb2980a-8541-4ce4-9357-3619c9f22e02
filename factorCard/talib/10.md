【因子信息开始】===============================================================

【因子编号和名称】

因子编号: FC_002: 百分比价格振荡器 (Percentage Price Oscillator, PPO)

【1. 因子名称详情】

因子FC_002: 百分比价格振荡器 (Percentage Price Oscillator, PPO)。该指标通过计算两个不同周期的价格移动平均线之间的差值，并将其表示为长期移动平均线的百分比，来衡量价格动量。

【2. 核心公式】

对于给定的时间点 $t$，PPO的计算公式如下：
$$ \text{PPO}_t = \left( \frac{\text{MA}_{\text{fast}}(P, N_{\text{fast}})_t - \text{MA}_{\text{slow}}(P, N_{\text{slow}})_t}{\text{MA}_{\text{slow}}(P, N_{\text{slow}})_t} \right) \times 100 $$

若 $\text{MA}_{\text{slow}}(P, N_{\text{slow}})_t = 0$，则 $\text{PPO}_t = 0$。

其中：
*   $\text{MA}_{\text{fast}}(P, N_{\text{fast}})_t$ 是价格 $P$ 在时刻 $t$ 的短期移动平均值。
*   $\text{MA}_{\text{slow}}(P, N_{\text{slow}})_t$ 是价格 $P$ 在时刻 $t$ 的长期移动平均值。

【3. 变量定义】

*   $P_i$: 在时间点 $i$ 的价格（通常为收盘价）。
*   $N_{\text{fast}}$: 短期移动平均线的周期长度。
*   $N_{\text{slow}}$: 长期移动平均线的周期长度。要求 $N_{\text{slow}} > N_{\text{fast}}$。若初始设定 $N_{\text{slow}} < N_{\text{fast}}$，则两者会被交换以满足此条件。
*   $\text{MA}(P, N)_t$: 价格序列 $P$ 在时刻 $t$ 的 $N$ 周期移动平均值。
*   $\text{PPO}_t$: 在时刻 $t$ 的百分比价格振荡器值。

【4. 函数与方法说明】

*   **移动平均 (Moving Average, MA):**
    该因子可以使用多种类型的移动平均线。最常见和经典的是简单移动平均 (Simple Moving Average, SMA)。
    *   **简单移动平均 (SMA):** 特定周期内价格的算术平均值。
        对于周期 $N$ 和时刻 $t$，SMA 的计算公式为：
        $$ \text{SMA}(P, N)_t = \frac{P_t + P_{t-1} + \dots + P_{t-N+1}}{N} = \frac{1}{N} \sum_{i=0}^{N-1} P_{t-i} $$
        注意：计算SMA需要至少 $N$ 期的数据。因此，序列的前 $N-1$ 个点无法计算SMA值。

    其他可选的移动平均类型包括指数移动平均 (EMA)、加权移动平均 (WMA)、双重指数移动平均 (DEMA)、三重指数移动平均 (TEMA)等。本卡片以SMA为例进行说明。

【5. 计算步骤】

1.  **数据准备:**
    获取资产在连续时间点上的价格序列 $P = \{P_1, P_2, \dots, P_M\}$，其中 $M$ 是数据点总数。通常使用收盘价。

2.  **参数设定:**
    *   选择短期移动平均周期 $N_{\text{fast}}$ (例如：12)。
    *   选择长期移动平均周期 $N_{\text{slow}}$ (例如：26)。
    *   确保 $N_{\text{slow}} > N_{\text{fast}}$。如果用户设置的 $N_{\text{slow}} < N_{\text{fast}}$，则将 $N_{\text{fast}}$ 和 $N_{\text{slow}}$ 的值互换。
    *   选择移动平均的类型（例如：SMA）。

3.  **计算短期移动平均 $\text{MA}_{\text{fast}}$:**
    对于时间序列中的每个点 $t$（从 $N_{\text{fast}}$ 到 $M$），计算短期移动平均值：
    $$ \text{MA}_{\text{fast}}(P, N_{\text{fast}})_t = \frac{1}{N_{\text{fast}}} \sum_{i=0}^{N_{\text{fast}}-1} P_{t-i} $$
    时间序列的前 $N_{\text{fast}}-1$ 个数据点将没有 $\text{MA}_{\text{fast}}$ 值。

4.  **计算长期移动平均 $\text{MA}_{\text{slow}}$:**
    对于时间序列中的每个点 $t$（从 $N_{\text{slow}}$ 到 $M$），计算长期移动平均值：
    $$ \text{MA}_{\text{slow}}(P, N_{\text{slow}})_t = \frac{1}{N_{\text{slow}}} \sum_{i=0}^{N_{\text{slow}}-1} P_{t-i} $$
    时间序列的前 $N_{\text{slow}}-1$ 个数据点将没有 $\text{MA}_{\text{slow}}$ 值。

5.  **计算PPO值:**
    对于两个移动平均序列中都有有效值的每个时间点 $t$（即从原始价格序列的第 $N_{\text{slow}}$ 个点开始，假设 $N_{\text{slow}} > N_{\text{fast}}$），计算PPO：
    令 $\text{Diff}_t = \text{MA}_{\text{fast}}(P, N_{\text{fast}})_t - \text{MA}_{\text{slow}}(P, N_{\text{slow}})_t$。
    如果 $\text{MA}_{\text{slow}}(P, N_{\text{slow}})_t \neq 0$:
    $$ \text{PPO}_t = \left( \frac{\text{Diff}_t}{\text{MA}_{\text{slow}}(P, N_{\text{slow}})_t} \right) \times 100 $$
    如果 $\text{MA}_{\text{slow}}(P, N_{\text{slow}})_t = 0$:
    $$ \text{PPO}_t = 0 $$
    因此，PPO序列的第一个有效值对应于原始价格序列的第 $N_{\text{slow}}$ 个数据点。

【6. 备注与参数说明】

*   **周期选择:** $N_{\text{fast}}$ 和 $N_{\text{slow}}$ 的选择对PPO的敏感度和行为有显著影响。常用的默认值为 $N_{\text{fast}}=12$ 和 $N_{\text{slow}}=26$。
*   **移动平均类型:** 因子定义允许使用不同类型的移动平均线。SMA是最基础的形式，EMA对近期价格赋予更大权重，因此反应更灵敏。选择不同的MA类型会改变PPO的特性。
*   **输入数据:** 虽然通常使用收盘价，但PPO也可以应用于开盘价、最高价、最低价或典型价格等。
*   **归一化:** 与APO不同，PPO通过除以长期移动平均线并乘以100来实现归一化。这使得PPO的值可以用于比较不同价格水平的资产，或者同一资产在不同价格区间的表现。
*   **解读:** PPO的解读与APO类似。正值表示短期趋势强于长期趋势，负值相反。零轴交叉点常被视为趋势变化的信号。由于其百分比形式，更容易设定通用的超买超卖阈值。
*   **除零处理:** 当长期移动平均值 $\text{MA}_{\text{slow}}$ 为零时（理论上对正价格数据不会发生，除非MA本身计算出错或输入价格为0），PPO值被设为0以避免计算错误。



【7. 关联因子补充说明】

【关联因子信息开始】===============================================================

【因子编号和名称】

因子编号: FC_001: 绝对价格振荡器 (Absolute Price Oscillator, APO)

【1. 因子名称详情】

因子FC_001: 绝对价格振荡器 (Absolute Price Oscillator, APO)。该指标通过计算两个不同周期的价格移动平均线之间的绝对差值，来衡量价格动量。

【2. 核心公式】

对于给定的时间点 $t$，APO的计算公式如下：
$$ \text{APO}_t = \text{MA}_{\text{fast}}(P, N_{\text{fast}})_t - \text{MA}_{\text{slow}}(P, N_{\text{slow}})_t $$

其中：
*   $\text{MA}_{\text{fast}}(P, N_{\text{fast}})_t$ 是价格 $P$ 在时刻 $t$ 的短期移动平均值。
*   $\text{MA}_{\text{slow}}(P, N_{\text{slow}})_t$ 是价格 $P$ 在时刻 $t$ 的长期移动平均值。

【3. 变量定义】

*   $P_i$: 在时间点 $i$ 的价格（通常为收盘价）。
*   $N_{\text{fast}}$: 短期移动平均线的周期长度。
*   $N_{\text{slow}}$: 长期移动平均线的周期长度。要求 $N_{\text{slow}} > N_{\text{fast}}$。若初始设定 $N_{\text{slow}} < N_{\text{fast}}$，则两者会被交换以满足此条件。
*   $\text{MA}(P, N)_t$: 价格序列 $P$ 在时刻 $t$ 的 $N$ 周期移动平均值。
*   $\text{APO}_t$: 在时刻 $t$ 的绝对价格振荡器值。

【4. 函数与方法说明】

*   **移动平均 (Moving Average, MA):**
    该因子可以使用多种类型的移动平均线。最常见和经典的是简单移动平均 (Simple Moving Average, SMA)。
    *   **简单移动平均 (SMA):** 特定周期内价格的算术平均值。
        对于周期 $N$ 和时刻 $t$，SMA 的计算公式为：
        $$ \text{SMA}(P, N)_t = \frac{P_t + P_{t-1} + \dots + P_{t-N+1}}{N} = \frac{1}{N} \sum_{i=0}^{N-1} P_{t-i} $$
        注意：计算SMA需要至少 $N$ 期的数据。因此，序列的前 $N-1$ 个点无法计算SMA值。

    其他可选的移动平均类型包括指数移动平均 (EMA)、加权移动平均 (WMA)、双重指数移动平均 (DEMA)、三重指数移动平均 (TEMA)等。本卡片以SMA为例进行说明。

【5. 计算步骤】

1.  **数据准备:**
    获取资产在连续时间点上的价格序列 $P = \{P_1, P_2, \dots, P_M\}$，其中 $M$ 是数据点总数。通常使用收盘价。

2.  **参数设定:**
    *   选择短期移动平均周期 $N_{\text{fast}}$ (例如：12)。
    *   选择长期移动平均周期 $N_{\text{slow}}$ (例如：26)。
    *   确保 $N_{\text{slow}} > N_{\text{fast}}$。如果用户设置的 $N_{\text{slow}} < N_{\text{fast}}$，则将 $N_{\text{fast}}$ 和 $N_{\text{slow}}$ 的值互换。
    *   选择移动平均的类型（例如：SMA）。

3.  **计算短期移动平均 $\text{MA}_{\text{fast}}$:**
    对于时间序列中的每个点 $t$（从 $N_{\text{fast}}$ 到 $M$），计算短期移动平均值：
    $$ \text{MA}_{\text{fast}}(P, N_{\text{fast}})_t = \frac{1}{N_{\text{fast}}} \sum_{i=0}^{N_{\text{fast}}-1} P_{t-i} $$
    时间序列的前 $N_{\text{fast}}-1$ 个数据点将没有 $\text{MA}_{\text{fast}}$ 值。

4.  **计算长期移动平均 $\text{MA}_{\text{slow}}$:**
    对于时间序列中的每个点 $t$（从 $N_{\text{slow}}$ 到 $M$），计算长期移动平均值：
    $$ \text{MA}_{\text{slow}}(P, N_{\text{slow}})_t = \frac{1}{N_{\text{slow}}} \sum_{i=0}^{N_{\text{slow}}-1} P_{t-i} $$
    时间序列的前 $N_{\text{slow}}-1$ 个数据点将没有 $\text{MA}_{\text{slow}}$ 值。

5.  **计算APO值:**
    对于两个移动平均序列中都有有效值的每个时间点 $t$（即从原始价格序列的第 $N_{\text{slow}}$ 个点开始，假设 $N_{\text{slow}} > N_{\text{fast}}$），计算APO：
    $$ \text{APO}_t = \text{MA}_{\text{fast}}(P, N_{\text{fast}})_t - \text{MA}_{\text{slow}}(P, N_{\text{slow}})_t $$
    因此，APO序列的第一个有效值对应于原始价格序列的第 $N_{\text{slow}}$ 个数据点。

【6. 备注与参数说明】

*   **周期选择:** $N_{\text{fast}}$ 和 $N_{\text{slow}}$ 的选择对APO的敏感度和行为有显著影响。常用的默认值为 $N_{\text{fast}}=12$ 和 $N_{\text{slow}}=26$，与MACD指标的默认周期相同。
*   **移动平均类型:** 因子定义允许使用不同类型的移动平均线。SMA是最基础的形式，EMA对近期价格赋予更大权重，因此反应更灵敏。选择不同的MA类型会改变APO的特性。
*   **输入数据:** 虽然通常使用收盘价，但APO也可以应用于开盘价、最高价、最低价或典型价格（(最高价+最低价+收盘价)/3）等。
*   **解读:** APO的值表示短期趋势相对于长期趋势的强度。正值表示短期均线在长期均线上方，可能预示上涨动能；负值则相反。零轴交叉点常被视为趋势变化的信号。
*   **无界性:** APO的值没有固定的上下限，其绝对大小取决于标的价格的波动范围。

【关联因子信息结束】===============================================================

---

【关联因子信息开始】===============================================================

【因子编号和名称】

因子编号: FC_002: 百分比价格振荡器 (Percentage Price Oscillator, PPO)

【1. 因子名称详情】

因子FC_002: 百分比价格振荡器 (Percentage Price Oscillator, PPO)。该指标通过计算两个不同周期的价格移动平均线之间的差值，并将其表示为长期移动平均线的百分比，来衡量价格动量。

【2. 核心公式】

对于给定的时间点 $t$，PPO的计算公式如下：
$$ \text{PPO}_t = \left( \frac{\text{MA}_{\text{fast}}(P, N_{\text{fast}})_t - \text{MA}_{\text{slow}}(P, N_{\text{slow}})_t}{\text{MA}_{\text{slow}}(P, N_{\text{slow}})_t} \right) \times 100 $$

若 $\text{MA}_{\text{slow}}(P, N_{\text{slow}})_t = 0$，则 $\text{PPO}_t = 0$。

其中：
*   $\text{MA}_{\text{fast}}(P, N_{\text{fast}})_t$ 是价格 $P$ 在时刻 $t$ 的短期移动平均值。
*   $\text{MA}_{\text{slow}}(P, N_{\text{slow}})_t$ 是价格 $P$ 在时刻 $t$ 的长期移动平均值。

【3. 变量定义】

*   $P_i$: 在时间点 $i$ 的价格（通常为收盘价）。
*   $N_{\text{fast}}$: 短期移动平均线的周期长度。
*   $N_{\text{slow}}$: 长期移动平均线的周期长度。要求 $N_{\text{slow}} > N_{\text{fast}}$。若初始设定 $N_{\text{slow}} < N_{\text{fast}}$，则两者会被交换以满足此条件。
*   $\text{MA}(P, N)_t$: 价格序列 $P$ 在时刻 $t$ 的 $N$ 周期移动平均值。
*   $\text{PPO}_t$: 在时刻 $t$ 的百分比价格振荡器值。

【4. 函数与方法说明】

*   **移动平均 (Moving Average, MA):**
    该因子可以使用多种类型的移动平均线。最常见和经典的是简单移动平均 (Simple Moving Average, SMA)。
    *   **简单移动平均 (SMA):** 特定周期内价格的算术平均值。
        对于周期 $N$ 和时刻 $t$，SMA 的计算公式为：
        $$ \text{SMA}(P, N)_t = \frac{P_t + P_{t-1} + \dots + P_{t-N+1}}{N} = \frac{1}{N} \sum_{i=0}^{N-1} P_{t-i} $$
        注意：计算SMA需要至少 $N$ 期的数据。因此，序列的前 $N-1$ 个点无法计算SMA值。

    其他可选的移动平均类型包括指数移动平均 (EMA)、加权移动平均 (WMA)、双重指数移动平均 (DEMA)、三重指数移动平均 (TEMA)等。本卡片以SMA为例进行说明。

【5. 计算步骤】

1.  **数据准备:**
    获取资产在连续时间点上的价格序列 $P = \{P_1, P_2, \dots, P_M\}$，其中 $M$ 是数据点总数。通常使用收盘价。

2.  **参数设定:**
    *   选择短期移动平均周期 $N_{\text{fast}}$ (例如：12)。
    *   选择长期移动平均周期 $N_{\text{slow}}$ (例如：26)。
    *   确保 $N_{\text{slow}} > N_{\text{fast}}$。如果用户设置的 $N_{\text{slow}} < N_{\text{fast}}$，则将 $N_{\text{fast}}$ 和 $N_{\text{slow}}$ 的值互换。
    *   选择移动平均的类型（例如：SMA）。

3.  **计算短期移动平均 $\text{MA}_{\text{fast}}$:**
    对于时间序列中的每个点 $t$（从 $N_{\text{fast}}$ 到 $M$），计算短期移动平均值：
    $$ \text{MA}_{\text{fast}}(P, N_{\text{fast}})_t = \frac{1}{N_{\text{fast}}} \sum_{i=0}^{N_{\text{fast}}-1} P_{t-i} $$
    时间序列的前 $N_{\text{fast}}-1$ 个数据点将没有 $\text{MA}_{\text{fast}}$ 值。

4.  **计算长期移动平均 $\text{MA}_{\text{slow}}$:**
    对于时间序列中的每个点 $t$（从 $N_{\text{slow}}$ 到 $M$），计算长期移动平均值：
    $$ \text{MA}_{\text{slow}}(P, N_{\text{slow}})_t = \frac{1}{N_{\text{slow}}} \sum_{i=0}^{N_{\text{slow}}-1} P_{t-i} $$
    时间序列的前 $N_{\text{slow}}-1$ 个数据点将没有 $\text{MA}_{\text{slow}}$ 值。

5.  **计算PPO值:**
    对于两个移动平均序列中都有有效值的每个时间点 $t$（即从原始价格序列的第 $N_{\text{slow}}$ 个点开始，假设 $N_{\text{slow}} > N_{\text{fast}}$），计算PPO：
    令 $\text{Diff}_t = \text{MA}_{\text{fast}}(P, N_{\text{fast}})_t - \text{MA}_{\text{slow}}(P, N_{\text{slow}})_t$。
    如果 $\text{MA}_{\text{slow}}(P, N_{\text{slow}})_t \neq 0$:
    $$ \text{PPO}_t = \left( \frac{\text{Diff}_t}{\text{MA}_{\text{slow}}(P, N_{\text{slow}})_t} \right) \times 100 $$
    如果 $\text{MA}_{\text{slow}}(P, N_{\text{slow}})_t = 0$:
    $$ \text{PPO}_t = 0 $$
    因此，PPO序列的第一个有效值对应于原始价格序列的第 $N_{\text{slow}}$ 个数据点。

【6. 备注与参数说明】

*   **周期选择:** $N_{\text{fast}}$ 和 $N_{\text{slow}}$ 的选择对PPO的敏感度和行为有显著影响。常用的默认值为 $N_{\text{fast}}=12$ 和 $N_{\text{slow}}=26$。
*   **移动平均类型:** 因子定义允许使用不同类型的移动平均线。SMA是最基础的形式，EMA对近期价格赋予更大权重，因此反应更灵敏。选择不同的MA类型会改变PPO的特性。
*   **输入数据:** 虽然通常使用收盘价，但PPO也可以应用于开盘价、最高价、最低价或典型价格等。
*   **归一化:** 与APO不同，PPO通过除以长期移动平均线并乘以100来实现归一化。这使得PPO的值可以用于比较不同价格水平的资产，或者同一资产在不同价格区间的表现。
*   **解读:** PPO的解读与APO类似。正值表示短期趋势强于长期趋势，负值相反。零轴交叉点常被视为趋势变化的信号。由于其百分比形式，更容易设定通用的超买超卖阈值。
*   **除零处理:** 当长期移动平均值 $\text{MA}_{\text{slow}}$ 为零时（理论上对正价格数据不会发生，除非MA本身计算出错或输入价格为0），PPO值被设为0以避免计算错误。

【关联因子信息结束】===============================================================

【因子信息结束】===============================================================