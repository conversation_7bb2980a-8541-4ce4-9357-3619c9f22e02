【因子信息开始】===============================================================

【因子编号和名称】

因子编号: MACDEXT_SIG: 扩展指数平滑异同移动平均线 - 信号线 (Moving Average Convergence Divergence Extended - Signal Line, MACDEXT_SIG)

【1. 因子名称详情】

因子2: 扩展指数平滑异同移动平均线 - 信号线 (Moving Average Convergence Divergence Extended - Signal Line, MACDEXT_SIG)。该指标是对差离值（DIF / MACD Line）进行移动平均平滑后得到的线。

【2. 核心公式】

$Signal_t = MA(DIF, P_{signal}, Type_{signal})_t$

其中：
*   $Signal_t$ 是在 $t$ 时刻的信号线值。
*   $DIF_t$ 是在 $t$ 时刻的差离值（即上一个因子MACDEXT_DIF的输出）。
*   $MA(DIF, P_{signal}, Type_{signal})_t$ 是在 $t$ 时刻对差离值序列 $DIF$，采用周期 $P_{signal}$ 和类型 $Type_{signal}$ 计算得到的移动平均值。
*   $P_{signal}$ 是信号线移动平均的周期。
*   $Type_{signal}$ 是信号线移动平均的类型。

【3. 变量定义】

*   $DIF_t$: $t$ 时刻的差离值序列（由因子MACDEXT_DIF计算得出）。
*   $P_{signal}$: 信号线移动平均的计算周期（例如：9）。
*   $Type_{signal}$: 信号线移动平均的类型（例如：EMA, SMA等）。
*   $MA(Data, Period, Type)_t$: 一个通用的移动平均函数，表示在 $t$ 时刻对指定数据序列 $Data$，使用周期 $Period$ 和指定类型 $Type$ 计算的移动平均值。

【4. 函数与方法说明】

移动平均 $MA(Data, Period, Type)_t$ 可以是多种类型，其计算方法与因子MACDEXT_DIF 【4. 函数与方法说明】中描述的相同：

1.  **简单移动平均 (Simple Moving Average, SMA)**:
    $SMA_t(Data, N) = \frac{1}{N} \sum_{i=0}^{N-1} Data_{t-i}$
    （详见因子MACDEXT_DIF）

2.  **指数移动平均 (Exponential Moving Average, EMA)**:
    $EMA_t(Data, N) = \alpha \cdot Data_t + (1-\alpha) \cdot EMA_{t-1}(Data, N)$, with $\alpha = \frac{k}{N+1}$ (typically $k=2$).
    （详见因子MACDEXT_DIF）

3.  **加权移动平均 (Weighted Moving Average, WMA)**:
    $WMA_t(Data, N) = \frac{\sum_{i=0}^{N-1} (N-i) \cdot Data_{t-i}}{N(N+1)/2}$
    （详见因子MACDEXT_DIF）

4.  **双指数移动平均 (Double Exponential Moving Average, DEMA)**:
    $DEMA_t(Data, N) = 2 \cdot EMA_t(Data, N) - EMA_t(EMA(Data, N), N)$
    （详见因子MACDEXT_DIF）

5.  **三指数移动平均 (Triple Exponential Moving Average, TEMA)**:
    $TEMA_t = 3 \cdot EMA_t(Data,N) - 3 \cdot EMA_t(EMA(Data,N),N) + EMA_t(EMA(EMA(Data,N),N),N)$
    （详见因子MACDEXT_DIF）

    (以及其他MA类型。在TA-LIB的MACDEXT中，用户可以为信号线指定这些MA类型中的任意一种。）

【5. 计算步骤】

1.  **计算差离值序列 (DIF)**:
    a.  **数据准备**: 获取输入价格序列 $Price_t$。
    b.  **参数设定 (DIF)**:
        *   确定快线周期 $P_{fast}$。
        *   确定快线移动平均类型 $Type_{fast}$。
        *   确定慢线周期 $P_{slow}$。
        *   确定慢线移动平均类型 $Type_{slow}$。
    c.  **参数调整 (DIF)**: 确保 $P_{slow} > P_{fast}$。若否则交换快慢参数及其类型。
    d.  **计算快线移动平均 (DIF)**: $MA_{fast,t} = MA(Price, P_{fast}, Type_{fast})_t$。
    e.  **计算慢线移动平均 (DIF)**: $MA_{slow,t} = MA(Price, P_{slow}, Type_{slow})_t$。
    f.  **得到差离值序列 (DIF)**: $DIF_t = MA_{fast,t} - MA_{slow,t}$。此序列将作为计算信号线的输入。

2.  **参数设定 (Signal Line)**:
    *   确定信号线移动平均周期 $P_{signal}$ (例如：9)。
    *   确定信号线移动平均类型 $Type_{signal}$ (例如：EMA)。

3.  **计算信号线 (Signal Line)**:
    根据步骤1f得到的 $DIF_t$ 序列、周期 $P_{signal}$ 和类型 $Type_{signal}$，计算信号线移动平均值序列 $Signal_t$。
    例如，若 $Type_{signal}$为EMA，则 $Signal_t = EMA_t(DIF, P_{signal})$。
    计算信号线需要 $P_{signal}$ 或更多期的 $DIF_t$ 数据来初始化。因此，第一个有效的 $Signal_t$ 值将晚于第一个有效的 $DIF_t$ 值。

【6. 备注与参数说明】

*   **经典参数**: 传统的MACD通常使用 $P_{signal}=9$ 周期EMA对DIF线进行平滑得到信号线。
*   **MA类型选择**: 同DIF线，信号线也可以使用不同的MA类型，这将影响信号线的平滑程度和响应速度。
*   **数据窗口期**: 信号线的计算依赖于DIF线，因此其有效起始点会比DIF线更晚。总的回溯期（lookback period）是DIF线的回溯期加上信号线MA的回溯期。
*   **用途**: 信号线与DIF线的交叉常被用作交易信号。



【7. 关联因子补充说明】

【关联因子信息开始】===============================================================**

**【因子编号和名称】**

因子编号: MACDEXT_DIF: 扩展指数平滑异同移动平均线 - 差离值 (Moving Average Convergence Divergence Extended - Difference, MACDEXT_DIF)

**【1. 因子名称详情】**

因子1: 扩展指数平滑异同移动平均线 - 差离值 (Moving Average Convergence Divergence Extended - Difference, MACDEXT_DIF)。该指标是两条不同周期的价格移动平均线之间的差值。

**【2. 核心公式】**

$DIF_t = MA(Price, P_{fast}, Type_{fast})_t - MA(Price, P_{slow}, Type_{slow})_t$

其中：
*   $DIF_t$ 是在 $t$ 时刻的差离值（MACD线）。
*   $MA(Price, P, Type)_t$ 是在 $t$ 时刻对价格序列 $Price$，采用周期 $P$ 和类型 $Type$ 计算得到的移动平均值。
*   $P_{fast}$ 是快线移动平均的周期。
*   $Type_{fast}$ 是快线移动平均的类型。
*   $P_{slow}$ 是慢线移动平均的周期。
*   $Type_{slow}$ 是慢线移动平均的类型。

**【3. 变量定义】**

*   $Price_t$: $t$ 时刻的输入价格序列（通常为收盘价）。
*   $P_{fast}$: 快线移动平均的计算周期（例如：12）。
*   $Type_{fast}$: 快线移动平均的类型（例如：EMA, SMA等）。
*   $P_{slow}$: 慢线移动平均的计算周期（例如：26）。
*   $Type_{slow}$: 慢线移动平均的类型（例如：EMA, SMA等）。
*   $MA(Data, Period, Type)_t$: 一个通用的移动平均函数，表示在 $t$ 时刻对指定数据序列 $Data$，使用周期 $Period$ 和指定类型 $Type$ 计算的移动平均值。

**【4. 函数与方法说明】**

移动平均 $MA(Data, Period, Type)_t$ 可以是多种类型，以下列出几种常见的计算方法：

1.  **简单移动平均 (Simple Moving Average, SMA)**:
    $SMA_t(Data, N) = \frac{1}{N} \sum_{i=0}^{N-1} Data_{t-i}$
    其中 $N$ 是周期，$Data_{t-i}$ 是过去第 $i$ 期的数据点。计算SMA需要至少 $N$ 期的数据。

2.  **指数移动平均 (Exponential Moving Average, EMA)**:
    $EMA_t(Data, N) = \alpha \cdot Data_t + (1-\alpha) \cdot EMA_{t-1}(Data, N)$
    其中平滑系数 $\alpha = \frac{k}{N+1}$。通常 $k=2$。
    第一个 $EMA$ 值（即 $EMA_0$）的计算有多种方式，常见的是使用前 $N$ 期数据的简单平均值(SMA)作为初始值: $EMA_0 = SMA(Data, N)$ for the first N periods。后续的EMA值则通过上述递归公式计算。计算EMA需要至少 $N$ 期数据（用于初始SMA）或更长的历史数据以达到稳定。

3.  **加权移动平均 (Weighted Moving Average, WMA)**:
    $WMA_t(Data, N) = \frac{\sum_{i=0}^{N-1} (N-i) \cdot Data_{t-i}}{\sum_{j=1}^{N} j} = \frac{\sum_{i=0}^{N-1} (N-i) \cdot Data_{t-i}}{N(N+1)/2}$
    最近的数据点被赋予更大的权重。

4.  **双指数移动平均 (Double Exponential Moving Average, DEMA)**:
    $DEMA_t(Data, N) = 2 \cdot EMA_t(Data, N) - EMA_t(EMA(Data, N), N)$
    它通过对EMA进行再次EMA平滑并进行调整来减少滞后性。

5.  **三指数移动平均 (Triple Exponential Moving Average, TEMA)**:
    $EMA1_t = EMA_t(Data, N)$
    $EMA2_t = EMA_t(EMA1, N)$
    $EMA3_t = EMA_t(EMA2, N)$
    $TEMA_t = 3 \cdot EMA1_t - 3 \cdot EMA2_t + EMA3_t$

    (还有其他MA类型如TRIMA, KAMA, MAMA, T3等，具体实现各有不同。)
    在TA-LIB的MACDEXT中，用户可以为快线和慢线分别指定这些MA类型中的任意一种。

**【5. 计算步骤】**

1.  **数据准备**: 获取输入价格序列 $Price_t$ (例如每日收盘价)。
2.  **参数设定**:
    *   确定快线周期 $P_{fast}$ (例如：12)。
    *   确定快线移动平均类型 $Type_{fast}$ (例如：EMA)。
    *   确定慢线周期 $P_{slow}$ (例如：26)。
    *   确定慢线移动平均类型 $Type_{slow}$ (例如：EMA)。
3.  **参数调整**: 确保慢线周期大于快线周期。如果 $P_{slow} < P_{fast}$，则交换 $P_{slow}$ 与 $P_{fast}$ 的值，并同时交换 $Type_{slow}$ 与 $Type_{fast}$ 的类型。
4.  **计算快线移动平均**:
    根据价格序列 $Price_t$、周期 $P_{fast}$ 和类型 $Type_{fast}$，计算快线移动平均值序列 $MA_{fast,t}$。
    例如，若 $Type_{fast}$为EMA，则 $MA_{fast,t} = EMA_t(Price, P_{fast})$。计算需要 $P_{fast}$ 或更多期的历史数据来初始化。
5.  **计算慢线移动平均**:
    根据价格序列 $Price_t$、周期 $P_{slow}$ 和类型 $Type_{slow}$，计算慢线移动平均值序列 $MA_{slow,t}$。
    例如，若 $Type_{slow}$为EMA，则 $MA_{slow,t} = EMA_t(Price, P_{slow})$。计算需要 $P_{slow}$ 或更多期的历史数据来初始化。
6.  **计算差离值 (DIF / MACD Line)**:
    对于每一个时间点 $t$，计算 $DIF_t = MA_{fast,t} - MA_{slow,t}$。
    第一个有效的 $DIF_t$ 值取决于 $MA_{fast,t}$ 和 $MA_{slow,t}$ 中需要更多历史数据的那一个（即 $\max(\text{Lookback}(P_{fast}, Type_{fast}), \text{Lookback}(P_{slow}, Type_{slow}))$，其中Lookback代表该MA类型和周期所需的最小数据点数）。

**【6. 备注与参数说明】**

*   **经典参数**: 传统的MACD通常使用EMA作为移动平均类型，快线周期为12，慢线周期为26。
*   **MA类型选择**: MACDEXT的灵活性在于允许用户为快线和慢线选择不同的MA类型，这可以显著改变指标的行为。例如，使用SMA会使线条更平滑但滞后更大，而使用DEMA或TEMA则旨在减少滞后。
*   **数据窗口期**: 计算所需的初始数据量取决于所选MA类型及其周期。例如，EMA的有效起始点通常会晚于SMA。
*   **价格数据**: 通常使用收盘价计算，但也可以应用于开盘价、最高价、最低价或典型价格等。

**【关联因子信息结束】===============================================================**

---

**【关联因子信息开始】===============================================================**

**【因子编号和名称】**

因子编号: MACDEXT_SIG: 扩展指数平滑异同移动平均线 - 信号线 (Moving Average Convergence Divergence Extended - Signal Line, MACDEXT_SIG)

**【1. 因子名称详情】**

因子2: 扩展指数平滑异同移动平均线 - 信号线 (Moving Average Convergence Divergence Extended - Signal Line, MACDEXT_SIG)。该指标是对差离值（DIF / MACD Line）进行移动平均平滑后得到的线。

**【2. 核心公式】**

$Signal_t = MA(DIF, P_{signal}, Type_{signal})_t$

其中：
*   $Signal_t$ 是在 $t$ 时刻的信号线值。
*   $DIF_t$ 是在 $t$ 时刻的差离值（即上一个因子MACDEXT_DIF的输出）。
*   $MA(DIF, P_{signal}, Type_{signal})_t$ 是在 $t$ 时刻对差离值序列 $DIF$，采用周期 $P_{signal}$ 和类型 $Type_{signal}$ 计算得到的移动平均值。
*   $P_{signal}$ 是信号线移动平均的周期。
*   $Type_{signal}$ 是信号线移动平均的类型。

**【3. 变量定义】**

*   $DIF_t$: $t$ 时刻的差离值序列（由因子MACDEXT_DIF计算得出）。
*   $P_{signal}$: 信号线移动平均的计算周期（例如：9）。
*   $Type_{signal}$: 信号线移动平均的类型（例如：EMA, SMA等）。
*   $MA(Data, Period, Type)_t$: 一个通用的移动平均函数，表示在 $t$ 时刻对指定数据序列 $Data$，使用周期 $Period$ 和指定类型 $Type$ 计算的移动平均值。

**【4. 函数与方法说明】**

移动平均 $MA(Data, Period, Type)_t$ 可以是多种类型，其计算方法与因子MACDEXT_DIF 【4. 函数与方法说明】中描述的相同：

1.  **简单移动平均 (Simple Moving Average, SMA)**:
    $SMA_t(Data, N) = \frac{1}{N} \sum_{i=0}^{N-1} Data_{t-i}$
    （详见因子MACDEXT_DIF）

2.  **指数移动平均 (Exponential Moving Average, EMA)**:
    $EMA_t(Data, N) = \alpha \cdot Data_t + (1-\alpha) \cdot EMA_{t-1}(Data, N)$, with $\alpha = \frac{k}{N+1}$ (typically $k=2$).
    （详见因子MACDEXT_DIF）

3.  **加权移动平均 (Weighted Moving Average, WMA)**:
    $WMA_t(Data, N) = \frac{\sum_{i=0}^{N-1} (N-i) \cdot Data_{t-i}}{N(N+1)/2}$
    （详见因子MACDEXT_DIF）

4.  **双指数移动平均 (Double Exponential Moving Average, DEMA)**:
    $DEMA_t(Data, N) = 2 \cdot EMA_t(Data, N) - EMA_t(EMA(Data, N), N)$
    （详见因子MACDEXT_DIF）

5.  **三指数移动平均 (Triple Exponential Moving Average, TEMA)**:
    $TEMA_t = 3 \cdot EMA_t(Data,N) - 3 \cdot EMA_t(EMA(Data,N),N) + EMA_t(EMA(EMA(Data,N),N),N)$
    （详见因子MACDEXT_DIF）

    (以及其他MA类型。在TA-LIB的MACDEXT中，用户可以为信号线指定这些MA类型中的任意一种。)

**【5. 计算步骤】**

1.  **计算差离值序列 (DIF)**:
    a.  **数据准备**: 获取输入价格序列 $Price_t$。
    b.  **参数设定 (DIF)**:
        *   确定快线周期 $P_{fast}$。
        *   确定快线移动平均类型 $Type_{fast}$。
        *   确定慢线周期 $P_{slow}$。
        *   确定慢线移动平均类型 $Type_{slow}$。
    c.  **参数调整 (DIF)**: 确保 $P_{slow} > P_{fast}$。若否则交换快慢参数及其类型。
    d.  **计算快线移动平均 (DIF)**: $MA_{fast,t} = MA(Price, P_{fast}, Type_{fast})_t$。
    e.  **计算慢线移动平均 (DIF)**: $MA_{slow,t} = MA(Price, P_{slow}, Type_{slow})_t$。
    f.  **得到差离值序列 (DIF)**: $DIF_t = MA_{fast,t} - MA_{slow,t}$。此序列将作为计算信号线的输入。

2.  **参数设定 (Signal Line)**:
    *   确定信号线移动平均周期 $P_{signal}$ (例如：9)。
    *   确定信号线移动平均类型 $Type_{signal}$ (例如：EMA)。

3.  **计算信号线 (Signal Line)**:
    根据步骤1f得到的 $DIF_t$ 序列、周期 $P_{signal}$ 和类型 $Type_{signal}$，计算信号线移动平均值序列 $Signal_t$。
    例如，若 $Type_{signal}$为EMA，则 $Signal_t = EMA_t(DIF, P_{signal})$。
    计算信号线需要 $P_{signal}$ 或更多期的 $DIF_t$ 数据来初始化。因此，第一个有效的 $Signal_t$ 值将晚于第一个有效的 $DIF_t$ 值。

**【6. 备注与参数说明】**

*   **经典参数**: 传统的MACD通常使用 $P_{signal}=9$ 周期EMA对DIF线进行平滑得到信号线。
*   **MA类型选择**: 同DIF线，信号线也可以使用不同的MA类型，这将影响信号线的平滑程度和响应速度。
*   **数据窗口期**: 信号线的计算依赖于DIF线，因此其有效起始点会比DIF线更晚。总的回溯期（lookback period）是DIF线的回溯期加上信号线MA的回溯期。
*   **用途**: 信号线与DIF线的交叉常被用作交易信号。

**【关联因子信息结束】===============================================================**

---

**【关联因子信息开始】===============================================================**

**【因子编号和名称】**

因子编号: MACDEXT_HIST: 扩展指数平滑异同移动平均线 - 柱状图 (Moving Average Convergence Divergence Extended - Histogram, MACDEXT_HIST)

**【1. 因子名称详情】**

因子3: 扩展指数平滑异同移动平均线 - 柱状图 (Moving Average Convergence Divergence Extended - Histogram, MACDEXT_HIST)。该指标是差离值（DIF / MACD Line）与信号线（Signal Line）之间的差。

**【2. 核心公式】**

$Histogram_t = DIF_t - Signal_t$

其中：
*   $Histogram_t$ 是在 $t$ 时刻的MACD柱状图值。
*   $DIF_t$ 是在 $t$ 时刻的差离值（因子MACDEXT_DIF的输出）。
*   $Signal_t$ 是在 $t$ 时刻的信号线值（因子MACDEXT_SIG的输出）。

**【3. 变量定义】**

*   $DIF_t$: $t$ 时刻的差离值序列 (来自因子MACDEXT_DIF)。
*   $Signal_t$: $t$ 时刻的信号线序列 (来自因子MACDEXT_SIG)。

**【4. 函数与方法说明】**

此因子本身不直接使用复杂的统计函数，它的计算依赖于 $DIF_t$ 和 $Signal_t$ 的值。$DIF_t$ 和 $Signal_t$ 的计算则依赖于移动平均函数 $MA(Data, Period, Type)_t$。其定义和常见类型（SMA, EMA, WMA, DEMA, TEMA等）已在因子MACDEXT_DIF的【4. 函数与方法说明】部分详细描述，此处为保持独立性，再次概要提及：

*   移动平均 $MA(Data, Period, Type)_t$ : 根据特定数据序列、周期和类型计算的平均值。
    *   **SMA**: 简单算术平均。
    *   **EMA**: 指数加权平均，近期数据权重更高。
    *   (其他类型详见因子MACDEXT_DIF说明)

**【5. 计算步骤】**

1.  **计算差离值序列 (DIF)**:
    a.  **数据准备**: 获取输入价格序列 $Price_t$。
    b.  **参数设定 (DIF)**: 确定快线周期 $P_{fast}$、快线MA类型 $Type_{fast}$、慢线周期 $P_{slow}$、慢线MA类型 $Type_{slow}$。
    c.  **参数调整 (DIF)**: 确保 $P_{slow} > P_{fast}$。若否则交换快慢参数及其类型。
    d.  **计算快线移动平均 (DIF)**: $MA_{fast,t} = MA(Price, P_{fast}, Type_{fast})_t$。
    e.  **计算慢线移动平均 (DIF)**: $MA_{slow,t} = MA(Price, P_{slow}, Type_{slow})_t$。
    f.  **得到差离值序列 (DIF)**: $DIF_t = MA_{fast,t} - MA_{slow,t}$。

2.  **计算信号线序列 (Signal)**:
    a.  **参数设定 (Signal Line)**: 确定信号线移动平均周期 $P_{signal}$、信号线MA类型 $Type_{signal}$。
    b.  **计算信号线 (Signal Line)**: 根据步骤1f得到的 $DIF_t$ 序列、周期 $P_{signal}$ 和类型 $Type_{signal}$，计算信号线移动平均值序列 $Signal_t = MA(DIF, P_{signal}, Type_{signal})_t$。

3.  **计算MACD柱状图 (Histogram)**:
    对于每一个有对应 $DIF_t$ 和 $Signal_t$ 值的时间点 $t$，计算:
    $Histogram_t = DIF_t - Signal_t$
    第一个有效的 $Histogram_t$ 值取决于 $Signal_t$ 的第一个有效值，因为 $Signal_t$ 的计算依赖于DIF序列且有自身的滞后。

**【6. 备注与参数说明】**

*   **参数依赖**: MACD柱状图的参数完全继承自DIF线和信号线的参数设置。
*   **解读**:
    *   柱状图的正负表示DIF线在信号线的上方或下方。
    *   柱状图的绝对值大小表示DIF线与信号线之间的距离，可以看作是动能的强度。
    *   柱状图从负转正或从正转负（即穿越零轴）通常被视为趋势可能发生变化的信号。
    *   柱状图的收缩和扩张可以反映趋势的减弱和加强。
*   **数据窗口期**: 柱状图的有效起始点与信号线的有效起始点相同，是三个MACDEXT输出中最晚出现的。

**【关联因子信息结束】===============================================================**

【因子信息结束】===============================================================
