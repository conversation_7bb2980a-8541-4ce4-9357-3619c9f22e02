【因子信息开始】===============================================================

【因子编号和名称】

因子编号: 012: 平均趋向指数评估 (Average Directional Movement Index Rating, ADXR)

【1. 因子名称详情】

因子完整名称：平均趋向指数评估 (Average Directional Movement Index Rating, ADXR)。该指标是平均趋向指数 (ADX) 的平滑版本，用于评估趋势的强度。

【2. 核心公式】

ADXR 的计算依赖于 ADX（平均趋向指数）。其核心思想是取当前周期的 ADX 值与若干周期前的 ADX 值的平均。

$ADXR_t = \frac{ADX_t + ADX_{t - (\text{period} - 1)}}{\text{2}}$

其中，ADX 的计算过程较为复杂，涉及到以下步骤：

1.  真实波幅 (True Range, TR):
    $TR_t = \max(H_t - L_t, |H_t - C_{t-1}|, |L_t - C_{t-1}|)$

2.  方向性运动 (Directional Movement, DM):
    *   上方向性运动 (+DM):
        $\text{upMove}_t = H_t - H_{t-1}$
        $\text{downMove}_t = L_{t-1} - L_t$
        如果 $\text{upMove}_t > \text{downMove}_t$ 且 $\text{upMove}_t > 0$, 则 $+DM_t = \text{upMove}_t$
        否则 $+DM_t = 0$
    *   下方向性运动 (-DM):
        如果 $\text{downMove}_t > \text{upMove}_t$ 且 $\text{downMove}_t > 0$, 则 $-DM_t = \text{downMove}_t$
        否则 $-DM_t = 0$

3.  真实波幅、+DM、-DM的N周期平滑（Wilder's Smoothing）：
    对于序列 $X_t$:
    初始 $Smooth(X, N)_N = \sum_{i=1}^{N} X_i / N$ （前N个值的简单平均）
    后续 $Smooth(X, N)_t = (Smooth(X, N)_{t-1} \times (N-1) + X_t) / N$
    我们计算：
    $ATR_t = Smooth(TR, \text{period})_t$
    $+DM_{\text{period},t} = Smooth(+DM, \text{period})_t$
    $-DM_{\text{period},t} = Smooth(-DM, \text{period})_t$

4.  方向性指标 (Directional Indicators, DI):
    $+DI_{\text{period},t} = \left( \frac{+DM_{\text{period},t}}{ATR_t} \right) \times 100$
    $-DI_{\text{period},t} = \left( \frac{-DM_{\text{period},t}}{ATR_t} \right) \times 100$

5.  趋向指数 (Directional Movement Index, DX):
    $DX_t = \left( \frac{|+DI_{\text{period},t} - (-DI_{\text{period},t})|}{|+DI_{\text{period},t} + (-DI_{\text{period},t})|} \right) \times 100$
    (如果分母为0，则 $DX_t = 0$)

6.  平均趋向指数 (Average Directional Movement Index, ADX):
    $ADX_t = Smooth(DX, \text{period})_t$
    （使用与上述 TR, DM 相同的 N 周期 Wilder's Smoothing 方法，其中 N 为 period）

【3. 变量定义】

*   $ADXR_t$: 在时间点 $t$ 的平均趋向指数评估值。
*   $ADX_t$: 在时间点 $t$ 的平均趋向指数值。
*   $ADX_{t - (\text{period} - 1)}$: 在时间点 $t$ 之前的第 $(\text{period} - 1)$ 个周期的 ADX 值。
*   $\text{period}$: 计算 ADX 及 ADXR 时使用的时间周期参数。
*   $H_t$: 在时间点 $t$ 的最高价。
*   $L_t$: 在时间点 $t$ 的最低价。
*   $C_t$: 在时间点 $t$ 的收盘价。
*   $C_{t-1}$: 在时间点 $t-1$ 的收盘价。
*   $H_{t-1}$: 在时间点 $t-1$ 的最高价。
*   $L_{t-1}$: 在时间点 $t-1$ 的最低价。
*   $TR_t$: 在时间点 $t$ 的真实波幅。
*   $+DM_t$: 在时间点 $t$ 的上方向性运动。
*   $-DM_t$: 在时间点 $t$ 的下方向性运动。
*   $ATR_t$: 在时间点 $t$ 的N周期平滑真实波幅。
*   $+DM_{\text{period},t}$: 在时间点 $t$ 的N周期平滑上方向性运动。
*   $-DM_{\text{period},t}$: 在时间点 $t$ 的N周期平滑下方向性运动。
*   $+DI_{\text{period},t}$: 在时间点 $t$ 的N周期正方向指标。
*   $-DI_{\text{period},t}$: 在时间点 $t$ 的N周期负方向指标。
*   $DX_t$: 在时间点 $t$ 的趋向指数。

【4. 函数与方法说明】

*   $\max(a,b,c,...)$: 返回一组数值中的最大值。
*   $|x|$: 返回 $x$ 的绝对值。
*   $Smooth(X, N)$: Wilder's Smoothing（威尔德平滑法），一种特殊的指数移动平均。
    *   计算方法：
        1.  对于序列 $X$ 的第一个平滑值 $Smooth(X, N)_N$（即在第 $N$ 个数据点处的平滑值），它是 $X$ 的前 $N$ 个值的简单算术平均：
            $Smooth(X, N)_N = \frac{\sum_{i=1}^{N} X_i}{N}$
        2.  对于后续的平滑值 $Smooth(X, N)_t$ (其中 $t > N$):
            $Smooth(X, N)_t = \frac{Smooth(X, N)_{t-1} \times (N-1) + X_t}{N}$
    *   此方法应用于计算 $ATR_t$, $+DM_{\text{period},t}$, $-DM_{\text{period},t}$ 以及 $ADX_t$。

【5. 计算步骤】

数据准备：需要包含最高价 (High)、最低价 (Low)、收盘价 (Close) 的时间序列数据。参数 `period` 需要预先设定。

步骤 1: 计算真实波幅 (TR)
    对于每个时间点 $t$ (从第二个数据点开始，因为它需要前一收盘价):
    $TR_t = \max(H_t - L_t, |H_t - C_{t-1}|, |L_t - C_{t-1}|)$

步骤 2: 计算方向性运动 (+DM 和 -DM)
    对于每个时间点 $t$ (从第二个数据点开始):
    $\text{upMove}_t = H_t - H_{t-1}$
    $\text{downMove}_t = L_{t-1} - L_t$
    如果 $\text{upMove}_t > \text{downMove}_t$ 且 $\text{upMove}_t > 0$, 则 $+DM_t = \text{upMove}_t$, 否则 $+DM_t = 0$
    如果 $\text{downMove}_t > \text{upMove}_t$ 且 $\text{downMove}_t > 0$, 则 $-DM_t = \text{downMove}_t$, 否则 $-DM_t = 0$

步骤 3: 计算平滑后的真实波幅 (ATR)、平滑后的+DM ($+DM_{\text{period}}$)、平滑后的-DM ($-DM_{\text{period}}$)
    使用参数 `period` 对 $TR_t$, $+DM_t$, $-DM_t$ 序列进行 Wilder's Smoothing。
    *   计算第一个 $ATR_{\text{period}}$: 需要前 `period` 个 $TR$ 值，计算其简单平均值。例如，第 `period` 个 $ATR$ 值是 $TR_1$ 到 $TR_{\text{period}}$ 的平均。
    *   后续 $ATR_t = (ATR_{t-1} \times (\text{period}-1) + TR_t) / \text{period}$
    *   类似地计算 $+DM_{\text{period},t}$ 和 $-DM_{\text{period},t}$。

步骤 4: 计算方向性指标 (+DI 和 -DI)
    对于每个时间点 $t$ (在完成步骤3之后，有足够的数据点):
    $+DI_{\text{period},t} = \left( \frac{+DM_{\text{period},t}}{ATR_t} \right) \times 100$
    $-DI_{\text{period},t} = \left( \frac{-DM_{\text{period},t}}{ATR_t} \right) \times 100$
    若 $ATR_t = 0$，则 $+DI_{\text{period},t}$ 和 $-DI_{\text{period},t}$ 通常设为0或根据具体实现处理。

步骤 5: 计算趋向指数 (DX)
    对于每个时间点 $t$:
    $\text{sumDI} = +DI_{\text{period},t} + (-DI_{\text{period},t})$
    如果 $\text{sumDI} \neq 0$:
        $DX_t = \left( \frac{|+DI_{\text{period},t} - (-DI_{\text{period},t})|}{\text{sumDI}} \right) \times 100$
    否则:
        $DX_t = 0$

步骤 6: 计算平均趋向指数 (ADX)
    使用参数 `period` 对 $DX_t$ 序列进行 Wilder's Smoothing。
    *   计算第一个 $ADX_{\text{period}}$ (即第 `period` 个 $DX$ 值计算出来后的第一个 $ADX$值): 需要 `period` 个 $DX$ 值 (这些 $DX$ 值本身也需要之前的 `period` 个平滑数据)，计算其简单平均值。例如，在拥有 $DX_1, DX_2, ..., DX_{\text{period}}$ 之后，第一个 $ADX$ 值是这些 $DX$ 值的平均。
    *   后续 $ADX_t = (ADX_{t-1} \times (\text{period}-1) + DX_t) / \text{period}$
    为了产生第一个 $ADX$ 值，总共需要 $2 \times \text{period} - 1$ 个原始价格数据点。
    ($\text{period}$ 个数据点计算第一个TR/DM序列，之后 $\text{period}-1$ 个TR/DM用于计算第一个ATR/smoothDM/DI/DX, 之后 $\text{period}$ 个 DX 用于计算第一个ADX)。

步骤 7: 计算平均趋向指数评估 (ADXR)
    一旦获得了 ADX 序列：
    对于每个时间点 $t$ (要求 $t$ 时刻的 ADX 和 $t-(\text{period}-1)$ 时刻的 ADX 都已计算出来)：
    $ADXR_t = \frac{ADX_t + ADX_{t - (\text{period} - 1)}}{\text{2}}$
    第一个 ADXR 值的计算需要当前 ADX 值和 $(\text{period}-1)$ 个周期之前的 ADX 值。因此，ADXR 的起始点会比 ADX 的起始点晚 $(\text{period}-1)$ 个周期。

【6. 备注与参数说明】

*   **参数 `period`**: 这是 ADXR 计算中最重要的参数，它同时用于 ADX 的计算（包括 TR, +DM, -DM, DX 的平滑）。通常默认值为14。较短的周期使指标更敏感，较长的周期使其更平滑。
*   **数据预处理**: 确保输入的最高价、最低价、收盘价数据序列是干净且对齐的。
*   **初始值**: Wilder's Smoothing 的初始值是前 N 个数据的简单移动平均。这意味着指标的初始值需要一定数量的历史数据才能稳定。ADX 的计算需要 $2 \times \text{period} - 1$ 条数据来产生第一个有效值。ADXR 则在此基础上再延迟 $\text{period}-1$ 条数据。因此，第一个 ADXR 值需要 $ (2 \times \text{period} - 1) + (\text{period} - 1) = 3 \times \text{period} - 2 $ 条原始价格数据才能产生。
*   **无取整**: TALib 的 C 源码中明确指出，最终的 ADXR 计算 `(adx[i] + adx[j]) / 2.0` 不进行类似 Wilder 书中早期手动计算时可能进行的取整操作，以保证计算机计算的精度。
*   **应用**: ADXR 通过对 ADX 进行进一步平滑，有助于过滤掉 ADX 的一些短期波动，提供对趋势强度更稳定的评估。它常与 +DI 和 -DI 结合使用来判断趋势方向和强度。

【因子信息结束】===============================================================