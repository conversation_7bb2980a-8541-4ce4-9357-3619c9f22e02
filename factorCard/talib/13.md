【因子信息开始】===============================================================

【因子编号和名称】

因子编号: F023 (示例编号)
因子中文名称: 阿隆振荡器 (Aroon Oscillator, AROONOSC)

【1. 因子名称详情】

因子全称: 阿隆振荡器 (Aroon Oscillator)
英文简称: AROONOSC

【2. 核心公式】

阿隆振荡器衡量价格达到其N周期内最高点或最低点以来经过的时间，并将其表示为振荡器形式。
它首先计算阿隆指标的两个组成部分：阿隆升轨 (Aroon-Up) 和阿隆降轨 (Aroon-Down)。

1.  **阿隆升轨 (Aroon-Up)**:
    `$$ \text{AroonUp}_t = \left( \frac{N - (\text{自N周期内最高价出现以来的周期数}_t)}{N} \right) \times 100 $$`

2.  **阿隆降轨 (Aroon-Down)**:
    `$$ \text{AroonDown}_t = \left( \frac{N - (\text{自N周期内最低价出现以来的周期数}_t)}{N} \right) \times 100 $$`

3.  **阿隆振荡器 (Aroon Oscillator)**:
    `$$ \text{AroonOscillator}_t = \text{AroonUp}_t - \text{AroonDown}_t $$`

    上述公式可以简化为（这也是源码中采用的经典计算方式）：
    `$$ \text{AroonOscillator}_t = \frac{100}{N} \times (\text{N周期内最高价的绝对索引}_t - \text{N周期内最低价的绝对索引}_t) $$`

    其中，“绝对索引”指的是在整个时间序列数据中的位置索引。

【3. 变量定义】

*   `t`: 当前计算周期的时间点（例如，天）。
*   `N`: 计算阿隆指标的时间窗口长度（周期数），即 `optInTimePeriod`。
*   `\text{High}_i`: 在周期 `i` 的最高价。
*   `\text{Low}_i`: 在周期 `i` 的最低价。
*   `\text{自N周期内最高价出现以来的周期数}_t`: 在从 `t-N+1` 到 `t` (包含`t`) 的 `N` 个周期内，找到最高价 `\text{High}_k`。此变量即为 `t-k`。如果最高价出现在当前周期 `t`，则此值为0。
*   `\text{自N周期内最低价出现以来的周期数}_t`: 在从 `t-N+1` 到 `t` (包含`t`) 的 `N` 个周期内，找到最低价 `\text{Low}_j`。此变量即为 `t-j`。如果最低价出现在当前周期 `t`，则此值为0。
*   `\text{N周期内最高价的绝对索引}_t`: 在包含当前周期 `t` 的最近 `N` 个周期（即时间窗口 `[t-N+1, t]`）内，最高价出现那天的原始数据序列中的绝对索引值。
*   `\text{N周期内最低价的绝对索引}_t`: 在包含当前周期 `t` 的最近 `N` 个周期（即时间窗口 `[t-N+1, t]`）内，最低价出现那天的原始数据序列中的绝对索引值。

【4. 函数与方法说明】

1.  **N周期最高价的识别与索引获取**:
    对于当前周期 `t`，考察回溯 `N` 个周期的时间窗口 `W_t = [\text{data}_{t-N+1}, \dots, \text{data}_t]`。
    在该窗口 `W_t` 内的最高价序列 `\text{High}` 中，找到最大值。记录下这个最大值在原始完整时间序列中的绝对索引。

2.  **N周期最低价的识别与索引获取**:
    同理，对于当前周期 `t`，考察回溯 `N` 个周期的时间窗口 `W_t`。
    在该窗口 `W_t` 内的最低价序列 `\text{Low}` 中，找到最小值。记录下这个最小值在原始完整时间序列中的绝对索引。

【5. 计算步骤】

1.  **数据准备**: 准备包含历史最高价 (`High`) 和最低价 (`Low`) 的时间序列数据。确定参数 `N` (时间窗口长度)。
2.  **初始化**:
    *   第一个有效的阿隆振荡器值需要至少 `N` 个周期的数据。因此，计算从第 `N` 个数据点开始（如果数据从索引0开始，则为索引 `N-1` 的数据点）。
    *   对于每个计算点 `t` (从 `N-1` 开始，假设0基索引):
        a.  **确定考察窗口**: 定义当前考察的时间窗口为从 `t-N+1` 到 `t` 的 `N` 个周期。
        b.  **寻找窗口内最高价及其索引**:
            *   在当前窗口的最高价数据（`High[t-N+1]` 至 `High[t]`）中，找到最大值。
            *   记录该最大值在原始完整最高价时间序列中的绝对索引，称之为  `\text{IndexH}_t`。
            *   (对应于详细公式的计算): `\text{PeriodsSinceHigh}_t = t - \text{IndexH}_t` (如果`IndexH_t`是0基索引)。
            *   (对应于详细公式的计算): `\text{AroonUp}_t = ((N - \text{PeriodsSinceHigh}_t) / N) \times 100`。
        c.  **寻找窗口内最低价及其索引**:
            *   在当前窗口的最低价数据（`Low[t-N+1]` 至 `Low[t]`）中，找到最小值。
            *   记录该最小值在原始完整最低价时间序列中的绝对索引，称之为  `\text{IndexL}_t`。
            *   (对应于详细公式的计算): `\text{PeriodsSinceLow}_t = t - \text{IndexL}_t` (如果`IndexL_t`是0基索引)。
            *   (对应于详细公式的计算): `\text{AroonDown}_t = ((N - \text{PeriodsSinceLow}_t) / N) \times 100`。
        d.  **计算阿隆振荡器 (经典简化法)**:
            `$$ \text{AroonOscillator}_t = \frac{100}{N} \times (\text{IndexH}_t - \text{IndexL}_t) $$`
            (这等价于 `\text{AroonUp}_t - \text{AroonDown}_t`，因为
            `\text{AroonUp}_t - \text{AroonDown}_t = \frac{100}{N} \times [ (N - (t - \text{IndexH}_t)) - (N - (t - \text{IndexL}_t)) ]`
            `= \frac{100}{N} \times [ N - t + \text{IndexH}_t - N + t - \text{IndexL}_t ]`
            `= \frac{100}{N} \times (\text{IndexH}_t - \text{IndexL}_t)` )

3.  **迭代计算**: 对后续的每个周期 `t`，重复步骤 2.a 至 2.d。在寻找窗口内的最高/最低价及其索引时，可以通过优化算法来避免每次都完全重新扫描整个窗口：
    *   当窗口向前滑动一个周期时，新的数据点进入窗口，最旧的数据点离开窗口。
    *   如果离开窗口的数据点恰好是之前记录的最高价（或最低价）所在点，则需要在新的窗口内（不含离开点，包含新进点）重新搜索最高价（或最低价）及其索引。
    *   如果新进入窗口的数据点高于（或低于）当前记录的窗口最高价（或最低价），则更新最高价（或最低价）及其索引为新数据点的信息。
    *   否则，之前记录的最高价（或最低价）及其索引仍然有效（只要它没离开窗口）。

【6. 备注与参数说明】

*   **参数 `N` (optInTimePeriod)**:
    *   表示计算阿隆指标的时间周期长度。
    *   TALIB中默认值为14。
    *   其取值范围通常建议在2到100000之间。较短的周期对价格变化更敏感，产生更多信号；较长的周期更平滑，信号较少但可能更可靠。
*   **数据预处理**:
    *   需要有效的最高价和最低价时间序列数据。
    *   输入数据序列的长度必须至少为 `N` 才能产生第一个阿隆振荡器值。
*   **输出范围**: 阿隆振荡器的值域为 -100 到 +100。
    *   接近 +100 表示强劲的上升趋势（近期高点频繁出现且非常接近当前周期，近期低点出现较早）。
    *   接近 -100 表示强劲的下降趋势（近期低点频繁出现且非常接近当前周期，近期高点出现较早）。
    *   围绕0波动可能表示趋势不明朗或震荡。
*   **起始计算点**: 第一个阿隆振荡器值的计算需要 `N` 个数据点。因此，输出序列的起始点会比输入序列晚 `N-1` 个点（或者说，第一个输出值对应输入序列的第 `N` 个点，TALIB中`outBegIdx`通常为`optInTimePeriod`，对应0基输入数组的第`optInTimePeriod`个元素，这意味着前`optInTimePeriod`个元素被用于计算第一个输出）。

【因子信息结束】===============================================================