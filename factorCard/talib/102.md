【因子信息开始】===============================================================

【因子编号和名称】

因子编号: SAREXT_001: 抛物线转向指标扩展版 (Parabolic Stop and Reverse Extended, SAREXT)

【1. 因子名称详情】

因子的完整名称为“抛物线转向指标扩展版”，英文名称为 Parabolic Stop and Reverse Extended，简称 SAREXT。它是一种趋势跟踪止损和反转（Stop and Reverse, SAR）系统，用于指示当前趋势的方向以及潜在的趋势反转点。扩展版允许对加速因子和反转行为进行更细致的参数控制。输出值为正表示当前为上涨趋势的SAR值，输出值为负表示当前为下跌趋势的SAR值 (其绝对值为SAR值)。

【2. 核心公式】

SAREXT的计算是迭代的，依赖于前一期的状态。其核心思想是，SAR点会朝着当前趋势的方向移动，移动速度由加速因子(AF)控制。当价格触及或穿透SAR点时，趋势发生反转。

**基本迭代公式:**
1.  **下一期SAR的计算 (无趋势反转时):**
    $SAR_{t+1} = SAR_t + AF_t \times (EP_t - SAR_t)$

    其中：
    *   $SAR_{t+1}$ 是为 $t+1$ 期计算的SAR值。
    *   $SAR_t$ 是当前 $t$ 期的SAR值 (在没有反转的情况下，它是上一期计算出的 $SAR_{t \to current}$；如果发生反转，它是反转SAR值)。
    *   $AF_t$ 是当前 $t$ 期的加速因子。
    *   $EP_t$ 是当前 $t$ 期的极点价格 (Extreme Point)。

2.  **加速因子 (AF) 的更新:**
    *   **上涨趋势中:** 如果当期最高价 $H_t > EP_{t-1}$ (创出新高):
        $AF_t = \min(AF_{t-1} + \text{AFLongStep}, \text{AFMaxLong})$
    *   **下跌趋势中:** 如果当期最低价 $L_t < EP_{t-1}$ (创出新低):
        $AF_t = \min(AF_{t-1} + \text{AFShortStep}, \text{AFMaxShort})$
    *   若未创出新的极点价格，则 $AF_t = AF_{t-1}$。
    *   趋势反转时，AF会重置为对应的初始加速因子 (`AFInitLong` 或 `AFInitShort`)。

3.  **趋势反转时的SAR值:**
    *   **从上涨趋势反转为下跌趋势时:** 初始反转SAR值设为前一上涨趋势的极高点 $EP_{long, t-1}$。
        $SAR_{reversal} = EP_{long, t-1}$
        然后根据参数调整： $SAR_{reversal} = SAR_{reversal} \times (1 + \text{OffsetOnReverse})$
    *   **从下跌趋势反转为上涨趋势时:** 初始反转SAR值设为前一下跌趋势的极低点 $EP_{short, t-1}$。
        $SAR_{reversal} = EP_{short, t-1}$
        然后根据参数调整： $SAR_{reversal} = SAR_{reversal} \times (1 - \text{OffsetOnReverse})$

4.  **SAR值的边界限制:**
    *   **上涨趋势中:** 计算出的 $SAR_{t+1}$ 不能高于 $L_{t-1}$ 和 $L_t$。
        $SAR_{t+1} = \min(SAR_{t+1, calculated}, L_{t-1}, L_t)$
        反转到下跌趋势时，反转SAR值不能低于 $H_{t-1}$ 和 $H_t$。
        $SAR_{reversal} = \max(SAR_{reversal, calculated}, H_{t-1}, H_t)$
    *   **下跌趋势中:** 计算出的 $SAR_{t+1}$ 不能低于 $H_{t-1}$ 和 $H_t$。
        $SAR_{t+1} = \max(SAR_{t+1, calculated}, H_{t-1}, H_t)$
        反转到上涨趋势时，反转SAR值不能高于 $L_{t-1}$ 和 $L_t$。
        $SAR_{reversal} = \min(SAR_{reversal, calculated}, L_{t-1}, L_t)$

【3. 变量定义】

*   $H_t$: 第 $t$ 期的最高价。
*   $L_t$: 第 $t$ 期的最低价。
*   $SAR_t$: 第 $t$ 期的抛物线转向指标值。这是用于当前周期的止损位。
*   $SAR_{t+1}$: 为第 $t+1$ 期计算的抛物线转向指标值。
*   $EP_t$: 第 $t$ 期的极点价格。在上涨趋势中，为迄今为止的最高价；在下跌趋势中，为迄今为止的最低价。
*   $AF_t$: 第 $t$ 期的加速因子。
*   $Trend_t$: 第 $t$ 期的趋势方向 (1表示上涨，-1表示下跌或0表示下跌)。
*   $\text{optInStartValue}$: (参数) 初始SAR值和方向。如果为0，则自动检测；如果 > 0，则初始为多头趋势，SAR值为该值；如果 < 0，则初始为空头趋势，SAR值为其绝对值。
*   $\text{optInOffsetOnReverse}$: (参数) 趋势反转时，对新SAR值的调整百分比。
*   $\text{optInAccelerationInitLong}$: (参数) 上涨趋势的初始加速因子。
*   $\text{optInAccelerationLong}$: (参数) 上涨趋势的加速因子步长 (即每次AF的增加量)。
*   $\text{optInAccelerationMaxLong}$: (参数) 上涨趋势的最大加速因子。
*   $\text{optInAccelerationInitShort}$: (参数) 下跌趋势的初始加速因子。
*   $\text{optInAccelerationShort}$: (参数) 下跌趋势的加速因子步长。
*   $\text{optInAccelerationMaxShort}$: (参数) 下跌趋势的最大加速因子。
*   $OutputSAREXT_t$: 第 $t$ 期最终输出的SAREXT值。如果 $Trend_t$ 为上涨，则 $OutputSAREXT_t = SAR_t$；如果 $Trend_t$ 为下跌，则 $OutputSAREXT_t = -SAR_t$。

【4. 函数与方法说明】

*   $\min(x_1, x_2, ..., x_n)$: 返回 $x_1, x_2, ..., x_n$ 中的最小值。
*   $\max(x_1, x_2, ..., x_n)$: 返回 $x_1, x_2, ..., x_n$ 中的最大值。
*   $abs(x)$: 返回 $x$ 的绝对值。
*   方向性运动 (-DM, PlusDM): 用于当 $\text{optInStartValue} = 0$ 时确定初始趋势。
    *   $UpMove_t = H_t - H_{t-1}$
    *   $DownMove_t = L_{t-1} - L_t$
    *   $+DM_t = \begin{cases} UpMove_t & \text{if } UpMove_t > DownMove_t \text{ and } UpMove_t > 0 \\ 0 & \text{otherwise} \end{cases}$
    *   $-DM_t = \begin{cases} DownMove_t & \text{if } DownMove_t > UpMove_t \text{ and } DownMove_t > 0 \\ 0 & \text{otherwise} \end{cases}$
    当 $\text{optInStartValue} = 0$ 时，比较第一个有效周期的 $+DM$ 和 $-DM$ 来确定初始趋势。如果 $-DM > +DM$，则初始趋势为下跌，否则为上涨。

【5. 计算步骤】

SAREXT的计算至少需要2个价格周期的数据来开始。第一个价格周期的数据用于设定初始的参考点，第二个周期开始输出SAREXT的值。假设我们有价格序列 $H_0, L_0, H_1, L_1, ..., H_N, L_N$。

**A. 初始化 (计算第一个SAREXT值，对应于 $t=1$ 时刻的价格 $H_1, L_1$)**

1.  **确定初始趋势 ($Trend_1$) 和初始SAR ($SAR_{initial}$):**
    *   **如果 $\text{optInStartValue} > 0$ (指定初始为上涨趋势):**
        *   $Trend_1 = \text{上涨}$
        *   $SAR_{initial} = \text{optInStartValue}$
        *   $EP_1 = H_1$ (第一个有效周期的最高价)
        *   $AF_1 = \text{optInAccelerationInitLong}$
    *   **如果 $\text{optInStartValue} < 0$ (指定初始为下跌趋势):**
        *   $Trend_1 = \text{下跌}$
        *   $SAR_{initial} = abs(\text{optInStartValue})$
        *   $EP_1 = L_1$ (第一个有效周期的最低价)
        *   $AF_1 = \text{optInAccelerationInitShort}$
    *   **如果 $\text{optInStartValue} = 0$ (自动检测初始趋势):**
        *   使用 $H_0, L_0, H_1, L_1$ 计算 $+DM_1$ 和 $-DM_1$。
        *   如果 $-DM_1 > +DM_1$:
            *   $Trend_1 = \text{下跌}$
            *   $SAR_{initial} = H_0$ (前一周期最高价)
            *   $EP_1 = L_1$
            *   $AF_1 = \text{optInAccelerationInitShort}$
        *   否则 ($+DM_1 \ge -DM_1$):
            *   $Trend_1 = \text{上涨}$
            *   $SAR_{initial} = L_0$ (前一周期最低价)
            *   $EP_1 = H_1$
            *   $AF_1 = \text{optInAccelerationInitLong}$

2.  **第一个输出值 $OutputSAREXT_1$:**
    *   如果 $Trend_1 = \text{上涨}$: $OutputSAREXT_1 = SAR_{initial}$
    *   如果 $Trend_1 = \text{下跌}$: $OutputSAREXT_1 = -SAR_{initial}$

3.  **为下一周期 ($t=2$) 计算 $SAR_2$:**
    *   $SAR_{candidate, 2} = SAR_{initial} + AF_1 \times (EP_1 - SAR_{initial})$
    *   **边界限制:**
        *   如果 $Trend_1 = \text{上涨}$: $SAR_2 = \min(SAR_{candidate, 2}, L_0, L_1)$
        *   如果 $Trend_1 = \text{下跌}$: $SAR_2 = \max(SAR_{candidate, 2}, H_0, H_1)$
    *   将 $SAR_2, EP_1, AF_1, Trend_1$ 作为下一轮计算的输入 ($SAR_{current}, EP_{prev}, AF_{prev}, Trend_{prev}$)。

**B. 迭代计算 (对于 $t = 2, 3, ..., N$)**

在每一期 $t$，我们有上一期计算得到的用于本期的参数: $SAR_{current}$ (即 $SAR_t$ 的止损位), $EP_{prev}$ (即 $EP_{t-1}$), $AF_{prev}$ (即 $AF_{t-1}$), $Trend_{prev}$ (即 $Trend_{t-1}$)。
同时需要本期价格 $H_t, L_t$ 和上一期价格 $H_{t-1}, L_{t-1}$。

1.  **判断趋势是否反转，并确定当前周期的 $OutputSAREXT_t, SAR_t, EP_t, AF_t, Trend_t$**:

    *   **如果 $Trend_{prev} = \text{上涨}$:**
        *   **反转判断:** 如果 $L_t \le SAR_{current}$:
            *   $Trend_t = \text{下跌}$ (趋势反转)
            *   $SAR_t = EP_{prev}$ (SAR值设为前一趋势的极点)
            *   **边界限制(下跌反转SAR):** $SAR_t = \max(SAR_t, H_{t-1}, H_t)$
            *   **应用反转偏移:** $SAR_t = SAR_t \times (1 + \text{optInOffsetOnReverse})$
            *   $OutputSAREXT_t = -SAR_t$
            *   $AF_t = \text{optInAccelerationInitShort}$ (重置AF)
            *   $EP_t = L_t$ (新的极点价格为当前最低价)
        *   **无反转 (继续上涨):**
            *   $Trend_t = \text{上涨}$
            *   $OutputSAREXT_t = SAR_{current}$ (输出值为当前SAR)
            *   $SAR_t = SAR_{current}$ (用于计算下一期SAR的基准)
            *   如果 $H_t > EP_{prev}$ (创出新高):
                *   $EP_t = H_t$
                *   $AF_t = \min(AF_{prev} + \text{optInAccelerationLong}, \text{optInAccelerationMaxLong})$
            *   否则 (未创新高):
                *   $EP_t = EP_{prev}$
                *   $AF_t = AF_{prev}$

    *   **如果 $Trend_{prev} = \text{下跌}$:**
        *   **反转判断:** 如果 $H_t \ge SAR_{current}$:
            *   $Trend_t = \text{上涨}$ (趋势反转)
            *   $SAR_t = EP_{prev}$ (SAR值设为前一趋势的极点)
            *   **边界限制(上涨反转SAR):** $SAR_t = \min(SAR_t, L_{t-1}, L_t)$
            *   **应用反转偏移:** $SAR_t = SAR_t \times (1 - \text{optInOffsetOnReverse})$
            *   $OutputSAREXT_t = SAR_t$
            *   $AF_t = \text{optInAccelerationInitLong}$ (重置AF)
            *   $EP_t = H_t$ (新的极点价格为当前最高价)
        *   **无反转 (继续下跌):**
            *   $Trend_t = \text{下跌}$
            *   $OutputSAREXT_t = -SAR_{current}$ (输出值为当前SAR的负值)
            *   $SAR_t = SAR_{current}$ (用于计算下一期SAR的基准)
            *   如果 $L_t < EP_{prev}$ (创出新低):
                *   $EP_t = L_t$
                *   $AF_t = \min(AF_{prev} + \text{optInAccelerationShort}, \text{optInAccelerationMaxShort})$
            *   否则 (未创新低):
                *   $EP_t = EP_{prev}$
                *   $AF_t = AF_{prev}$

2.  **计算下一期的SAR值 ($SAR_{next\_period}$，即 $SAR_{t+1}$):**
    *   $SAR_{candidate, t+1} = SAR_t + AF_t \times (EP_t - SAR_t)$
      (注意: 这里的 $SAR_t, EP_t, AF_t$ 是步骤B.1中根据是否反转更新后的值)
    *   **边界限制:**
        *   如果 $Trend_t = \text{上涨}$: $SAR_{next\_period} = \min(SAR_{candidate, t+1}, L_{t-1}, L_t)$
        *   如果 $Trend_t = \text{下跌}$: $SAR_{next\_period} = \max(SAR_{candidate, t+1}, H_{t-1}, H_t)$

3.  **准备下一轮迭代:**
    *   将 $SAR_{next\_period}$ 赋给 $SAR_{current}$ 供下一期使用。
    *   将 $EP_t$ 赋给 $EP_{prev}$ 供下一期使用。
    *   将 $AF_t$ 赋给 $AF_{prev}$ 供下一期使用。
    *   将 $Trend_t$ 赋给 $Trend_{prev}$ 供下一期使用。

4.  重复步骤 B.1 至 B.3 直到所有数据处理完毕。

【6. 备注与参数说明】

*   **数据预处理:** 需要至少两期的高低价数据才能开始计算。第一期的SAREXT值基于前一期和当前期的数据进行初始化。
*   **参数选择:**
    *   $\text{optInStartValue}$: (默认0) 若为0，根据最初两日价格的DM指标决定初始方向；若非0，则直接指定初始方向和SAR值。
    *   $\text{optInOffsetOnReverse}$: (默认0) 反转时对新SAR的额外调整幅度。例如0.01表示1%。正值在多转空时增加SAR，负值在空转多时减少SAR (源码中是 `sar += sar * offset` 和 `sar -= sar * offset`)。
    *   **上涨趋势参数:**
        *   $\text{optInAccelerationInitLong}$: (默认0.02) 上涨趋势初始加速因子。
        *   $\text{optInAccelerationLong}$: (默认0.02) 上涨趋势加速因子增长步长。
        *   $\text{optInAccelerationMaxLong}$: (默认0.20) 上涨趋势加速因子上限。
    *   **下跌趋势参数:**
        *   $\text{optInAccelerationInitShort}$: (默认0.02) 下跌趋势初始加速因子。
        *   $\text{optInAccelerationShort}$: (默认0.02) 下跌趋势加速因子增长步长。
        *   $\text{optInAccelerationMaxShort}$: (默认0.20) 下跌趋势加速因子上限。
*   **参数一致性调整:** 如果设置的初始AF大于最大AF，则初始AF会被设为最大AF。如果AF步长大于最大AF，则AF步长会被设为最大AF (源码中是如果 $\text{optInAccelerationLong} > \text{optInAccelerationMaxLong}$，则 $\text{optInAccelerationLong} = \text{optInAccelerationMaxLong}$，这似乎是笔误，通常应该是初始值和步长本身小于等于最大值)。
*   **输出值符号:** SAREXT指标输出值的正负号代表了当前SAR所指示的趋势方向。正值表示当前处于上涨趋势（SAR在价格下方作为支撑），负值表示当前处于下跌趋势（SAR的绝对值在价格上方作为压力）。这与标准SAR不同，标准SAR仅输出SAR值本身。
*   **窗口期 (Lookback):** 计算SAREXT第一个有效值至少需要回看1个周期的数据（总共2个数据点）。

【因子信息结束】===============================================================