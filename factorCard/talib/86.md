【因子信息开始】===============================================================

【因子编号和名称】
因子编号: (待分配)
因子中文名称: 负向运动 (Minus Directional Movement, -DM)

【1. 因子名称详情】
因子的完整名称为“负向运动”，英文名称为“Minus Directional Movement”，通常简称为“-DM”或“DM-”。该因子衡量在给定周期内，价格向下的运动幅度。它是构建平均动向指数（ADX）等指标的基础组成部分。

【2. 核心公式】
负向运动的计算分为两步：首先计算单周期的原始负向运动（$raw\textDM1^-_t$），然后对这些原始值进行平滑处理得到最终的 $N$ 周期负向运动 ($-\text{DM}_{N,t}$)。

1.  **单周期原始负向运动 ($raw\textDM1^-_t$)**:
    先定义当期向上的价格变动 ($UpMove_t$) 和当期向下的价格变动 ($DownMove_t$):
    $UpMove_t = H_t - H_{t-1}$
    $DownMove_t = L_{t-1} - L_t$

    其中 $H_t$ 和 $L_t$ 分别是当期最高价和最低价，$H_{t-1}$ 和 $L_{t-1}$ 分别是上一期最高价和最低价。

    然后，单周期原始负向运动 $raw\textDM1^-_t$ 定义为：
    $raw\textDM1^-_t = \begin{cases} DownMove_t & \text{if } DownMove_t > 0 \text{ and } DownMove_t > UpMove_t \\ 0 & \text{otherwise} \end{cases}$

2.  **$N$ 周期平滑负向运动 ($-\text{DM}_{N,t}$)**:
    采用 Wilder's Smoothing 方法（一种特殊的指数移动平均）。
    *   如果周期 $N=1$:
        $-\text{DM}_{1,t} = raw\textDM1^-_t$
    *   如果周期 $N>1$:
        对于序列的第一个 $-\text{DM}_{N}$ 值，其计算基于一个初始累加值和后续的迭代平滑。TALIB 的具体实现中，第一个输出的 $-\text{DM}_{N}$ 值是经过特定初始化和平滑稳定期后的结果。
        后续的 $-\text{DM}_{N,t}$ 通过以下递归公式计算：
        $-\text{DM}_{N,t} = -\text{DM}_{N,t-1} - \frac{-\text{DM}_{N,t-1}}{N} + raw\textDM1^-_t$
        这等价于：
        $-\text{DM}_{N,t} = (1 - \frac{1}{N}) \cdot -\text{DM}_{N,t-1} + raw\textDM1^-_t$

【3. 变量定义】
*   $H_t$: 第 $t$ 期的最高价。
*   $L_t$: 第 $t$ 期的最低价。
*   $H_{t-1}$: 第 $t-1$ 期的最高价。
*   $L_{t-1}$: 第 $t-1$ 期的最低价。
*   $UpMove_t$: 第 $t$ 期价格向上突破前一期高点的幅度。
*   $DownMove_t$: 第 $t$ 期价格向下突破前一期低点的幅度。
*   $raw\textDM1^-_t$: 第 $t$ 期的单周期原始负向运动值。
*   $-\text{DM}_{N,t}$: 第 $t$ 期，周期为 $N$ 的平滑负向运动值。
*   $-\text{DM}_{N,t-1}$: 第 $t-1$ 期，周期为 $N$ 的平滑负向运动值。
*   $N$: 计算负向运动的时间周期参数（例如，14天）。

【4. 函数与方法说明】
*   **Wilder's Smoothing (威尔德平滑法)**:
    这是一种特殊的指数移动平均 (EMA)，其平滑系数 $\alpha = 1/N$。
    计算公式为: $SMMA_t = \frac{Input_t + (N-1) \cdot SMMA_{t-1}}{N}$
    整理后即为: $SMMA_t = SMMA_{t-1} - \frac{SMMA_{t-1}}{N} + Input_t$
    其中，$SMMA_t$ 是当前周期的平滑值，$SMMA_{t-1}$ 是前一周期的平滑值，$Input_t$ 是当前周期的输入值 (在此因子中为 $raw\textDM1^-_t$)，$N$ 是平滑周期。
    在计算序列的第一个平滑值时，不同的实现有不同的初始化方法。TALIB 的实现中，会先对特定数量的初始 $raw\textDM1^-$ 值进行累加作为种子，然后通过迭代应用上述平滑公式一段“不稳定周期”后，才开始输出第一个正式的平滑值。

【5. 计算步骤】
1.  **数据准备**: 获取时间序列数据，包括每个周期的最高价 ($H_t$) 和最低价 ($L_t$)。设定参数 $N$ (时间周期，`optInTimePeriod`)。

2.  **计算单周期向上和向下移动**:
    对于时间序列中的每个周期 $t$ (从第二个数据点开始，因需要 $t-1$ 期的数据)：
    *   $UpMove_t = H_t - H_{t-1}$
    *   $DownMove_t = L_{t-1} - L_t$

3.  **计算单周期原始负向运动 ($raw\textDM1^-_t$)**:
    对于每个周期 $t$ (从第二个数据点开始)：
    *   如果 $DownMove_t > 0$ 且 $DownMove_t > UpMove_t$，则 $raw\textDM1^-_t = DownMove_t$。
    *   否则 (即 $DownMove_t_ \le 0$ 或者 $DownMove_t \le UpMove_t$)，$raw\textDM1^-_t = 0$。
    这将生成一个 $raw\textDM1^-$ 的时间序列，其第一个值对应原始价格数据的第二个点。

4.  **计算周期为 $N$ 的平滑负向运动 ($-\text{DM}_{N,t}$)**:

    *   **情况 A: 如果 $N=1$ (无平滑)**
        对于 $raw\textDM1^-$ 序列中的每个值 $raw\textDM1^-_t$：
        $-\text{DM}_{1,t} = raw\textDM1^-_t$
        第一个 $-\text{DM}_{1}$ 值对应原始数据的第二个点产生 $raw\textDM1^-_1$。

    *   **情况 B: 如果 $N>1$ (采用Wilder平滑)**
        a.  **初始化种子值**: 计算 $raw\textDM1^-$ 序列中前 $N-1$ 个值的和 (即使用从原始价格数据第2个点到第N个点计算得出的 $raw\textDM1^-_1, \dots, raw\textDM1^-_{N-1}$ 进行累加)。
            $Seed\textDM = \sum_{k=1}^{N-1} raw\textDM1^-_k$
        b.  **迭代平滑与稳定期**:
            将 $Seed\textDM$ 作为第一次平滑计算时的“前一期平滑值”。
            接下来，对于 $raw\textDM1^-$ 序列中的第 $N$ 个值 ($raw\textDM1^-_N$) 以及其后的每一个 $raw\textDM1^-_k$ (对应原始价格序列的第 $N+1$ 个点及以后)， последовательно (sequentially) 应用Wilder平滑公式:
            Let $Prev\textDM_N = Seed\textDM$ (for the first iteration below)
            Iterate $k$ from $N$ up to $N + \text{UnstablePeriod} - 1$:
            Current $raw\textDM1^-_k$ is used.
            $Temp\textDM_N = Prev\textDM_N - \frac{Prev\textDM_N}{N} + raw\textDM1^-_k$
            $Prev\textDM_N = Temp\textDM_N$ (for the next iteration in this stabilization phase)
            经过这个预热/稳定期（UnstablePeriod 次迭代，具体次数由 TALIB 内部定义，如 `TA_GLOBALS_UNSTABLE_PERIOD(TA_FUNC_UNST_MINUS_DM,MinusDM)`) 迭代后得到的 $Prev\textDM_N$ 即为第一个输出的 $-\text{DM}_N$ 因子值。这个值对应于原始价格序列中大约第 $N + \text{UnstablePeriod}$ 个数据点。
        c.  **计算后续因子值**:
            对于稳定期之后的每个周期 $t'$ (对应 $raw\textDM1^-$ 序列中 $N + \text{UnstablePeriod}$ 之后的点)，使用前一期的最终平滑值 $-\text{DM}_{N,t'-1}$ 和当前周期的 $raw\textDM1^-_{t'}$ 来计算当前的 $-\text{DM}_{N,t'}$:
            $-\text{DM}_{N,t'} = -\text{DM}_{N,t'-1} - \frac{-\text{DM}_{N,t'-1}}{N} + raw\textDM1^-_{t'}$

【6. 备注与参数说明】
*   **参数 $N$ (optInTimePeriod)**: 这是计算负向运动的周期长度。一个常用的值是14。
*   **起始计算点**: 由于需要前一期数据， $raw\textDM1^-$ 的计算从原始数据的第二个点开始。对于平滑后的 $-\text{DM}_N$，第一个有效的输出值会更晚，取决于周期 $N$ 和内部稳定期的长度。通常，计算 $-\text{DM}_N$ 需要至少 $N + \text{稳定期长度}$ 个原始数据点。
*   **稳定期 (Unstable Period)**: 对于指数移动平均类型的计算，初期的几个值可能不够稳定。TALIB 使用一个内部定义的“不稳定周期”来预热计算，使得输出的第一个值已经经过了一定程度的平滑稳定。在重新实现时，可以考虑从何时开始认为平滑值是可靠的，或者简单地从能计算出第一个平滑值时就开始使用，并知晓其初期的不稳定性。
*   **用途**: 负向运动 (-DM) 是构成更复杂技术指标如平均动向指数 (ADX) 和平均动向指数评估指标 (ADXR) 的关键组成部分，常与正向运动 (+DM) 和真实波幅 (TR) 一同使用。
*   **零值情况**: 当天的价格波动完全被前一天的价格区间包含，或者向上突破的幅度大于或等于向下突破的幅度 (且向下运动不为正)，则当期的 $raw\textDM1^-_t$ 为0。

【因子信息结束】===============================================================