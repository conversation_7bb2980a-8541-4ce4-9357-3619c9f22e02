【因子信息开始】===============================================================

【因子编号和名称】

因子编号: (可自行分配) 周期中点值 (MidPoint, MIDPOINT)

【1. 因子名称详情】

因子1: 周期中点值 (MidPoint, MIDPOINT)

【2. 核心公式】

给定一个时间序列数据 $P_t$ 和一个时间周期 $N$，在 $t$ 时刻的周期中点值 $MIDPOINT_t$ 计算如下：

$MIDPOINT_t = \frac{Highest_t(N) + Lowest_t(N)}{2}$

其中：
*   $Highest_t(N)$ 是从时刻 $t-N+1$ 到时刻 $t$ （包含这两个时刻，共 $N$ 个数据点）的输入序列 $P$ 中的最大值。
*   $Lowest_t(N)$ 是从时刻 $t-N+1$ 到时刻 $t$ （包含这两个时刻，共 $N$ 个数据点）的输入序列 $P$ 中的最小值。

【3. 变量定义】

*   $P_t$: 在时刻 $t$ 的输入数据值（例如：收盘价）。这是一个时间序列。
*   $N$: 计算周期，即用于确定最大值和最小值的回顾期长度。
*   $Highest_t(N)$: 在时刻 $t$ 观察到的，过去 $N$ 个周期（包括当前周期 $t$）内的输入数据 $P$ 的最高值。
    $Highest_t(N) = \max(P_{t-N+1}, P_{t-N+2}, \ldots, P_t)$
*   $Lowest_t(N)$: 在时刻 $t$ 观察到的，过去 $N$ 个周期（包括当前周期 $t$）内的输入数据 $P$ 的最低值。
    $Lowest_t(N) = \min(P_{t-N+1}, P_{t-N+2}, \ldots, P_t)$
*   $MIDPOINT_t$: 在时刻 $t$ 计算得到的周期中点值。

【4. 函数与方法说明】

*   $\max(x_1, x_2, \ldots, x_k)$: 此函数返回其参数列表中的最大值。
*   $\min(x_1, x_2, \ldots, x_k)$: 此函数返回其参数列表中的最小值。

【5. 计算步骤】

1.  **数据准备**:
    *   获取输入的时间序列数据 $P = \{P_0, P_1, P_2, \ldots, P_M\}$，其中 $P_i$ 是第 $i$ 个时间点的数据值。
2.  **参数设定**:
    *   确定时间周期 $N$。例如，如果 $N=14$，则使用过去14个周期的数据进行计算。
3.  **初始化**:
    *   由于计算需要 $N$ 个周期的数据，因此第一个有效的 $MIDPOINT$ 值将在第 $N-1$ 个索引位置产生（假设数据索引从0开始）。换句话说，需要至少 $N$ 个数据点才能计算出第一个值。
4.  **迭代计算**:
    对于时间序列中的每个有效点 $t$（从 $t = N-1$ 到 $M$）：
    a.  **确定计算窗口**:
        选取当前时刻 $t$ 及之前的 $N-1$ 个数据点，形成一个包含 $N$ 个数据点的窗口：$\{P_{t-N+1}, P_{t-N+2}, \ldots, P_t\}$。
    b.  **寻找窗口内的最高值**:
        在步骤 a 中确定的数据窗口内，找到最大值。
        $Highest_t(N) = \max(P_{t-N+1}, P_{t-N+2}, \ldots, P_t)$。
    c.  **寻找窗口内的最低值**:
        在步骤 a 中确定的数据窗口内，找到最小值。
        $Lowest_t(N) = \min(P_{t-N+1}, P_{t-N+2}, \ldots, P_t)$。
    d.  **计算中点值**:
        使用公式 $MIDPOINT_t = \frac{Highest_t(N) + Lowest_t(N)}{2}$ 计算当前时刻 $t$ 的中点值。
    e.  将计算得到的 $MIDPOINT_t$ 存储到结果序列中。
5.  **完成**:
    重复步骤 4 直到处理完所有有效的输入数据点。输出序列的第一个有效值对应输入序列的第 $N$ 个数据点（索引 $N-1$）。

【6. 备注与参数说明】

*   **时间周期 (optInTimePeriod / $N$)**:
    *   这是计算中点值所考虑的回溯期长度。
    *   TALIB中此参数的有效范围通常是从2到100000，常用默认值为14。
    *   较短的周期对近期价格变化更敏感，而较长的周期则更平滑。
*   **输入数据 (inReal / $P_t$)**:
    *   该因子通常应用于价格序列（如收盘价），但理论上可以应用于任何数值型时间序列。
*   **数据预处理**:
    *   确保输入数据序列没有缺失值，或者对缺失值进行合理填充，否则可能影响窗口内最高/最低值的准确性。
*   **输出延迟 (Lookback)**:
    *   该因子的计算结果相对于输入数据有 $N-1$ 个周期的延迟。也就是说，第一个 $MIDPOINT$ 值是在观察到 $N$ 个数据点之后才产生的。
*   **与MIDPRICE的区别**:
    *   MIDPOINT 计算的是**单一时间序列**（如收盘价）在过去N期内的最高值和最低值的平均。
    *   而另一个常见的指标 MIDPRICE（周期内价格中位数，通常是 (最高价 + 最低价) / 2）是针对每个周期的**高价(High)和低价(Low)**这两个序列进行计算的，即 $MIDPRICE_t = (High_t + Low_t)/2$。这两个是不同的概念。当前因子 `MIDPOINT` 是基于单一输入序列的。

【因子信息结束】===============================================================