【因子信息开始】===============================================================

【因子编号和名称】

因子编号: SAR_001: 抛物线止损反转指标 (Parabolic Stop and Reverse, SAR)

【1. 因子名称详情】

抛物线止损反转指标 (Parabolic Stop and Reverse, SAR) 是一种用于识别趋势方向和潜在反转点的技术分析工具。它在图表上显示为一系列点，位于价格之上或之下，指示着多头或空头趋势的止损位。当价格触及这些点时，预示着趋势可能发生反转。

【2. 核心公式】

抛物线SAR的计算是迭代的，并且取决于前一期的SAR值、极值点(EP)和加速因子(AF)。其核心思想如下：

1.  **上涨趋势中 (Previous Trend was UP):**
    $SAR_t = SAR_{t-1} + AF_{t-1} \times (EP_{t-1} - SAR_{t-1})$

2.  **下跌趋势中 (Previous Trend was DOWN):**
    $SAR_t = SAR_{t-1} + AF_{t-1} \times (EP_{t-1} - SAR_{t-1})$
    *(公式形式相同，但EP、AF及后续调整规则不同)*

3.  **加速因子 (AF) 更新:**
    如果趋势延续且创出新的极值点 (上涨趋势中创更高高点，下跌趋势中创更低低点):
    $AF_t = \min(AF_{t-1} + AF_{step}, AF_{max})$
    否则:
    $AF_t = AF_{t-1}$
    当趋势反转时:
    $AF_t = AF_{step}$

4.  **极值点 (EP) 更新:**
    上涨趋势中: $EP_t = \max(EP_{t-1}, H_t)$
    下跌趋势中: $EP_t = \min(EP_{t-1}, L_t)$
    当趋势反转时，EP会根据新趋势的第一个周期的高/低点重置。

**符号说明**
*   $SAR_t$: 当前周期的SAR值。
*   $SAR_{t-1}$: 前一计算周期的SAR值 (即传递到当前周期用于计算的SAR值)。
*   $EP_{t-1}$: 前一计算周期结束时确定的极值点。
*   $AF_{t-1}$: 前一计算周期结束时确定的加速因子。
*   $AF_{step}$: 加速因子步长 (参数)。
*   $AF_{max}$: 加速因子最大值 (参数)。
*   $H_t$: 当前周期的最高价。
*   $L_t$: 当前周期的最低价。

【3. 变量定义】

*   $H_t$: 时间点 `t` 的最高价。
*   $L_t$: 时间点 `t` 的最低价。
*   $SAR_t^{out}$: 在时间点 `t` 输出的SAR值。
*   $SAR_{t}^{calc}$: 在时间点 `t-1` 计算的、预备在时间点 `t` 使用的SAR值。
*   $EP_{t-1}$: 在时间点 `t-1` 结束时确定的趋势极值点 (significant point / extreme point)。若是上涨趋势，则为期间的最高价；若是下跌趋势，则为期间的最低价。
*   $AF_{t-1}$: 在时间点 `t-1` 结束时确定的加速因子。
*   $Trend_{t-1}$: 进入时间点 `t` 时的趋势方向 (上涨/下跌)。
*   $AF_{step}$: 加速因子 (AF) 的初始值及每次的增量步长，是一个可调参数。
*   $AF_{max}$: 加速因子 (AF) 的最大上限，是一个可调参数。

【4. 函数与方法说明】

1.  **方向性运动 (Directional Movement) - 用于确定初始趋势:**
    *   上移值 (UpMove): $UpMove_t = H_t - H_{t-1}$
    *   下移值 (DownMove): $DownMove_t = L_{t-1} - L_t$
    *   正方向性运动 ($+DM_t$):
        $+DM_t = \begin{cases} UpMove_t & \text{if } UpMove_t > DownMove_t \text{ and } UpMove_t > 0 \\ 0 & \text{otherwise} \end{cases}$
    *   负方向性运动 ($-DM_t$):
        $-DM_t = \begin{cases} DownMove_t & \text{if } DownMove_t > UpMove_t \text{ and } DownMove_t > 0 \\ 0 & \text{otherwise} \end{cases}$
    *   若 $UpMove_t = DownMove_t$ 或两者均非正数，则 $+DM_t = 0$ 且 $-DM_t = 0$。

【5. 计算步骤】

**A. 初始化 (周期0 和 周期1):**
   SAR的计算至少需要两个价格周期的数据。第一个SAR值 (`SAR_1^{out}`) 是针对数据序列中的第二个价格周期 (`t=1`) 计算的，它需要用到第一个价格周期 (`t=0`) 和第二个价格周期 (`t=1`) 的数据。

   1.  **确定初始趋势方向 (`Trend_1`):**
      *   使用周期0和周期1的最高价 ($H_0, H_1$) 和最低价 ($L_0, L_1$) 计算 $+DM_1$ 和 $-DM_1$。
          *   $UpMove_1 = H_1 - H_0$
          *   $DownMove_1 = L_0 - L_1$
          *   根据上述规则计算 $+DM_1$ 和 $-DM_1$。
      *   如果 $-DM_1 > 0$ (且暗示 $-DM_1 > +DM_1$ 或 $+DM_1=0$)，则初始趋势 `Trend_1` 为 **下跌**。
      *   否则 (包括 $+DM_1 > -DM_1$ 或两者均为0的情况)，初始趋势 `Trend_1` 为 **上涨**。

   2.  **设置初始SAR值、EP和AF (针对周期1):**
      *   (`sar_calc_for_p1` 指的是为周期1计算的SAR值)
      *   如果 `Trend_1` 为 **上涨**:
          *   $SAR_{1}^{calc} = L_0$  (前一周期 `t=0` 的最低价)
          *   $EP_0 = H_1$ (当前周期 `t=1` 的最高价作为初始EP)
      *   如果 `Trend_1` 为 **下跌**:
          *   $SAR_{1}^{calc} = H_0$  (前一周期 `t=0` 的最高价)
          *   $EP_0 = L_1$ (当前周期 `t=1` 的最低价作为初始EP)
      *   $AF_0 = AF_{step}$ (初始加速因子)

**B. 迭代计算 (对于周期 `t = 1, 2, ..., N`):**
   在每个周期 `t` 开始时，我们有来自上一周期 `t-1` 迭代结束时确定的 `Trend_{t-1}` (进入本周期的趋势)、`SAR_{t}^{calc}` (为本周期 `t` 计算的SAR值)、`EP_{t-1}` (上一周期的极值点) 和 `AF_{t-1}` (上一周期的加速因子)。
   同时，获取当前周期 `t` 的价格 $H_t, L_t$ 以及前一周期 `t-1` 的价格 $H_{t-1}, L_{t-1}$。

   1.  **如果 `Trend_{t-1}` 为上涨:**
      *   **检查趋势是否反转:** 如果当前最低价 $L_t \le SAR_{t}^{calc}$：
          *   趋势反转为 **下跌** (`Trend_t = DOWN`)。
          *   **当日SAR输出值:** $SAR_t^{out} = EP_{t-1}$ (使用前一上涨趋势的极值点作为反转日的SAR)。
          *   **SAR值边界调整:** $SAR_t^{out} = \max(SAR_t^{out}, H_{t-1}, H_t)$ (反转SAR不能低于前一日或当日的最高价)。
          *   **为新下跌趋势重置EP和AF:**
              *   $EP_t = L_t$
              *   $AF_t = AF_{step}$
      *   **如果趋势未反转 (继续上涨):**
          *   趋势仍为 **上涨** (`Trend_t = UP`)。
          *   **当日SAR输出值:** $SAR_t^{out} = SAR_{t}^{calc}$。
          *   **更新EP和AF:**
              *   如果 $H_t > EP_{t-1}$ (创出新高):
                  *   $EP_t = H_t$
                  *   $AF_t = \min(AF_{t-1} + AF_{step}, AF_{max})$
              *   否则 (未创出新高):
                  *   $EP_t = EP_{t-1}$
                  *   $AF_t = AF_{t-1}$

   2.  **如果 `Trend_{t-1}` 为下跌:**
      *   **检查趋势是否反转:** 如果当前最高价 $H_t \ge SAR_{t}^{calc}$：
          *   趋势反转为 **上涨** (`Trend_t = UP`)。
          *   **当日SAR输出值:** $SAR_t^{out} = EP_{t-1}$ (使用前一下跌趋势的极值点作为反转日的SAR)。
          *   **SAR值边界调整:** $SAR_t^{out} = \min(SAR_t^{out}, L_{t-1}, L_t)$ (反转SAR不能高于前一日或当日的最低价)。
          *   **为新上涨趋势重置EP和AF:**
              *   $EP_t = H_t$
              *   $AF_t = AF_{step}$
      *   **如果趋势未反转 (继续下跌):**
          *   趋势仍为 **下跌** (`Trend_t = DOWN`)。
          *   **当日SAR输出值:** $SAR_t^{out} = SAR_{t}^{calc}$。
          *   **更新EP和AF:**
              *   如果 $L_t < EP_{t-1}$ (创出新低):
                  *   $EP_t = L_t$
                  *   $AF_t = \min(AF_{t-1} + AF_{step}, AF_{max})$
              *   否则 (未创出新低):
                  *   $EP_t = EP_{t-1}$
                  *   $AF_t = AF_{t-1}$

   3.  **计算下一周期的SAR值 (`SAR_{t+1}^{calc}`):**
      *   $SAR_{t+1}^{calc} = SAR_t^{out} + AF_t \times (EP_t - SAR_t^{out})$

   4.  **`SAR_{t+1}^{calc}` 的边界调整 (重要):**
      *   如果 `Trend_t` 为 **上涨** (即下一周期预期是上涨趋势的SAR):
          *   $SAR_{t+1}^{calc} = \min(SAR_{t+1}^{calc}, L_t, L_{t-1})$ (下一周期的SAR不能高于当前周期或前一周期的最低价)。
      *   如果 `Trend_t` 为 **下跌** (即下一周期预期是下跌趋势的SAR):
          *   $SAR_{t+1}^{calc} = \max(SAR_{t+1}^{calc}, H_t, H_{t-1})$ (下一周期的SAR不能低于当前周期或前一周期的最高价)。

   5.  **状态传递:** 将 $Trend_t, SAR_{t+1}^{calc}, EP_t, AF_t$ 作为下一周期 (`t+1`) 计算的输入。输出 $SAR_t^{out}$。

**C. 输出:**
   对每个计算周期 `t` (从1到N)，输出 $SAR_t^{out}$。

【6. 备注与参数说明】

*   **参数:**
    *   `optInAcceleration` ($AF_{step}$): 加速因子步长。常见默认值为 0.02。
    *   `optInMaximum` ($AF_{max}$): 加速因子最大值。常见默认值为 0.20。
*   **参数约束:**
    *   通常 $AF_{step}$ 应小于或等于 $AF_{max}$。
    *   在此处提供的TA-Lib实现中，如果初始设置的 $AF_{step}$ 大于 $AF_{max}$，则 $AF_{step}$ (即代码中的`optInAcceleration`变量自身在后续使用时) 和初始的当前加速因子 (`af`) 都会被修正为 $AF_{max}$ 的值。这意味着如果步长设得比最大值还大，实际步长会变成最大值，并且加速过程会立即达到最大加速因子。
*   **数据预处理:** 至少需要两个数据点（高价和低价序列）。第一个数据点用于确定初始状态，因此第一个SAR输出值对应输入序列中的第二个数据点。
*   **滞后性:** 第一个SAR值基于之前一个周期的数据来建立初始的极值点（EP）和SAR值，因此SAR指标的输出通常比输入数据序列少一个周期。
*   **四舍五入:** 原始Wilder的描述中可能包含为简化示例而进行的四舍五入。本因子描述基于TA-Lib的实现，其中默认不进行中间步骤的四舍五入 (由`SAR_ROUNDING(x)`宏定义为空可知)。
*   **解释:** SAR点位于价格下方时，通常表示上涨趋势；位于价格上方时，通常表示下跌趋势。当价格穿越SAR点时，被视为趋势反转的信号。SAR也被用作追踪止损点。

【因子信息结束】===============================================================