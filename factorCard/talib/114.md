【因子信息开始】===============================================================

【因子编号和名称】

因子编号: SUB001: 向量算术减法 (Vector Arithmetic Subtraction, SUB)

【1. 因子名称详情】

因子1: 向量算术减法 (Vector Arithmetic Subtraction, SUB)。该因子计算两个输入时间序列（或向量）之间对应元素的差值。

【2. 核心公式】

对于两个输入时间序列 $I_1$ 和 $I_2$，在任意时间点 $t$，输出序列 $O$ 的计算公式如下：

$O_t = I_{1,t} - I_{2,t}$

其中：
*   $O_t$ 是在时间点 $t$ 的输出因子值。
*   $I_{1,t}$ 是第一个输入序列在时间点 $t$ 的值。
*   $I_{2,t}$ 是第二个输入序列在时间点 $t$ 的值。

【3. 变量定义】

*   $I_{1,t}$: 第一个输入数值序列在时间点 $t$ 的值。它可以代表任何数值型数据，如价格、另一个指标的输出等。
*   $I_{2,t}$: 第二个输入数值序列在时间点 $t$ 的值。它可以代表任何数值型数据，通常与第一个输入序列具有相同的性质或维度，以便进行有意义的比较。
*   $O_t$: 输出序列在时间点 $t$ 的值，即 $I_{1,t}$ 与 $I_{2,t}$ 的差。
*   $t$: 时间点或序列中的索引位置。

【4. 函数与方法说明】

该因子不涉及复杂的特殊函数或统计方法。其核心操作是基础的算术减法。
*   **减法运算**: 对两个输入序列中相同位置（同一时间点）的元素执行标准的减法操作。

【5. 计算步骤】

1.  **数据准备**:
    *   准备两个长度相同的数值型时间序列，分别称为输入序列1 ($I_1$) 和输入序列2 ($I_2$)。确保这两个序列在每个对应的时间点 $t$ 上都有定义。
    *   例如，$I_1 = [i_{1,1}, i_{1,2}, ..., i_{1,N}]$，$I_2 = [i_{2,1}, i_{2,2}, ..., i_{2,N}]$，其中 $N$ 是序列的长度。

2.  **逐元素计算差值**:
    *   遍历两个输入序列，从第一个元素 (对应 $t=1$) 到最后一个元素 (对应 $t=N$)。
    *   对于每一个时间点 $t$（或索引位置 $idx$ from $0$ to $N-1$ in programming terms）：
        *   取输入序列1在 $t$ 点的值 $I_{1,t}$。
        *   取输入序列2在 $t$ 点的值 $I_{2,t}$。
        *   计算差值：$O_t = I_{1,t} - I_{2,t}$。
        *   将计算得到的 $O_t$ 存储到输出序列的相应位置。

3.  **输出结果**:
    *   完成所有元素的计算后，得到一个新的时间序列 $O = [o_1, o_2, ..., o_N]$，该序列即为最终的因子值序列。

**示例**:
假设输入序列1 ($I_1$) 为 `[10, 12, 15, 11]`
假设输入序列2 ($I_2$) 为 `[8, 13, 12, 10]`

计算步骤：
*   $O_1 = I_{1,1} - I_{2,1} = 10 - 8 = 2$
*   $O_2 = I_{1,2} - I_{2,2} = 12 - 13 = -1$
*   $O_3 = I_{1,3} - I_{2,3} = 15 - 12 = 3$
*   $O_4 = I_{1,4} - I_{2,4} = 11 - 10 = 1$

最终输出序列 ($O$) 为 `[2, -1, 3, 1]`

【6. 备注与参数说明】

*   **数据对齐**: 两个输入序列必须严格对齐，即在每个时间点 $t$ 上， $I_{1,t}$ 和 $I_{2,t}$ 必须是对应的。如果序列长度不同或未对齐，计算结果将无意义或无法进行。
*   **数据类型**: 输入数据应为数值型。输出结果的数据类型将与输入数据类型一致或能容纳计算结果的数值范围。
*   **无参数**: 此因子本身没有可调参数（如窗口期）。其行为完全由输入的两个序列决定。
*   **应用场景**: 向量算术减法是构建更复杂因子的基础操作。例如，它可以用于计算两个价格序列之间的价差（Spread），或者计算一个指标与其移动平均线之间的差值等。
*   **数据预处理**: 在实际应用中，可能需要对输入序列进行预处理，如处理缺失值（NaNs）。如果任一输入序列在某个时间点 $t$ 存在缺失值，则该点的输出 $O_t$ 通常也应为缺失值或遵循预定的处理规则。

【因子信息结束】===============================================================