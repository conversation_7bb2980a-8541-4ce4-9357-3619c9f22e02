【因子信息开始】===============================================================

【因子编号和名称】

因子编号: F001 (此为示例编号，请自行规划)
因子中文名称: 标准差 (Standard Deviation, STDDEV)

【1. 因子名称详情】

因子F001: 标准差 (Standard Deviation, STDDEV)
该指标用于衡量数据在其平均值附近的离散程度。它是方差的平方根。

【2. 核心公式】

标准差的计算首先依赖于方差（Variance, VAR）的计算。对于时间序列数据 $X$，在时间点 $t$、周期为 $P$ 的标准差 $\text{STDDEV}_t$ 计算如下：

1.  计算简单移动平均 (SMA):
    $$ \text{SMA}(X, P)_t = \frac{1}{P} \sum_{i=0}^{P-1} X_{t-i} $$

2.  计算方差 (VAR):
    一种常用的计算方法是（也是TA-Lib内部`INT_VAR`采用的思路）：
    $$ \text{VAR}(X, P)_t = \left( \frac{1}{P} \sum_{i=0}^{P-1} X_{t-i}^2 \right) - \left( \text{SMA}(X, P)_t \right)^2 $$
    这个公式等价于样本方差的定义（除以N而非N-1，因为通常配合SMA使用）：
    $$ \text{VAR}(X, P)_t = \frac{1}{P} \sum_{i=0}^{P-1} (X_{t-i} - \text{SMA}(X, P)_t)^2 $$

3.  计算标准差 (STDDEV):
    $$ \text{STDDEV}(X, P)_t = \sqrt{\text{VAR}(X, P)_t} $$

4.  应用标准差倍数 (可选):
    $$ \text{OutputSTDDEV}_t = \text{STDDEV}(X, P)_t \times \text{NbDev} $$

【3. 变量定义】

*   $X_t$: 在时间点 $t$ 的输入数据值（例如：收盘价）。
*   $P$: 计算周期（时间窗口期 `optInTimePeriod`），表示用于计算标准差的数据点数量。
*   $\text{SMA}(X, P)_t$: 输入数据 $X$ 在时间点 $t$ 的 $P$ 周期简单移动平均值。
*   $X_{t-i}^2$: 在时间点 $t-i$ 的输入数据值的平方。
*   $\text{VAR}(X, P)_t$: 输入数据 $X$ 在时间点 $t$ 的 $P$ 周期方差。
*   $\text{STDDEV}(X, P)_t$: 输入数据 $X$ 在时间点 $t$ 的 $P$ 周期标准差（未经倍数调整）。
*   $\text{NbDev}$: 标准差倍数 (`optInNbDev`)，一个用于调整最终输出标准差的乘数。默认为1。
*   $\text{OutputSTDDEV}_t$: 最终输出的 $t$ 时刻的标准差值。

【4. 函数与方法说明】

*   **简单移动平均 (Simple Moving Average, SMA):**
    计算方法：将指定周期 $P$ 内的所有数据点 $X_{t-i}$ 相加，然后除以周期 $P$。
    公式：
    $$ \text{SMA}(X, P)_t = \frac{X_t + X_{t-1} + \dots + X_{t-P+1}}{P} $$
    作用：平滑数据，反映数据的中心趋势。

*   **方差 (Variance, VAR):**
    计算方法：衡量一组数据围绕其平均值的离散程度。
    在本因子的计算中，采用的是 “数据平方的均值” 减去 “数据均值的平方” 的方式。
    公式：
    $$ \text{VAR}(X, P)_t = \left( \frac{1}{P} \sum_{i=0}^{P-1} X_{t-i}^2 \right) - \left( \text{SMA}(X, P)_t \right)^2 $$
    这等效于各个数据点与均值之差的平方的平均值（注意这里的平均是除以 $P$）。

*   **平方根 (Square Root, sqrt):**
    计算方法：标准的数学运算，找出一个非负实数 $a$，使得其平方等于给定的数 $x$ (即 $a^2 = x$)。
    公式： $a = \sqrt{x}$
    作用：将方差转换回与原始数据相同的度量单位。

【5. 计算步骤】

假设我们有一系列输入数据 $X = \{x_1, x_2, \dots, x_N\}$。
参数为时间窗口期 $P$ 和标准差倍数 $\text{NbDev}$。

对于从第 $P$ 个数据点开始的每个时间点 $t$（即 $t \ge P$）：

1.  **数据准备:**
    获取当前时间点 $t$ 及其之前的 $P-1$ 个数据点，形成一个包含 $P$ 个数据点的窗口：$\{X_{t-P+1}, \dots, X_{t-1}, X_t\}$。

2.  **计算窗口内数据的简单移动平均 (SMA):**
    $$ \text{current_SMA} = \frac{1}{P} \sum_{i=0}^{P-1} X_{t-i} $$

3.  **计算窗口内数据平方的简单移动平均:**
    对窗口内的每个数据点 $X_{t-i}$ 求平方，然后计算这些平方值的简单移动平均。
    $$ \text{current_SMA_sq} = \frac{1}{P} \sum_{i=0}^{P-1} X_{t-i}^2 $$

4.  **计算方差 (VAR):**
    $$ \text{current_VAR} = \text{current_SMA_sq} - (\text{current_SMA})^2 $$
    *注意：在实际计算中，由于浮点数精度问题，方差结果可能出现极小的负值。如果 `current_VAR` 小于0，则将其视为0。*

5.  **计算标准差 (STDDEV):**
    取上方计算得到的方差的非负平方根。
    $$ \text{current_raw_STDDEV} = \sqrt{\text{current_VAR}} $$
    （如果 `current_VAR` 为0或负（已处理为0），则 `current_raw_STDDEV` 为0）。

6.  **应用标准差倍数 (如有指定):**
    将上一步得到的原始标准差乘以参数 $\text{NbDev}$。
    $$ \text{OutputSTDDEV}_t = \text{current_raw_STDDEV} \times \text{NbDev} $$

7.  将 $\text{OutputSTDDEV}_t$ 作为时间点 $t$ 的因子值。重复此过程直到处理完所有数据点。

【6. 备注与参数说明】

*   **时间窗口期 (optInTimePeriod, $P$):**
    *   此参数定义了计算标准差所考虑的历史数据的长度。默认值通常是5（如TA-Lib中所示，如果用户未指定）。
    *   较短的窗口期会使标准差对近期价格波动更为敏感。
    *   较长的窗口期会产生更平滑的标准差，反映长期的波动性。
    *   选择合适的窗口期取决于分析目标和交易策略。

*   **标准差倍数 (optInNbDev, $\text{NbDev}$):**
    *   此参数用于缩放计算出的标准差值。默认值为1.0。
    *   当 $\text{NbDev}=1.0$ 时，输出即为标准差本身。
    *   此参数在构建布林带（Bollinger Bands）等指标时非常有用，其中上轨和下轨通常是移动平均线加上/减去 $N$ 倍（$N$ 即 $\text{NbDev}$）的标准差。

*   **数据预处理和起始点:**
    *   计算标准差至少需要 $P$ 个数据点。因此，输出序列的前 $P-1$ 个点将没有有效的标准差值。
    *   输入数据应为数值型序列。异常值（Outliers）会对标准差的计算产生较大影响，因为计算中涉及平方项，会放大异常值的作用。

*   **浮点数精度:**
    *   在方差计算步骤 $ \text{VAR} = (\sum X^2 / P) - (\text{SMA})^2 $ 中，如果两个项非常接近，可能会因为浮点精度问题导致结果为微小的负数。实际应用中，当方差计算结果为负时，应将其视为0，以确保平方根操作的有效性。TA-Lib源码中通过 `TA_IS_ZERO_OR_NEG` 进行了此类检查。

【因子信息结束】===============================================================