【因子信息开始】===============================================================

【因子编号和名称】

因子编号: F0001
因子中文名称: 贝塔系数 (Beta Coefficient, BETA)

【1. 因子名称详情】

因子F0001: 贝塔系数 (Beta Coefficient, BETA)
该因子衡量一项资产相对于整体市场（或特定基准）的波动性或系统性风险。

【2. 核心公式】

给定长度为 `N` 的时间窗口，在时刻 `t`，资产 `a` 相对于市场（或基准）`m` 的贝塔系数计算如下：

1.  首先，计算资产和市场在窗口期内每个子周期的收益率：
    对于子周期 `i` (其中 `i` 从 `t-N+1` 到 `t`)：
    资产收益率: $R_{a,i} = \frac{P_{a,i} - P_{a,i-1}}{P_{a,i-1}}$
    市场收益率: $R_{m,i} = \frac{P_{m,i} - P_{m,i-1}}{P_{m,i-1}}$
    (若 $P_{a,i-1}$ 或 $P_{m,i-1}$ 为零，则对应收益率 $R_{a,i}$ 或 $R_{m,i}$ 记为 0)

2.  然后，使用最小二乘法估计线性回归模型 $R_{a} = \alpha + \beta \cdot R_{m} + \epsilon$ 中的斜率 $\beta$。其计算公式为：

    $ \text{BETA}_t = \frac{N \sum_{i=t-N+1}^{t} (R_{a,i} \cdot R_{m,i}) - (\sum_{i=t-N+1}^{t} R_{a,i}) (\sum_{i=t-N+1}^{t} R_{m,i})}{N \sum_{i=t-N+1}^{t} (R_{m,i}^2) - (\sum_{i=t-N+1}^{t} R_{m,i})^2} $

    如果分母为零，则 $\text{BETA}_t$ 记为 0。

【3. 变量定义】

*   $\text{BETA}_t$: 在时刻 `t` 计算得到的贝塔系数值。
*   $N$: 计算贝塔系数所使用的时间窗口长度（即期数，对应源码中的 `optInTimePeriod`）。
*   $P_{a,i}$: 资产 `a` 在子周期 `i` 期末的价格。
*   $P_{a,i-1}$: 资产 `a` 在子周期 `i` 期初（即子周期 `i-1` 期末）的价格。
*   $P_{m,i}$: 市场（或基准）`m` 在子周期 `i` 期末的价格。
*   $P_{m,i-1}$: 市场（或基准）`m` 在子周期 `i` 期初（即子周期 `i-1` 期末）的价格。
*   $R_{a,i}$: 资产 `a` 在子周期 `i` 的收益率。
*   $R_{m,i}$: 市场（或基准）`m` 在子周期 `i` 的收益率。
*   $\sum_{i=t-N+1}^{t}$: 对从子周期 `t-N+1` 到子周期 `t` 的 `N` 个值进行求和。

【4. 函数与方法说明】

*   **收益率计算**:
    对每个子周期 `i`，资产（或市场）的收益率通过以下公式计算：
    $ \text{收益率}_i = \frac{\text{当期价格}_i - \text{上期价格}_{i-1}}{\text{上期价格}_{i-1}} $
    如果上期价格 $P_{i-1}$ 为零，则该周期的收益率视为0，以避免除以零的错误。

*   **简单线性回归斜率**:
    贝塔系数是资产收益率 $R_a$ 对市场收益率 $R_m$ 进行简单线性回归时得到的斜率。该斜率衡量了当市场收益率变动一个单位时，资产收益率平均变动的幅度。上述核心公式是计算该斜率的标准最小二乘法（OLS）估计量。

【5. 计算步骤】

1.  **数据准备**:
    获取两组时间序列数据：资产的价格序列（例如，某支股票的每日收盘价）和市场/基准的价格序列（例如，对应市场指数的每日收盘价）。
    确定计算周期 `N`（例如，20天）。

2.  **计算收益率序列**:
    对于资产价格序列和市场价格序列，分别计算每个周期的收益率。
    例如，对于 $P_{a,0}, P_{a,1}, \ldots, P_{a,T}$ 和 $P_{m,0}, P_{m,1}, \ldots, P_{m,T}$，计算得到：
    资产收益率序列: $R_{a,1}, R_{a,2}, \ldots, R_{a,T}$
    市场收益率序列: $R_{m,1}, R_{m,2}, \ldots, R_{m,T}$
    其中 $R_{k,i} = (P_{k,i} - P_{k,i-1}) / P_{k,i-1}$ (如果 $P_{k,i-1} \neq 0$，否则为0)，`k` 代表 `a` 或 `m`。
    计算第一个贝塔值至少需要 `N` 对收益率数据，即至少需要 `N+1` 个价格数据点。

3.  **初始化滑动窗口**:
    为了计算第一个贝塔值 (假设在原始价格序列的第 `N` 个收益率期，即第 `N+1` 个价格点之后)，需要前 `N` 个收益率数据对 $(R_{a,1}, R_{m,1}), \ldots, (R_{a,N}, R_{m,N})$。
    计算以下初始累积和：
    *   $S_x = \sum_{j=1}^{N} R_{m,j}$
    *   $S_y = \sum_{j=1}^{N} R_{a,j}$
    *   $S_{xx} = \sum_{j=1}^{N} (R_{m,j}^2)$
    *   $S_{xy} = \sum_{j=1}^{N} (R_{a,j} \cdot R_{m,j})$

4.  **计算第一个贝塔值**:
    使用步骤3中得到的累积和，计算第一个贝塔值 $\text{BETA}_N$:
    $\text{分母} = N \cdot S_{xx} - S_x^2$
    如果 $\text{分母} \neq 0$:
    $\text{BETA}_N = \frac{N \cdot S_{xy} - S_x \cdot S_y}{\text{分母}}$
    否则 $\text{BETA}_N = 0$。

5.  **滑动窗口并更新计算后续贝塔值**:
    对于后续的每个时刻 `t` (从 `N+1` 到总收益率期数 `T`):
    a.  **移除最旧的数据点**: 设最旧的收益率数据对为 $(R_{a, t-N}, R_{m, t-N})$。
        更新累积和：
        $S_x = S_x - R_{m, t-N}$
        $S_y = S_y - R_{a, t-N}$
        $S_{xx} = S_{xx} - (R_{m, t-N}^2)$
        $S_{xy} = S_{xy} - (R_{a, t-N} \cdot R_{m, t-N})$
    b.  **加入最新的数据点**: 设最新的收益率数据对为 $(R_{a,t}, R_{m,t})$。
        更新累积和：
        $S_x = S_x + R_{m,t}$
        $S_y = S_y + R_{a,t}$
        $S_{xx} = S_{xx} + (R_{m,t}^2)$
        $S_{xy} = S_{xy} + (R_{a,t} \cdot R_{m,t})$
    c.  **计算当前时刻的贝塔值 $\text{BETA}_t$**:
        $\text{分母}_t = N \cdot S_{xx} - S_x^2$
        如果 $\text{分母}_t \neq 0$:
        $\text{BETA}_t = \frac{N \cdot S_{xy} - S_x \cdot S_y}{\text{分母}_t}$
        否则 $\text{BETA}_t = 0$。
    d.  输出 $\text{BETA}_t$。

6.  重复步骤5，直到处理完所有数据点。第一个输出的贝塔值对应于第 `N` 个收益率期（即原始价格序列的第 `N` 个数据点，如果从0开始计数，那么是 `inReal[N]`，此点实际上是第 `N+1` 个价格）。

【6. 备注与参数说明】

*   **参数 `N` (optInTimePeriod)**: 时间窗口长度是计算贝塔系数的关键参数。常见的选择可以是一年（约250个交易日）、一个季度（约60个交易日）或一个月（约20个交易日）等，具体取决于分析的频率和目的。较短的窗口期对近期市场变化更敏感，而较长的窗口期则提供更平滑、更稳定的贝塔估计。
*   **数据频率**: 资产和市场/基准的价格数据应具有相同的时间频率（如每日、每周、每月）。
*   **基准选择**: 基准的选择非常重要，应选择能代表相关市场整体表现的指数（如沪深300指数、标普500指数等）。
*   **收益率类型**: 虽然本说明中采用的是简单收益率，但在某些情况下，也可以使用对数收益率 $R_i = \ln(P_i / P_{i-1})$。对数收益率具有时间可加性等良好特性，但对于单期贝塔计算，两者差异通常不大。
*   **解释**:
    *   $\text{BETA} > 1$: 表示资产的波动性大于市场整体，风险相对较高。
    *   $\text{BETA} = 1$: 表示资产的波动性与市场一致。
    *   $0 < \text{BETA} < 1$: 表示资产的波动性小于市场整体，风险相对较低。
    *   $\text{BETA} = 0$: 表示资产的收益与市场收益不相关。
    *   $\text{BETA} < 0$: 表示资产的收益与市场收益呈负相关，具有一定的对冲作用。
*   **零除处理**: 当用于计算贝塔的分母（即 $N \sum (R_{m,i}^2) - (\sum R_{m,i})^2$）为零时，意味着在所选窗口期内市场收益率没有波动（所有 $R_{m,i}$ 都相同，或者 $N=1$ 且只有一个 $R_m$）。在这种情况下，贝塔系数未定义或通常设为0，如源码中所示。

【因子信息结束】===============================================================