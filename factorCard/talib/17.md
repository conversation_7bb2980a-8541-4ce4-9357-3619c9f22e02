【因子信息开始】===============================================================

【因子编号和名称】

因子编号: AVGDEV001: 平均离差 (Average Deviation, AVGDEV)

【1. 因子名称详情】

因子AVGDEV001: 平均离差 (Average Deviation, AVGDEV)，该指标用于衡量一组数据点与其算术平均值之间的平均绝对离散程度。

【2. 核心公式】

给定一个时间序列数据 \(P\)，在时间点 \(t\) 的平均离差 \(AVGDEV_t\) 计算公式如下：

1.  首先，计算周期 \(N\) 内数据的算术平均值 \(\bar{P}_t\):
    \[
    \bar{P}_t = \frac{1}{N} \sum_{i=0}^{N-1} P_{t-i}
    \]

2.  然后，计算平均离差 \(AVGDEV_t\):
    \[
    AVGDEV_t = \frac{1}{N} \sum_{i=0}^{N-1} |P_{t-i} - \bar{P}_t|
    \]

【3. 变量定义】
*   \(AVGDEV_t\): 在时间点 \(t\) 计算得到的平均离差值。
*   \(P_t\): 在时间点 \(t\) 的输入数据值（例如：收盘价）。
*   \(P_{t-i}\): 从时间点 \(t\) 向前回溯 \(i\) 个周期的数据值。例如，\(P_{t-0}\) 是当前周期的值，\(P_{t-1}\) 是前一个周期的值。
*   \(N\): 计算平均离差的时间窗口长度（周期数）。
*   \(\bar{P}_t\): 在当前时间点 \(t\) 及其之前的 \(N-1\) 个周期（共 \(N\) 个数据点）的算术平均值。
*   \(| \cdot |\): 表示绝对值函数。
*   \(\sum\): 表示求和符号。

【4. 函数与方法说明】
*   **算术平均值 (Arithmetic Mean)**:
    计算方法：将指定周期 \(N\) 内的所有数据点的值（\(P_{t}, P_{t-1}, \ldots, P_{t-N+1}\)）相加，然后除以周期长度 \(N\)。
    公式: \(\bar{X} = \frac{X_1 + X_2 + \ldots + X_N}{N} = \frac{1}{N} \sum_{j=1}^{N} X_j\)

*   **绝对值 (Absolute Value)**:
    计算方法：如果一个数 \(x\) 大于或等于零，其绝对值为 \(x\) 本身；如果 \(x\) 小于零，其绝对值为 \(-x\)。
    公式:
    \[
    |x| =
    \begin{cases}
    x, & \text{if } x \geq 0 \\
    -x, & \text{if } x < 0
    \end{cases}
    \]

【5. 计算步骤】
1.  **数据准备**: 获取基础时间序列数据，例如股票的每日收盘价序列 \(P = \{p_1, p_2, \ldots, p_M\}\)，其中 \(M\) 是数据点的总数。
2.  **参数设定**: 确定计算周期 \(N\)。例如，如果 \(N=14\)，则使用过去14个周期的数据进行计算。
3.  **遍历计算**: 对于时间序列中的每一个时间点 \(t\)（从第 \(N\) 个数据点开始，因为需要至少 \(N\) 个数据来计算第一个有效值）：
    a.  **选取数据窗口**: 选定从时间点 \(t-N+1\) 到 \(t\) 的 \(N\) 个连续数据点：\(P_{t-N+1}, P_{t-N+2}, \ldots, P_t\)。
    b.  **计算窗口内均值 (\(\bar{P}_t\))**:
        将步骤 a 中选取的 \(N\) 个数据点的值相加，然后除以 \(N\)。
        \[
        \bar{P}_t = \frac{P_{t-N+1} + P_{t-N+2} + \ldots + P_t}{N}
        \]
    c.  **计算各项离差的绝对值**:
        对于窗口内的每一个数据点 \(P_k\) (其中 \(k\) 从 \(t-N+1\) 到 \(t\))，计算该数据点与均值 \(\bar{P}_t\) 之间的差的绝对值：\(|P_k - \bar{P}_t|\)。
    d.  **求和离差绝对值**:
        将步骤 c 中计算得到的 \(N\) 个绝对离差值相加。
        \[
        \text{SumOfAbsoluteDeviations}_t = \sum_{i=0}^{N-1} |P_{t-i} - \bar{P}_t|
        \]
    e.  **计算平均离差 (\(AVGDEV_t\))**:
        将步骤 d 中得到的离差绝对值之和除以周期 \(N\)。
        \[
        AVGDEV_t = \frac{\text{SumOfAbsoluteDeviations}_t}{N}
        \]
4.  **输出**: 得到时间点 \(t\) 的平均离差值 \(AVGDEV_t\)。对时间序列中的每个后续点重复步骤 3a 至 3e。

【6. 备注与参数说明】
*   **参数 `optInTimePeriod` (时间窗口长度 \(N\))**:
    *   这是计算平均离差时所考虑的历史数据点数量。
    *   TA-Lib 中通常默认值为 14。
    *   该参数的取值范围通常从 2 开始，没有严格的上限，但过大的值可能会使指标过于平滑，失去敏感性。
    *   选择较短的周期会使指标对近期数据的波动更敏感，而选择较长的周期则会产生更平滑的指标，反映长期的平均离散状况。
*   **输入数据 (`inReal`, 即 \(P\))**:
    *   通常使用金融资产的收盘价序列。
    *   也可以应用于其他类型的时间序列数据，如开盘价、最高价、最低价、成交量等。
*   **数据起始点**:
    *   由于计算需要 \(N\) 个周期的数据，因此平均离差序列的第一个有效值将从原始数据序列的第 \(N\) 个数据点开始。也就是说，输出序列会比输入序列短 \(N-1\) 个点。
*   **指标解读**:
    *   平均离差衡量的是数据点与其平均值之间的典型偏差大小。
    *   值越大，表示数据点相对于其均值越分散，波动性可能越大。
    *   值越小，表示数据点更紧密地聚集在均值周围，波动性可能越小。
*   **与其他指标的关系**:
    *   平均离差与标准差 (Standard Deviation) 都是衡量数据离散程度的指标。标准差使用的是平方差的均值的平方根，而平均离差使用的是绝对差的均值。平均离差对极端值的敏感性通常低于标准差。

【因子信息结束】===============================================================