【因子信息开始】===============================================================

【因子编号和名称】

因子编号: DX001
因子中文名称: 方向运动指数 (Directional Movement Index, DX)

【1. 因子名称详情】

方向运动指数（Directional Movement Index, DX）是由 J. <PERSON> Jr. 开发的技术分析指标，用于评估价格运动的方向强度。DX本身是构建平均动向指数（ADX）的基础。DX衡量的是上升动向（+DI）和下降动向（-DI）之间的差异程度，反映了趋势的强度，但不指示趋势的方向。

【2. 核心公式】

DX的计算依赖于预先计算的上升方向指标（$+DI_N$）和下降方向指标（$-DI_N$）。核心公式如下：

1.  **方向运动指数 (DX):**
    $DX_N(t) = 100 \times \frac{|+DI_N(t) - (-DI_N(t))|}{|+DI_N(t) + (-DI_N(t))|}$
    如果 $(+DI_N(t) + (-DI_N(t))) = 0$，则：
    对于第一个计算出的DX值， $DX_N(t) = 0$。
    对于后续的DX值， $DX_N(t) = DX_N(t-1)$ (即取前一个周期的DX值)。

2.  **上升方向指标 ($+DI_N$):**
    $+DI_N(t) = 100 \times \frac{+DM_N(t)}{TR_N(t)}$
    如果 $TR_N(t) = 0$，则 $+DI_N(t) = 0$。

3.  **下降方向指标 ($-DI_N$):**
    $-DI_N(t) = 100 \times \frac{-DM_N(t)}{TR_N(t)}$
    如果 $TR_N(t) = 0$，则 $-DI_N(t) = 0$。

4.  **平滑后的上升方向动量 ($+DM_N$):**
    $+DM_N(t) = \text{WilderSmooth}(+DM1, N)(t)$

5.  **平滑后的下降方向动量 ($-DM_N$):**
    $-DM_N(t) = \text{WilderSmooth}(-DM1, N)(t)$

6.  **平滑后的真实波动幅度 ($TR_N$):**
    $TR_N(t) = \text{WilderSmooth}(TR1, N)(t)$

7.  **单周期上升方向动量 ($+DM1_t$):**
    $UpMove_t = H_t - H_{t-1}$
    $DownMove_t = L_{t-1} - L_t$
    $+DM1_t = \begin{cases} UpMove_t & \text{if } UpMove_t > DownMove_t \text{ and } UpMove_t > 0 \\ 0 & \text{otherwise} \end{cases}$

8.  **单周期下降方向动量 ($-DM1_t$):**
    $UpMove_t = H_t - H_{t-1}$
    $DownMove_t = L_{t-1} - L_t$
    $-DM1_t = \begin{cases} DownMove_t & \text{if } DownMove_t > UpMove_t \text{ and } DownMove_t > 0 \\ 0 & \text{otherwise} \end{cases}$

9.  **单周期真实波动幅度 ($TR1_t$):**
    $TR1_t = \max(H_t - L_t, |H_t - C_{t-1}|, |L_t - C_{t-1}|)$

【3. 变量定义】

*   $t$: 当前时间周期。
*   $t-1$: 前一个时间周期。
*   $H_t$: 当前周期的最高价。
*   $L_t$: 当前周期的最低价。
*   $C_t$: 当前周期的收盘价。
*   $H_{t-1}$: 前一个周期的最高价。
*   $L_{t-1}$: 前一个周期的最低价。
*   $C_{t-1}$: 前一个周期的收盘价。
*   $N$: 计算周期参数，通常为14。
*   $UpMove_t$: 当期向上运动幅度。
*   $DownMove_t$: 当期向下运动幅度。
*   $+DM1_t$: 单周期上升方向动量，表示当前周期价格上涨超出前一周期范围的部分。
*   $-DM1_t$: 单周期下降方向动量，表示当前周期价格下跌超出前一周期范围的部分。
*   $TR1_t$: 单周期真实波动幅度，衡量价格的实际波动范围。
*   $+DM_N(t)$: 经过N周期Wilder平滑的上升方向动量。
*   $-DM_N(t)$: 经过N周期Wilder平滑的下降方向动量。
*   $TR_N(t)$: 经过N周期Wilder平滑的真实波动幅度。
*   $+DI_N(t)$: N周期上升方向指标。
*   $-DI_N(t)$: N周期下降方向指标。
*   $DX_N(t)$: N周期方向运动指数。
*   $|x|$: $x$的绝对值。
*   $\max(a, b, c)$: $a, b, c$ 中的最大值。

【4. 函数与方法说明】

*   **Wilder 平滑 (WilderSmooth(Data, N))**:
    J. Welles Wilder Jr. 使用的一种特定类型的指数移动平均（EMA），其平滑因子 $\alpha = 1/N$。
    计算方法如下：
    1.  **初始值**:
        对于序列中的第一个平滑值 $S_0(\text{Data})$，其计算基于前 $N-1$ 个原始数据点（例如 $+DM1, -DM1, TR1$）的总和。
        例如，对于 $+DM_N$，第一个平滑值（对应于数据序列中的第 $N$ 个时间点，因为第一个时间点没有 $+DM1$）是：
        $+DM_N(\text{initial}) = \sum_{i=1}^{N-1} +DM1_{t_0+i}$ (其中 $t_0$ 是序列中可以计算 $+DM1$ 的第一个点的前一个点)
        同样的方法适用于 $-DM_N(\text{initial})$ 和 $TR_N(\text{initial})$。
        *注意：TA-Lib 的实现中，为 $+DM_N, -DM_N, TR_N$ 维护一个累积值。第一个 $N-1$ 个周期的 $+DM1, -DM1, TR1$ 值被累加起来形成初始的平滑值基数。例如 `prevPlusDM` 会累加 `optInTimePeriod-1` 个 `+DM1` 值。*

    2.  **后续值**:
        对于后续的平滑值 $S_t(\text{Data})$ （对应于 $t > \text{initial}$）：
        $S_t(\text{Data}) = S_{t-1}(\text{Data}) - \frac{S_{t-1}(\text{Data})}{N} + \text{Data}_t$
        其中 $\text{Data}_t$ 是当前周期的原始数据点（例如 $+DM1_t, -DM1_t, TR1_t$）。
        *TA-Lib在输出第一个有效DX值之前，会额外进行一定数量（`TA_GLOBALS_UNSTABLE_PERIOD` 定义的周期数）的这种平滑迭代，以稳定该移动平均值。*

【5. 计算步骤】

1.  **数据准备**:
    获取时间序列数据，包括每个周期的最高价 ($H_t$)、最低价 ($L_t$) 和收盘价 ($C_t$)。
    确定参数 $N$ (例如，14)。

2.  **计算单周期方向动量 ($+DM1_t, -DM1_t$) 和真实波动幅度 ($TR1_t$):**
    对于时间序列中的每个周期 $t$（从第二个周期开始，因为计算需要 $t-1$ 的数据）：
    a.  计算 $UpMove_t = H_t - H_{t-1}$。
    b.  计算 $DownMove_t = L_{t-1} - L_t$。
    c.  计算 $+DM1_t$:
        如果 $UpMove_t > DownMove_t$ 且 $UpMove_t > 0$，则 $+DM1_t = UpMove_t$。
        否则，$+DM1_t = 0$。
    d.  计算 $-DM1_t$:
        如果 $DownMove_t > UpMove_t$ 且 $DownMove_t > 0$，则 $-DM1_t = DownMove_t$。
        否则，$-DM1_t = 0$。
    e.  计算 $TR1_t = \max(H_t - L_t, |H_t - C_{t-1}|, |L_t - C_{t-1}|)$。

3.  **计算平滑后的方向动量 ($+DM_N, -DM_N$) 和真实波动幅度 ($TR_N$):**
    a.  **初始化**: 计算前 $N-1$ 个周期的 $+DM1, -DM1, TR1$ 值的总和，作为平滑计算的初始累积值。令这些初始累积值为 $prevPlusDM_{sum}$, $prevMinusDM_{sum}$, $prevTR_{sum}$。这些值对应于数据序列中的第 $N$ 个时间点（因为第一个时间点无法计算 $DM1, TR1$）。
    b.  **稳定期平滑**: 对于接下来的一个“不稳定周期”（TA-Lib中定义为 `TA_GLOBALS_UNSTABLE_PERIOD(TA_FUNC_UNST_DX,Dx)` 的长度，通常等于参数 $N$），继续使用Wilder平滑公式迭代计算：
        对于每个新周期 $t_{new}$（在初始求和之后）：
        $prevPlusDM_t = prevPlusDM_{t-1} - \frac{prevPlusDM_{t-1}}{N} + +DM1_{t_{new}}$
        $prevMinusDM_t = prevMinusDM_{t-1} - \frac{prevMinusDM_{t-1}}{N} + -DM1_{t_{new}}$
        $prevTR_t = prevTR_{t-1} - \frac{prevTR_{t-1}}{N} + TR1_{t_{new}}$
        （其中，平滑迭代的第一个 $prevPlusDM_{t-1}$ 即为 $prevPlusDM_{sum}$，以此类推。）
    c.  完成稳定期平滑后，得到的 $prevPlusDM, prevMinusDM, prevTR$ 将作为计算第一个 $DX$ 值的 $+DM_N, -DM_N, TR_N$。

4.  **计算方向指标 ($+DI_N, -DI_N$):**
    在稳定期结束后，对于第一个有效的输出点：
    a.  如果 $prevTR \neq 0$:
        $+DI_N = 100 \times \frac{prevPlusDM}{prevTR}$
        $-DI_N = 100 \times \frac{prevMinusDM}{prevTR}$
    b.  否则 (如果 $prevTR = 0$):
        $+DI_N = 0$
        $-DI_N = 0$

5.  **计算方向运动指数 ($DX_N$):**
    a.  计算 $sumDI = +DI_N + (-DI_N)$。
    b.  如果 $sumDI \neq 0$:
        $DX_N = 100 \times \frac{|+DI_N - (-DI_N)|}{sumDI}$
    c.  否则 (如果 $sumDI = 0$):
        对于计算出的第一个 $DX$ 值，$DX_N = 0.0$。

6.  **计算后续周期的 $DX_N$ 值:**
    对于每一个后续的时间周期 $t_{next}$:
    a.  获取当期的 $+DM1_{t_{next}}$, $-DM1_{t_{next}}$, $TR1_{t_{next}}$。
    b.  更新平滑值：
        $prevPlusDM = prevPlusDM - \frac{prevPlusDM}{N} + +DM1_{t_{next}}$
        $prevMinusDM = prevMinusDM - \frac{prevMinusDM}{N} + -DM1_{t_{next}}$
        $prevTR = prevTR - \frac{prevTR}{N} + TR1_{t_{next}}$
    c.  使用更新后的 $prevPlusDM, prevMinusDM, prevTR$ 重新计算当前的 $+DI_N$ 和 $-DI_N$ (如步骤4)。
    d.  使用新的 $+DI_N$ 和 $-DI_N$ 重新计算当前的 $DX_N$ (如步骤5，但如果 $sumDI=0$，则 $DX_N$ 值等于前一个周期的 $DX_N$ 值)。

【6. 备注与参数说明】

*   **参数选择**:
    *   `optInTimePeriod` (即公式中的 $N$): 时间周期，通常默认为14天。可以根据分析的资产和时间框架进行调整。较短的周期对价格变化更敏感，较长的周期则更平滑。
*   **数据预处理**:
    *   需要足够长的历史数据来完成初始化和平滑的稳定期。TA-Lib中，DX指标的Lookback（所需前期数据量）为 `optInTimePeriod + TA_GLOBALS_UNSTABLE_PERIOD(TA_FUNC_UNST_DX,Dx)`（当 `optInTimePeriod > 1` 时）。通常 `TA_GLOBALS_UNSTABLE_PERIOD` 等于 `optInTimePeriod` 或类似值，这意味着第一个DX输出值需要大约 $2N-1$ 个原始数据点。 (严格来说，是 `N-1` 个 $DM1/TR1$ 值用于求和，再加上 `unstable_period` 次迭代，第一个$DM1/TR1$需要2个原始价格点，总共是 `1 + (N-1) + unstable_period` 个价格点)
*   **Wilder的原始计算**: Wilder在其书中进行的计算有时基于整数或特定取整规则。TA-Lib的实现（如源码中`#undef round_pos; #define round_pos(x) (x)`所示）通常不进行此类取整，以保持计算精度。
*   **用途**: DX是计算ADX（平均动向指数）的中间步骤。ADX通常与$+DI_N$和$-DI_N$一同使用，以判断趋势的强度和方向。单独使用DX时，它主要反映趋势的强度，高DX值表示强趋势（无论上升或下降），低DX值表示弱趋势或盘整。

【因子信息结束】===============================================================