【因子信息开始】===============================================================

【因子编号和名称】

因子 MACD_HIST: 移动平均收敛散度柱 (Moving Average Convergence Divergence Histogram, MACD_HIST)

【1. 因子名称详情】

因子3: 移动平均收敛散度柱 (Moving Average Convergence Divergence Histogram, MACD_HIST)。也称为MACD柱状图或差值柱。

【2. 核心公式】

MACD_Hist_t = DIF_t - DEA_t
  – 其中 DIF_t 和 DEA_t 分别按前述方法计算得到。

【3. 变量定义】

• DIF_t: 差离值，由 MACD_DIF 因子计算得到。
• DEA_t: 信号线，由 MACD_DEA 因子计算得到。
• MACD_Hist_t: 两者之差，即 MACD 柱线值。

【4. 函数与方法说明】

MACD柱的计算相对简单，直接利用前两因子的结果进行运算，无需额外复杂函数：

MACD_Hist_t = DIF_t - DEA_t。

【5. 计算步骤】

步骤1: 确保 DIF 与 DEA 序列均已有效（DEA从 t = (N_slow - 1) + N_signal 开始有效）。

步骤2: 对于每个具有有效值的时刻 t，计算
  MACD_Hist_t = DIF_t - DEA_t。

【6. 备注与参数说明】

• MACD柱的变化通常用于识别势头变化，如从负转正或者正转负的信号。
• 参数设置与前述因子保持一致: N_fast = 12, N_slow = 26, N_signal = 9。



【7. 关联因子补充说明】

【关联因子信息开始】===============================================================

【因子编号和名称】

*   因子 MACD_DIF: 移动平均收敛散度差离值 (Moving Average Convergence Divergence Difference, DIF)
*   因子 MACD_DEA: 移动平均收敛散度信号线 (Moving Average Convergence Divergence Signal, DEA)
*   因子 MACD_HIST: 移动平均收敛散度柱 (Moving Average Convergence Divergence Histogram, MACD_HIST)

【1. 因子名称详情】

*   因子1: 移动平均收敛散度差离值 (Moving Average Convergence Divergence Difference, DIF)。通常也直接称为MACD线。
*   因子2: 移动平均收敛散度信号线 (Moving Average Convergence Divergence Signal, DEA)。通常也称为Signal线。
*   因子3: 移动平均收敛散度柱 (Moving Average Convergence Divergence Histogram, MACD_HIST)。也称为MACD柱状图或差值柱。

【2. 核心公式】

1.  **指数移动平均 (Exponential Moving Average, EMA)**
    对于给定的时间序列 \( P \) 和周期 \( N \)，其在时刻 \( t \) 的EMA计算如下：
    \( EMA_t(P, N) = P_t \times k + EMA_{t-1}(P, N) \times (1 - k) \)
    其中，平滑系数 \( k = \frac{2}{N+1} \)。
    \( EMA_0(P, N) \) (初始值) 通常取前 \( N \) 个数据点的简单移动平均 (SMA)：
    \( EMA_0(P, N) = SMA(P, N) = \frac{1}{N} \sum_{i=1}^{N} P_{initial\_set, i} \)
    (注: \(P_{initial\_set, i}\) 指用于初始化EMA的前N个价格数据点。在实际序列计算中，\(EMA_t(P,N)\)的第一个有效值出现在第N个数据点。)

2.  **移动平均收敛散度差离值 (DIF / MACD线)**
    \( DIF_t = EMA_t(P, N_{fast}) - EMA_t(P, N_{slow}) \)

3.  **移动平均收敛散度信号线 (DEA / MACDSignal线)**
    \( DEA_t = EMA_t(DIF, N_{signal}) \)

4.  **移动平均收敛散度柱 (MACD_HIST / MACD柱)**
    \( MACD\_Hist_t = DIF_t - DEA_t \)

【3. 变量定义】

*   \( P_t \): 时刻 \( t \) 的输入价格序列值（通常为收盘价）。
*   \( N_{fast} \): 计算DIF时使用的短周期EMA的周期长度。
*   \( N_{slow} \): 计算DIF时使用的长周期EMA的周期长度。
*   \( N_{signal} \): 计算DEA时对DIF序列进行EMA平滑的周期长度。
*   \( k \): EMA计算中的平滑系数。根据对应周期 \( N \) 计算，例如 \( k_{fast} = \frac{2}{N_{fast}+1} \), \( k_{slow} = \frac{2}{N_{slow}+1} \), \( k_{signal} = \frac{2}{N_{signal}+1} \)。
*   \( EMA_t(X, N) \): 数据序列 \( X \) 在时刻 \( t \) 的 \( N \) 周期指数移动平均值。
*   \( SMA(X, N) \): 数据序列 \( X \) 的 \( N \) 周期简单移动平均值。
*   \( DIF_t \): 时刻 \( t \) 的差离值（MACD线）。
*   \( DEA_t \): 时刻 \( t \) 的信号线（MACDSignal线）。
*   \( MACD\_Hist_t \): 时刻 \( t \) 的MACD柱线值。

【4. 函数与方法说明】

1.  **指数移动平均 (Exponential Moving Average, EMA)**
    *   **定义**: EMA是一种给予近期数据点更高权重的移动平均。
    *   **计算方法**:
        1.  **平滑系数 (k)**: \( k = \frac{2}{N+1} \)，其中 \( N \) 是EMA的周期。
        2.  **初始EMA值**: 第一个EMA值 (即序列中第 \( N \) 个数据点的EMA值) 通常使用前 \( N \) 个数据点的简单移动平均 (SMA) 作为近似值：
            \( SMA(P_{1..N}, N) = \frac{P_1 + P_2 + \dots + P_N}{N} \)
            因此，\( EMA_N(P, N) = SMA(P_{1..N}, N) \)。
            （注意：序列的前 \(N-1\) 个数据点没有定义EMA值，或者说EMA的计算需要至少 \(N\) 个数据点才能开始。）
        3.  **后续EMA值**: 对于 \( t > N \)，EMA的计算采用递归方式：
            \( EMA_t(P, N) = (P_t - EMA_{t-1}(P, N)) \times k + EMA_{t-1}(P, N) \)
            这等价于：
            \( EMA_t(P, N) = P_t \times k + EMA_{t-1}(P, N) \times (1 - k) \)

2.  **简单移动平均 (Simple Moving Average, SMA)**
    *   **定义**: SMA是一定周期内数据点的算术平均值。
    *   **计算方法**: 对于周期 \( N \) 和数据序列 \( X \)，在时刻 \( t \) （假设 \( t \ge N \)），SMA计算覆盖从 \( X_{t-N+1} \) 到 \( X_t \) 的数据点：
        \( SMA_t(X, N) = \frac{X_{t-N+1} + X_{t-N+2} + \dots + X_t}{N} \)

【5. 计算步骤】

**数据准备**:
*   获取输入价格序列 \( P = \{P_1, P_2, \dots, P_T\} \)，通常为收盘价。
*   设定参数：短周期 \( N_{fast} \)，长周期 \( N_{slow} \)，信号线周期 \( N_{signal} \)。
*   **参数调整**: 确保 \( N_{slow} > N_{fast} \)。如果初始设定 \( N_{slow} < N_{fast} \)，则交换 \( N_{slow} \) 和 \( N_{fast} \) 的值。

**步骤1: 计算短周期EMA (EMA_fast)**
1.  使用价格序列 \( P \) 和周期 \( N_{fast} \) 计算短周期EMA序列 \( EMA(P, N_{fast}) \)。
    *   平滑系数 \( k_{fast} = \frac{2}{N_{fast}+1} \)。
    *   计算 \( EMA(P, N_{fast}) \) 的第一个值（对应输入序列的第 \( N_{fast} \) 个点）：
        \( EMA_{N_{fast}}(P, N_{fast}) = SMA(P_{1..N_{fast}}, N_{fast}) = \frac{1}{N_{fast}} \sum_{i=1}^{N_{fast}} P_i \)
    *   对于输入序列中 \( t > N_{fast} \) 的后续点：
        \( EMA_t(P, N_{fast}) = P_t \times k_{fast} + EMA_{t-1}(P, N_{fast}) \times (1 - k_{fast}) \)
    称此序列为 \( \text{FastEMA}_t \)。其有效起始点为原始数据序列的第 \(N_{fast}\) 个点。

**步骤2: 计算长周期EMA (EMA_slow)**
1.  使用价格序列 \( P \) 和周期 \( N_{slow} \) 计算长周期EMA序列 \( EMA(P, N_{slow}) \)。
    *   平滑系数 \( k_{slow} = \frac{2}{N_{slow}+1} \)。
    *   计算 \( EMA(P, N_{slow}) \) 的第一个值（对应输入序列的第 \( N_{slow} \) 个点）：
        \( EMA_{N_{slow}}(P, N_{slow}) = SMA(P_{1..N_{slow}}, N_{slow}) = \frac{1}{N_{slow}} \sum_{i=1}^{N_{slow}} P_i \)
    *   对于输入序列中 \( t > N_{slow} \) 的后续点：
        \( EMA_t(P, N_{slow}) = P_t \times k_{slow} + EMA_{t-1}(P, N_{slow}) \times (1 - k_{slow}) \)
    称此序列为 \( \text{SlowEMA}_t \)。其有效起始点为原始数据序列的第 \(N_{slow}\) 个点。

**步骤3: 计算差离值 (DIF / MACD线)**
1.  DIF 值的计算需要 \( \text{FastEMA}_t \) 和 \( \text{SlowEMA}_t \) 均有有效值。由于 \( N_{slow} \ge N_{fast} \)，DIF 的计算从原始数据序列的第 \( N_{slow} \) 个点开始（此时 \( \text{FastEMA}_t \) 和 \( \text{SlowEMA}_t \) 均已开始产生有效值）。
2.  对于 \( t \ge N_{slow} \)：
    \( DIF_t = \text{FastEMA}_t - \text{SlowEMA}_t \)
    得到DIF序列 \( \{DIF_{N_{slow}}, DIF_{N_{slow}+1}, \dots, DIF_T\} \)。

**步骤4: 计算信号线 (DEA / MACDSignal线)**
1.  使用步骤3中得到的 DIF 序列和周期 \( N_{signal} \) 计算DEA序列 \( EMA(DIF, N_{signal}) \)。
    *   平滑系数 \( k_{signal} = \frac{2}{N_{signal}+1} \)。
    *   DEA序列的计算基于DIF序列。令DIF序列的第一个有效值为 \(DIF'_{1} = DIF_{N_{slow}}\), 第二个为 \(DIF'_{2} = DIF_{N_{slow}+1}\)，以此类推。
    *   计算 \( EMA(DIF', N_{signal}) \) 的第一个值（对应DIF序列的第 \( N_{signal} \) 个点）：
        \( EMA_{N_{signal}}(DIF', N_{signal}) = SMA(DIF'_{1..N_{signal}}, N_{signal}) = \frac{1}{N_{signal}} \sum_{i=1}^{N_{signal}} DIF'_i \)
    *   对于DIF序列中 \( j > N_{signal} \) 的后续点：
        \( EMA_j(DIF', N_{signal}) = DIF'_j \times k_{signal} + EMA_{j-1}(DIF', N_{signal}) \times (1 - k_{signal}) \)
    称此序列为 \( DEA_j \)。\( \text{DEA} \) 序列的第一个有效值对应原始价格序列的第 \( (N_{slow}-1) + N_{signal} \) 个点。

**步骤5: 计算MACD柱 (MACD_HIST / MACD柱)**
1.  MACD柱的计算需要 \( DIF_t \) 和 \( DEA_t \) 均有有效值。其计算从 \( DEA_t \) 的第一个有效值开始。
2.  对于所有 \( DEA_t \) 有效的时刻 \( t \)：
    \( MACD\_Hist_t = DIF_t - DEA_t \)
    (注意：这里的 \(DIF_t\) 和 \(DEA_t\) 的下标 \(t\) 应对应于原始价格序列的同一时间点。)

**输出**:
*   DIF 序列（MACD线）
*   DEA 序列（MACDSignal线）
*   MACD_Hist 序列（MACD柱）

有效输出的起始点：
*   \(DIF_t\) 的第一个有效值出现在原始数据的第 \(N_{slow}\) 期。
*   为了计算第一个 \(DEA_t\)，需要 \(N_{signal}\) 个 \(DIF_t\) 值。因此，第一个 \(DEA_t\) 和 \(MACD\_Hist_t\) 值出现在原始数据的第 \( (N_{slow}-1) + N_{signal} \) 期。之后每个周期都有对应的DIF, DEA, MACD_Hist值。

【6. 备注与参数说明】

*   **常用参数设置**: \( N_{fast} = 12 \), \( N_{slow} = 26 \), \( N_{signal} = 9 \)。这些是MACD指标最经典的参数组合。
*   **输入数据**: 通常使用金融资产的收盘价序列作为输入 \( P_t \)。也可以应用于其他价格类型（如开盘价、最高价、最低价、均价等）或任何时间序列数据。
*   **数据预处理**: 无特殊预处理要求，但输入数据序列应足够长，以保证计算出有意义的指标值。所需的最少数据点数为 \( (N_{slow}-1) + N_{signal} \)。
*   **EMA初始化**: EMA的初始化方法有多种，本卡片描述的是使用SMA进行初始化。不同的初始化方法可能导致指标早期值的微小差异，但随着时间的推移，这种差异会逐渐减小。
*   **用途**: MACD指标广泛用于识别趋势方向、强度、以及潜在的买卖点（例如通过金叉、死叉、背离等形态）。
    *   DIF线和DEA线的交叉（金叉/死叉）是常见的交易信号。
    *   MACD柱从负变正或从正变负，以及柱体的扩张和收缩，可以反映动能的变化。
    *   价格走势与MACD指标之间的背离可以预示趋势的潜在反转。

【关联因子信息结束】===============================================================

【因子信息结束】===============================================================