【因子信息开始】===============================================================

【因子编号和名称】

因子编号: OBV001: 能量潮 (On Balance Volume, OBV)

【1. 因子名称详情】

因子1: 能量潮 (On Balance Volume, OBV)

【2. 核心公式】

能量潮指标通过累计每日的成交量来构建。当今日价格（通常为收盘价）高于昨日价格时，今日成交量被加到前一日的OBV值上；当今日价格低于昨日价格时，今日成交量从前一日的OBV值中减去；若价格不变，则OBV值亦不变。

其数学表达式如下：

设 $C_t$ 为 $t$ 时刻的价格，$V_t$ 为 $t$ 时刻的成交量，$OBV_t$ 为 $t$ 时刻的能量潮值。

对于计算序列的第一个数据点 (令 $t=0$):
$OBV_0 = V_0$

对于后续的数据点 ($t > 0$):
$OBV_t = OBV_{t-1} + \Delta V_t$
其中，$\Delta V_t$ 的计算逻辑如下：
$\Delta V_t = \begin{cases} +V_t & \text{if } C_t > C_{t-1} \\ -V_t & \text{if } C_t < C_{t-1} \\ 0 & \text{if } C_t = C_{t-1} \end{cases}$

因此，完整公式可以写作：
$OBV_t = \begin{cases} V_0 & \text{if } t = 0 \\ OBV_{t-1} + V_t & \text{if } C_t > C_{t-1} \text{ and } t > 0 \\ OBV_{t-1} - V_t & \text{if } C_t < C_{t-1} \text{ and } t > 0 \\ OBV_{t-1} & \text{if } C_t = C_{t-1} \text{ and } t > 0 \end{cases}$

【3. 变量定义】

*   $t$: 时间周期（例如：天、小时等）。
*   $C_t$: 在时间周期 $t$ 的价格（通常使用收盘价）。
*   $C_{t-1}$: 在前一个时间周期 $t-1$ 的价格。
*   $V_t$: 在时间周期 $t$ 的成交量。
*   $OBV_t$: 在时间周期 $t$ 计算得到的能量潮指标值。
*   $OBV_{t-1}$: 在前一个时间周期 $t-1$ 计算得到的能量潮指标值。
*   $V_0$: 计算序列中第一个数据点的成交量。

【4. 函数与方法说明】

该因子主要使用基本的算术运算（加法、减法）和条件判断逻辑，不涉及复杂的特殊函数或统计方法。

【5. 计算步骤】

1.  **数据准备**:
    *   获取时间序列的价格数据 (例如，每日收盘价 $C_0, C_1, C_2, \ldots, C_N$)。
    *   获取时间序列的对应成交量数据 ($V_0, V_1, V_2, \ldots, V_N$)。

2.  **初始化**:
    *   对于计算范围内的第一个数据点 (时间点 $t=0$):
        *   记录其价格 $C_0$ 作为初始的“前一日价格” $P_{prev}$。
        *   记录其成交量 $V_0$。
        *   该数据点的能量潮值 $OBV_0$ 直接设置为 $V_0$。
        *   将 $OBV_0$ 存储为输出序列的第一个值。
        *   更新“前一日OBV值” $OBV_{prev} = OBV_0$。

3.  **迭代计算**:
    *   从计算范围内的第二个数据点 (时间点 $t=1$) 开始，依次处理到最后一个数据点 (时间点 $t=N$):
        a.  获取当前时间点 $t$ 的价格 $C_t$ 和成交量 $V_t$。
        b.  比较当前价格 $C_t$ 与记录的“前一日价格” $P_{prev}$：
            i.  如果 $C_t > P_{prev}$：当前OBV值 $OBV_{current} = OBV_{prev} + V_t$。
            ii. 如果 $C_t < P_{prev}$：当前OBV值 $OBV_{current} = OBV_{prev} - V_t$。
            iii.如果 $C_t = P_{prev}$：当前OBV值 $OBV_{current} = OBV_{prev}$ (即OBV值保持不变)。
        c.  将计算得到的 $OBV_{current}$ 存储为当前时间点 $t$ 的输出OBV值。
        d.  更新“前一日价格”： $P_{prev} = C_t$。
        e.  更新“前一日OBV值”： $OBV_{prev} = OBV_{current}$。

4.  **输出**:
    *   完成所有数据点的计算后，得到一个与输入价格和成交量序列长度相同的能量潮指标值序列 $OBV_0, OBV_1, \ldots, OBV_N$。

【6. 备注与参数说明】

*   **输入数据**: `inReal` 通常代表收盘价，但也可以使用其他价格，如典型价格（(最高价+最低价+收盘价)/3）等。`inVolume` 代表对应周期的成交量。
*   **无窗口期**: OBV是一个累积指标，其计算从所选数据范围的第一个点开始，因此没有传统意义上的“窗口期”参数。
*   **数值的绝对意义**: OBV指标的绝对数值大小通常不具有直接意义，因为它的起始值依赖于数据序列的第一个成交量值。分析的重点在于OBV线的趋势方向、形态（如背离、突破等）以及与价格趋势的配合情况。
*   **数据预处理**: 确保价格数据和成交量数据在时间上是同步和对应的。数据质量对指标的准确性至关重要。
*   **浮点数精度**: 原始C代码中存在对双精度（`double`）和单精度（`float`）输入数据的处理版本，但核心计算逻辑相同。输出统一为双精度。在实际应用中，通常建议使用双精度以保证计算准确性。

【因子信息结束】===============================================================