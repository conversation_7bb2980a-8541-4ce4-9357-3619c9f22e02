【因子信息开始】===============================================================

【因子编号和名称】

因子编号: F03001
因子中文名称: 终极振荡指标 (Ultimate Oscillator, ULTOSC)

【1. 因子名称详情】

因子 F03001: 终极振荡指标 (Ultimate Oscillator, ULTOSC)。该指标由拉里·威廉姆斯（<PERSON> Williams）于1976年提出，旨在通过整合短期、中期和长期三个不同时间周期的市场动量来生成交易信号，以克服其他振荡指标在单一周期内可能产生的虚假信号。

【2. 核心公式】

终极振荡指标 ($ULTOSC_t$) 的计算公式如下：

$ULTOSC_t = 100 \times \frac{ \left( 4 \times \frac{\sum_{i=0}^{P1-1} BP_{t-i}}{\sum_{i=0}^{P1-1} TR_{t-i}} \right) + \left( 2 \times \frac{\sum_{i=0}^{P2-1} BP_{t-i}}{\sum_{i=0}^{P2-1} TR_{t-i}} \right) + \left( 1 \times \frac{\sum_{i=0}^{P3-1} BP_{t-i}}{\sum_{i=0}^{P3-1} TR_{t-i}} \right) }{4+2+1}$

其中：
$BP_k = C_k - TL_k$
$TL_k = \min(L_k, C_{k-1})$
$TR_k = \max( (H_k - L_k), |H_k - C_{k-1}|, |L_k - C_{k-1}| )$

如果某个周期的 $\sum TR_{t-i}$ 为0，则该周期的比率项 $\frac{\sum BP_{t-i}}{\sum TR_{t-i}}$ 在分子加权和中贡献为0。

【3. 变量定义】

*   $ULTOSC_t$: $t$ 时刻的终极振荡指标值。
*   $C_k$: $k$ 时刻的收盘价。
*   $H_k$: $k$ 时刻的最高价。
*   $L_k$: $k$ 时刻的最低价。
*   $C_{k-1}$: $k-1$ 时刻的收盘价（即 $k$ 时刻的前一周期收盘价）。
*   $BP_k$: $k$ 时刻的购买压力 (Buying Pressure)。
*   $TL_k$: $k$ 时刻的真实低点 (True Low)。
*   $TR_k$: $k$ 时刻的真实波幅 (True Range)。
*   $P1$: 第一个时间周期（短周期），通常默认为7。
*   $P2$: 第二个时间周期（中周期），通常默认为14。
*   $P3$: 第三个时间周期（长周期），通常默认为28。
    （注意：$P1, P2, P3$ 指的是按时长排序后的短、中、长周期，权重4对应最短周期，权重2对应中间周期，权重1对应最长周期）。
*   $\sum_{i=0}^{N-1} X_{k-i}$: 对变量 $X$ 在最近 $N$ 个周期（从 $k-N+1$ 到 $k$ 时刻，共 $N$ 个值）进行求和。

【4. 函数与方法说明】

*   $\min(a, b)$: 返回 $a$ 和 $b$ 中的较小值。
    例如: $\min(10, 5) = 5$。
*   $\max(a, b, c)$: 返回 $a, b, c$ 中的最大值。
    例如: $\max(10, 5, 12) = 12$。
*   $|x|$: 返回 $x$ 的绝对值。
    例如: $|-5| = 5$, $|5| = 5$。
*   $\sum_{i=0}^{N-1} X_{k-i}$: 求和函数。表示将 $X_k, X_{k-1}, \dots, X_{k-N+1}$ 这 $N$ 个值相加。

【5. 计算步骤】

1.  **数据准备**:
    获取每个时间点 $k$ 的最高价 ($H_k$)、最低价 ($L_k$)、收盘价 ($C_k$)。确保有足够历史数据以计算所需周期的指标。

2.  **计算真实低点 ($TL_k$)**:
    对于每个时间点 $k$（从第二个数据点开始，因为需要 $C_{k-1}$）：
    $TL_k = \min(L_k, C_{k-1})$
    对于数据序列的第一个点 $k=0$， $C_{k-1}$ (即 $C_{-1}$) 不存在。通常处理方法是：要么从 $k=1$ 开始计算 $TL$ (此时 $C_0$ 作为前一周期收盘价)，或者将 $TL_0$ 设为 $L_0$。在此因子定义中，我们假设计算从有 $C_{k-1}$ 的点开始。

3.  **计算购买压力 ($BP_k$)**:
    对于每个时间点 $k$ (与 $TL_k$ 的计算期间对应)：
    $BP_k = C_k - TL_k$

4.  **计算真实波幅 ($TR_k$)**:
    对于每个时间点 $k$ (与 $TL_k$ 的计算期间对应)：
    $TR_k = \max( (H_k - L_k), |H_k - C_{k-1}|, |L_k - C_{k-1}| )$
    对于数据序列的第一个点 $k=0$，$TR_0$ 通常简化为 $H_0 - L_0$。在此因子定义中，我们假设计算从有 $C_{k-1}$ 的点开始。

5.  **计算各周期的BP和TR总和**:
    对于当前时间点 $t$，以及给定的三个周期 $P1, P2, P3$（已按短、中、长排序）：
    *   **短周期 (P1)**:
        $SumBP1_t = \sum_{i=0}^{P1-1} BP_{t-i}$ (最近 P1 个周期的BP值之和)
        $SumTR1_t = \sum_{i=0}^{P1-1} TR_{t-i}$ (最近 P1 个周期的TR值之和)
    *   **中周期 (P2)**:
        $SumBP2_t = \sum_{i=0}^{P2-1} BP_{t-i}$ (最近 P2 个周期的BP值之和)
        $SumTR2_t = \sum_{i=0}^{P2-1} TR_{t-i}$ (最近 P2 个周期的TR值之和)
    *   **长周期 (P3)**:
        $SumBP3_t = \sum_{i=0}^{P3-1} BP_{t-i}$ (最近 P3 个周期的BP值之和)
        $SumTR3_t = \sum_{i=0}^{P3-1} TR_{t-i}$ (最近 P3 个周期的TR值之和)

6.  **计算各周期的比率**:
    *   $Ratio1_t = \begin{cases} \frac{SumBP1_t}{SumTR1_t} & \text{if } SumTR1_t \neq 0 \\ 0 & \text{if } SumTR1_t = 0 \end{cases}$
    *   $Ratio2_t = \begin{cases} \frac{SumBP2_t}{SumTR2_t} & \text{if } SumTR2_t \neq 0 \\ 0 & \text{if } SumTR2_t = 0 \end{cases}$
    *   $Ratio3_t = \begin{cases} \frac{SumBP3_t}{SumTR3_t} & \text{if } SumTR3_t \neq 0 \\ 0 & \text{if } SumTR3_t = 0 \end{cases}$

7.  **计算终极振荡指标 ($ULTOSC_t$)**:
    $Average_t = \frac{ (4 \times Ratio1_t) + (2 \times Ratio2_t) + (1 \times Ratio3_t) }{7}$
    $ULTOSC_t = 100 \times Average_t$

    注意：权重分配是固定的：最短周期 (P1) 权重为4，中间周期 (P2) 权重为2，最长周期 (P3) 权重为1。总权重为 $4+2+1 = 7$。

【6. 备注与参数说明】

*   **参数选择**: 默认的周期参数为 $P1=7$, $P2=14$, $P3=28$。分析师可以根据所分析的证券或市场特性调整这些周期长度。较短的周期对近期价格变化更敏感，而较长的周期则提供更平滑的趋势视角。
*   **数据预处理**: 输入数据（最高价、最低价、收盘价）应为有效的市场数据，无缺失或明显错误。
*   **回溯期 (Lookback Period)**: ULTOSC的计算需要回溯数据。第一个有效的ULTOSC值，需要的最长周期（即 $\max(P1, P2, P3)$）的历史数据来计算 $BP$ 和 $TR$ 的累加和。例如，如果最长周期是28，则需要28个周期的 $BP$ 和 $TR$ 值，而这些值的计算又依赖于前一天的收盘价。因此，第一个ULTOSC指标值对应于输入序列中的第 $\max(P1,P2,P3)$ 个时间点（0-indexed，即第 $\max(P1,P2,P3)+1$ 条数据）。
*   **指标范围**: ULTOSC指标值通常在0到100之间波动。
*   **除零处理**: 如核心公式和计算步骤中所述，如果任何一个周期的真实波幅总和 ($SumTR_k$) 为零，则该周期对最终 $ULTOSC_t$ 计算的贡献（即 $Weight \times Ratio_k$）视为零。这可以防止除以零的错误，并表示在该特定周期内没有价格波动。
*   **应用解释**:
    *   低于30通常被视为超卖区域，可能预示着买入机会。
    *   高于70通常被视为超买区域，可能预示着卖出机会。
    *   价格与ULTOSC之间的背离（例如，价格创新低但ULTOSC未能创新低，形成牛市背离）也可以作为重要的交易信号。

【因子信息结束】===============================================================