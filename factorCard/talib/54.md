【因子信息开始】===============================================================

【因子编号和名称】

因子编号: MA003
因子名称: 加权移动平均 (Weighted Moving Average, WMA)

【1. 因子名称详情】

加权移动平均线 (Weighted Moving Average, WMA) 给予近期价格数据线性递增的权重。最新的数据点获得最大权重，最老的数据点获得最小权重。

【2. 核心公式】

对于时间序列数据 \(P\)，周期为 \(N\) 的加权移动平均 \(WMA_t\) 在 \(t\) 时刻的计算公式为：
\[ WMA_t = \frac{\sum_{i=0}^{N-1} w_{N-i} \cdot P_{t-i}}{\sum_{i=0}^{N-1} w_{N-i}} \]
其中，权重 \(w_k = k\)。具体展开为：
\[ WMA_t = \frac{N \cdot P_t + (N-1) \cdot P_{t-1} + \dots + 1 \cdot P_{t-N+1}}{N + (N-1) + \dots + 1} \]
分母是等差数列求和，即权重的总和：
\[ \sum_{k=1}^{N} k = \frac{N(N+1)}{2} \]
所以公式可以简化为：
\[ WMA_t = \frac{2}{N(N+1)} \sum_{i=0}^{N-1} (N-i) \cdot P_{t-i} \]

【3. 变量定义】

*   \(WMA_t\): \(t\) 时刻的加权移动平均值。
*   \(P_t\): \(t\) 时刻的价格。
*   \(P_{t-i}\): \(t-i\) 时刻的价格。
*   \(N\): 计算移动平均的周期长度。
*   \(w_k\): 分配给从近到远第 \(k\) 个价格的权重，这里 \(w_k=k\)，表示最近的价格权重为\(N\)，次近的为\(N-1\)，依此类推。

【4. 函数与方法说明】

*   \(\sum\): 求和运算符。

【5. 计算步骤】

1.  **数据准备**: 获取最近 \(N\) 个周期的数据序列 \(P_t, P_{t-1}, \dots, P_{t-N+1}\)。
2.  **分配权重**:
    *   当前价格 \(P_t\) 的权重为 \(N\)。
    *   前一个价格 \(P_{t-1}\) 的权重为 \(N-1\)。
    *   ...
    *   第 \(N\) 个过去的价格 \(P_{t-N+1}\) 的权重为 \(1\)。
3.  **计算加权和**: 将每个价格乘以其对应的权重，然后相加：
    \(WeightedSum = N \cdot P_t + (N-1) \cdot P_{t-1} + \dots + 1 \cdot P_{t-N+1}\)。
4.  **计算权重总和**: \(TotalWeight = \frac{N(N+1)}{2}\)。
5.  **计算WMA**: \(WMA_t = WeightedSum / TotalWeight\)。
6.  **滑动窗口**: 时间向前推移一个单位，重复步骤1-5。第一个WMA值需要至少\(N\)个数据点。

【6. 备注与参数说明】

*   **参数选择**:
    *   `optInTimePeriod` (周期 \(N\)): 决定了参与计算的数据点数量和权重的分配方式。
*   **数据预处理**: 通常使用收盘价。
*   **起始点**: 计算WMA需要至少 \(N\) 个数据点。时间序列的前 \(N-1\) 个点将没有WMA值。
*   **特殊情况**: 如果周期 \(N=1\)，则 \(WMA_t = P_t\)。



【7. 关联因子补充说明】

【关联因子信息开始】===============================================================

【因子编号和名称】

因子编号: MA001
因子名称: 简单移动平均 (Simple Moving Average, SMA)

【1. 因子名称详情】

简单移动平均线 (Simple Moving Average, SMA) 是指特定周期内收盘价的算术平均值。它对周期内的每个价格数据点给予相同的权重。

【2. 核心公式】

对于时间序列数据 \(P\)，周期为 \(N\) 的简单移动平均 \(SMA_t\) 在 \(t\) 时刻的计算公式为：
\[ SMA_t = \frac{1}{N} \sum_{i=0}^{N-1} P_{t-i} \]
或者写作：
\[ SMA_t = \frac{P_t + P_{t-1} + \dots + P_{t-N+1}}{N} \]

【3. 变量定义】

*   \(SMA_t\): \(t\) 时刻的简单移动平均值。
*   \(P_t\): \(t\) 时刻的价格（或其他输入数据）。
*   \(P_{t-i}\): \(t-i\) 时刻的价格。
*   \(N\): 计算移动平均的周期长度（窗口大小）。

【4. 函数与方法说明】

*   \(\sum\): 求和运算符，表示将指定范围内的数值相加。

【5. 计算步骤】

1.  **数据准备**: 获取最近 \(N\) 个周期的价格数据序列 \(P_t, P_{t-1}, \dots, P_{t-N+1}\)。
2.  **求和**: 将这 \(N\) 个周期的价格数据相加：\(Sum = P_t + P_{t-1} + \dots + P_{t-N+1}\)。
3.  **计算平均值**: 将得到的总和除以周期 \(N\)：\(SMA_t = Sum / N\)。
4.  **滑动窗口**: 时间向前推移一个单位，重复步骤1-3，计算新的SMA值。第一个SMA值需要至少\(N\)个数据点才能计算。

【6. 备注与参数说明】

*   **参数选择**:
    *   `optInTimePeriod` (周期 \(N\)): 计算平均值所回顾的K线数量。常见的选择有5, 10, 20, 30, 50, 100, 200等。周期越短，SMA对价格变化越敏感；周期越长，SMA越平滑，但滞后性也越大。
*   **数据预处理**: 通常使用收盘价作为输入\(P_t\)。也可以使用开盘价、最高价、最低价或典型价格等。
*   **起始点**: 计算SMA需要至少 \(N\) 个数据点。因此，时间序列的前 \(N-1\) 个点将没有SMA值。
*   **特殊情况**: 如果周期 \(N=1\)，则 \(SMA_t = P_t\)。

【关联因子信息结束】===============================================================

---

【关联因子信息开始】===============================================================

【因子编号和名称】

因子编号: MA002
因子名称: 指数移动平均 (Exponential Moving Average, EMA)

【1. 因子名称详情】

指数移动平均线 (Exponential Moving Average, EMA) 是一种加权移动平均，它给予近期价格数据更高的权重。与SMA相比，EMA对价格变化的反应更为迅速。

【2. 核心公式】

对于时间序列数据 \(P\)，周期为 \(N\) 的指数移动平均 \(EMA_t\) 在 \(t\) 时刻的计算公式为：
\[ EMA_t = \alpha \cdot P_t + (1-\alpha) \cdot EMA_{t-1} \]
其中，平滑系数 \(\alpha\) (alpha) 的计算方式为：
\[ \alpha = \frac{2}{N+1} \]
初始值 \(EMA_0\) (即序列中第一个EMA值) 通常采用对应周期的SMA值，或者直接取第一个数据点的值（当历史数据不足时）。TA-Lib的实现通常使用前N个数据的SMA作为第一个有效EMA值。

【3. 变量定义】

*   \(EMA_t\): \(t\) 时刻的指数移动平均值。
*   \(EMA_{t-1}\): \(t-1\) 时刻的指数移动平均值 (前一日的EMA)。
*   \(P_t\): \(t\) 时刻的价格（或其他输入数据）。
*   \(N\): 计算移动平均的周期长度。
*   \(\alpha\): 平滑系数，决定了新价格数据在EMA计算中的权重。

【4. 函数与方法说明】

*   上述公式是一个递归定义，每个EMA值都依赖于前一个EMA值。

【5. 计算步骤】

1.  **数据准备**: 获取价格数据序列 \(P\)。
2.  **确定周期 \(N\)**: 选择一个合适的周期 \(N\)。
3.  **计算平滑系数 \(\alpha\)**: \(\alpha = \frac{2}{N+1}\)。
4.  **初始化EMA**:
    *   计算第一个 \(EMA\) 值 (例如，\(EMA_{N-1}\) 如果数据从0开始索引)：通常使用前 \(N\) 个价格数据的简单移动平均 (SMA) 作为初始值。即 \(EMA_{N-1} = \frac{1}{N} \sum_{i=0}^{N-1} P_i\)。
5.  **递归计算后续EMA值**: 对于 \(\_t \ge N\):
    \[ EMA_t = \alpha \cdot P_t + (1-\alpha) \cdot EMA_{t-1} \]
    其中 \(P_t\) 是当前周期的价格，\(EMA_{t-1}\) 是上一个周期的EMA值。

【6. 备注与参数说明】

*   **参数选择**:
    *   `optInTimePeriod` (周期 \(N\)): 定义了回顾期，并间接决定了平滑系数\(\alpha\)。周期越短，\(\alpha\)越大，EMA对近期价格变化越敏感。
*   **数据预处理**: 通常使用收盘价。
*   **起始点**: 第一个EMA值的计算需要 \(N\) 个数据点（用于计算初始SMA）。因此，时间序列的前 \(N-1\) 个点将没有EMA值，第 \(N\) 个点（索引为 \(N-1\)）是第一个有EMA值的点。
*   **特殊情况**: 如果周期 \(N=1\)，则 \(\alpha = 1\)，此时 \(EMA_t = P_t\)。

【关联因子信息结束】===============================================================

---

【关联因子信息开始】===============================================================

【因子编号和名称】

因子编号: MA003
因子名称: 加权移动平均 (Weighted Moving Average, WMA)

【1. 因子名称详情】

加权移动平均线 (Weighted Moving Average, WMA) 给予近期价格数据线性递增的权重。最新的数据点获得最大权重，最老的数据点获得最小权重。

【2. 核心公式】

对于时间序列数据 \(P\)，周期为 \(N\) 的加权移动平均 \(WMA_t\) 在 \(t\) 时刻的计算公式为：
\[ WMA_t = \frac{\sum_{i=0}^{N-1} w_{N-i} \cdot P_{t-i}}{\sum_{i=0}^{N-1} w_{N-i}} \]
其中，权重 \(w_k = k\)。具体展开为：
\[ WMA_t = \frac{N \cdot P_t + (N-1) \cdot P_{t-1} + \dots + 1 \cdot P_{t-N+1}}{N + (N-1) + \dots + 1} \]
分母是等差数列求和，即权重的总和：
\[ \sum_{k=1}^{N} k = \frac{N(N+1)}{2} \]
所以公式可以简化为：
\[ WMA_t = \frac{2}{N(N+1)} \sum_{i=0}^{N-1} (N-i) \cdot P_{t-i} \]

【3. 变量定义】

*   \(WMA_t\): \(t\) 时刻的加权移动平均值。
*   \(P_t\): \(t\) 时刻的价格。
*   \(P_{t-i}\): \(t-i\) 时刻的价格。
*   \(N\): 计算移动平均的周期长度。
*   \(w_k\): 分配给从近到远第 \(k\) 个价格的权重，这里 \(w_k=k\)，表示最近的价格权重为\(N\)，次近的为\(N-1\)，依此类推。

【4. 函数与方法说明】

*   \(\sum\): 求和运算符。

【5. 计算步骤】

1.  **数据准备**: 获取最近 \(N\) 个周期的数据序列 \(P_t, P_{t-1}, \dots, P_{t-N+1}\)。
2.  **分配权重**:
    *   当前价格 \(P_t\) 的权重为 \(N\)。
    *   前一个价格 \(P_{t-1}\) 的权重为 \(N-1\)。
    *   ...
    *   第 \(N\) 个过去的价格 \(P_{t-N+1}\) 的权重为 \(1\)。
3.  **计算加权和**: 将每个价格乘以其对应的权重，然后相加：
    \(WeightedSum = N \cdot P_t + (N-1) \cdot P_{t-1} + \dots + 1 \cdot P_{t-N+1}\)。
4.  **计算权重总和**: \(TotalWeight = \frac{N(N+1)}{2}\)。
5.  **计算WMA**: \(WMA_t = WeightedSum / TotalWeight\)。
6.  **滑动窗口**: 时间向前推移一个单位，重复步骤1-5。第一个WMA值需要至少\(N\)个数据点。

【6. 备注与参数说明】

*   **参数选择**:
    *   `optInTimePeriod` (周期 \(N\)): 决定了参与计算的数据点数量和权重的分配方式。
*   **数据预处理**: 通常使用收盘价。
*   **起始点**: 计算WMA需要至少 \(N\) 个数据点。时间序列的前 \(N-1\) 个点将没有WMA值。
*   **特殊情况**: 如果周期 \(N=1\)，则 \(WMA_t = P_t\)。

【关联因子信息结束】===============================================================

---

【关联因子信息开始】===============================================================

【因子编号和名称】

因子编号: MA004
因子名称: 双指数移动平均 (Double Exponential Moving Average, DEMA)

【1. 因子名称详情】

双指数移动平均 (Double Exponential Moving Average, DEMA) 由 Patrick Mulloy 开发，旨在减少移动平均线的滞后性。它通过结合单个EMA和该EMA的EMA来实现。

【2. 核心公式】

对于时间序列数据 \(P\)，周期为 \(N\) 的双指数移动平均 \(DEMA_t\) 在 \(t\) 时刻的计算公式为：
\[ DEMA_t = 2 \cdot EMA(P, N)_t - EMA(EMA(P, N), N)_t \]
其中：
*   \(EMA(P, N)_t\) 是价格 \(P\) 在 \(t\) 时刻，周期为 \(N\) 的指数移动平均。
*   \(EMA(EMA(P, N), N)_t\) 是对 \(EMA(P, N)\) 序列再次进行周期为 \(N\) 的指数移动平均。

【3. 变量定义】

*   \(DEMA_t\): \(t\) 时刻的双指数移动平均值。
*   \(P\): 原始价格数据序列。
*   \(N\): 计算EMA时使用的周期长度。
*   \(EMA(X, N)_t\): 对序列 \(X\) 计算的周期为 \(N\) 的指数移动平均在 \(t\) 时刻的值。

【4. 函数与方法说明】

*   **指数移动平均 (EMA)**:
    \[ EMA_t = \alpha \cdot X_t + (1-\alpha) \cdot EMA_{t-1} \]
    其中 \(\alpha = \frac{2}{N+1}\)，\(X_t\) 是输入序列在 \(t\) 时刻的值。第一个 \(EMA\) 值通常使用前 \(N\) 个 \(X\) 值的简单移动平均 (SMA) 初始化。

【5. 计算步骤】

1.  **数据准备**: 获取价格数据序列 \(P\)。
2.  **确定周期 \(N\)**: 选择一个合适的周期 \(N\)。
3.  **计算第一个EMA (EMA1)**:
    *   设 \(EMA1_t = EMA(P, N)_t\)。
    *   计算平滑系数 \(\alpha = \frac{2}{N+1}\)。
    *   初始化 \(EMA1\): 例如，\(EMA1_{N-1} = \frac{1}{N} \sum_{i=0}^{N-1} P_i\)。
    *   对于 \(\_t \ge N\)，递归计算 \(EMA1_t = \alpha \cdot P_t + (1-\alpha) \cdot EMA1_{t-1}\)。
4.  **计算第二个EMA (EMA2)**:
    *   设 \(EMA2_t = EMA(EMA1, N)_t\)。这是对步骤3中计算出的 \(EMA1\) 序列再次进行EMA计算。
    *   使用相同的平滑系数 \(\alpha = \frac{2}{N+1}\)。
    *   初始化 \(EMA2\): 第一个有效的 \(EMA1\) 值是 \(EMA1_{N-1}\)。因此，第一个 \(EMA2\) 值 (例如，\(EMA2_{2N-2}\)) 需要 \(N\) 个 \(EMA1\) 值来初始化（通过SMA）。即 \(EMA2_{2N-2} = \frac{1}{N} \sum_{i=0}^{N-1} EMA1_{N-1+i}\)。
    *   对于后续的 \(\_t\)，递归计算 \(EMA2_t = \alpha \cdot EMA1_t + (1-\alpha) \cdot EMA2_{t-1}\)。
5.  **计算DEMA**:
    \[ DEMA_t = 2 \cdot EMA1_t - EMA2_t \]
    确保 \(EMA1_t\) 和 \(EMA2_t\) 是对应同一时刻 \(t\) 的值。

【6. 备注与参数说明】

*   **参数选择**:
    *   `optInTimePeriod` (周期 \(N\)): DEMA唯一的参数，用于其内部所有EMA的计算。
*   **数据预处理**: 通常使用收盘价。
*   **起始点**: 由于DEMA依赖于两次EMA计算，其第一个有效值所需的原始数据点数量比单个EMA更多。具体来说，第一个 \(EMA1\) 需要 \(N\) 个数据点，第一个 \(EMA2\) 又需要 \(N\) 个 \(EMA1\) 值。因此，第一个DEMA值将出现在原始数据序列的第 \(2N-1\) 个点之后 (TA-Lib的lookback是 \(2 \cdot (N-1)\))。
*   **特殊情况**: 如果周期 \(N=1\)，\(\alpha=1\)，则 \(EMA(P,1)_t = P_t\)。那么 \(EMA1_t = P_t\)，\(EMA2_t = EMA(P,1)_t = P_t\)。所以 \(DEMA_t = 2 P_t - P_t = P_t\)。

【关联因子信息结束】===============================================================

---

【关联因子信息开始】===============================================================

【因子编号和名称】

因子编号: MA005
因子名称: 三重指数移动平均 (Triple Exponential Moving Average, TEMA)

【1. 因子名称详情】

三重指数移动平均 (Triple Exponential Moving Average, TEMA) 同样由 Patrick Mulloy 开发，旨在进一步减少移动平均线的滞后性，比DEMA更平滑且反应更灵敏。

【2. 核心公式】

对于时间序列数据 \(P\)，周期为 \(N\) 的三重指数移动平均 \(TEMA_t\) 在 \(t\) 时刻的计算公式为：
\[ TEMA_t = 3 \cdot EMA(P, N)_t - 3 \cdot EMA(EMA(P, N), N)_t + EMA(EMA(EMA(P, N), N), N)_t \]
简写为：
\[ TEMA_t = 3 \cdot EMA1_t - 3 \cdot EMA2_t + EMA3_t \]
其中：
*   \(EMA1_t = EMA(P, N)_t\)
*   \(EMA2_t = EMA(EMA1, N)_t\)
*   \(EMA3_t = EMA(EMA2, N)_t\)

【3. 变量定义】

*   \(TEMA_t\): \(t\) 时刻的三重指数移动平均值。
*   \(P\): 原始价格数据序列。
*   \(N\): 计算EMA时使用的周期长度。
*   \(EMA1_t\): 对原始价格 \(P\) 计算的EMA。
*   \(EMA2_t\): 对 \(EMA1\) 序列计算的EMA。
*   \(EMA3_t\): 对 \(EMA2\) 序列计算的EMA。

【4. 函数与方法说明】

*   **指数移动平均 (EMA)**:
    \[ EMA_t = \alpha \cdot X_t + (1-\alpha) \cdot EMA_{t-1} \]
    其中 \(\alpha = \frac{2}{N+1}\)，\(X_t\) 是输入序列在 \(t\) 时刻的值。第一个 \(EMA\) 值通常使用前 \(N\) 个 \(X\) 值的简单移动平均 (SMA) 初始化。

【5. 计算步骤】

1.  **数据准备**: 获取价格数据序列 \(P\)。
2.  **确定周期 \(N\)**: 选择一个合适的周期 \(N\)。
3.  **计算第一个EMA (EMA1)**:
    *   \(EMA1_t = EMA(P, N)_t\)。
    *   计算平滑系数 \(\alpha = \frac{2}{N+1}\)。
    *   初始化并递归计算 \(EMA1\) 序列（详见EMA因子卡片MA002的计算步骤）。
4.  **计算第二个EMA (EMA2)**:
    *   \(EMA2_t = EMA(EMA1, N)_t\)。
    *   使用相同的平滑系数 \(\alpha\)。
    *   以 \(EMA1\) 序列为输入，初始化并递归计算 \(EMA2\) 序列。
5.  **计算第三个EMA (EMA3)**:
    *   \(EMA3_t = EMA(EMA2, N)_t\)。
    *   使用相同的平滑系数 \(\alpha\)。
    *   以 \(EMA2\) 序列为输入，初始化并递归计算 \(EMA3\) 序列。
6.  **计算TEMA**:
    \[ TEMA_t = 3 \cdot EMA1_t - 3 \cdot EMA2_t + EMA3_t \]
    确保 \(EMA1_t, EMA2_t, EMA3_t\) 是对应同一时刻 \(t\) 的值。

【6. 备注与参数说明】

*   **参数选择**:
    *   `optInTimePeriod` (周期 \(N\)): TEMA唯一的参数，用于其内部所有EMA的计算。
*   **数据预处理**: 通常使用收盘价。
*   **起始点**: TEMA依赖于三次EMA计算。第一个有效TEMA值所需的原始数据点数量更多。TA-Lib的lookback是 \(3 \cdot (N-1)\)。
*   **特殊情况**: 如果周期 \(N=1\)，\(\alpha=1\)，则 \(EMA(X,1)_t = X_t\)。那么 \(EMA1_t = P_t\)，\(EMA2_t = P_t\)，\(EMA3_t = P_t\)。所以 \(TEMA_t = 3 P_t - 3 P_t + P_t = P_t\)。

【关联因子信息结束】===============================================================

---

【关联因子信息开始】===============================================================

【因子编号和名称】

因子编号: MA006
因子名称: 三角移动平均 (Triangular Moving Average, TRIMA)

【1. 因子名称详情】

三角移动平均 (Triangular Moving Average, TRIMA) 是一种加权移动平均，其权重呈三角形分布，中间数据的权重最大，两端数据的权重最小。这通常通过对简单移动平均 (SMA) 进行再次简单移动平均来实现。

【2. 核心公式】

对于周期 \(N\) 的TRIMA，其计算可以等效为两次SMA运算。
设 \(N_{SMA1}\) 和 \(N_{SMA2}\) 是两次SMA的周期。
如果周期 \(N\) 是奇数, 则 \(k = (N+1)/2\)。
\[ TRIMA_t = SMA(SMA(P, k), k)_t \]
如果周期 \(N\) 是偶数, 则 \(k_1 = N/2\) 并且 \(k_2 = N/2 + 1\)。
\[ TRIMA_t = SMA(SMA(P, k_1), k_2)_t \]
(注意：TA-Lib的`TRIMA`函数内部实现时，对于周期 `optInTimePeriod`，它实际上计算 `SMA(SMA(P, ceil(optInTimePeriod/2)), ceil((optInTimePeriod+1)/2))`。为了简化，我们采用一个统一的思路：`ceil(N/2)` 和 `ceil((N+1)/2)`。设 `period1 = ceil(N/2)` (向上取整)， `period2 = floor(N/2) + 1` (若N为偶数，这与period1相同；若N为奇数，这也与period1相同)。
更准确地说，TA-Lib内部对TRIMA的周期是N，它会计算两个内部周期 `(N+1)/2`（整数除法，相当于`floor((N+1)/2)`，或者说对于奇数是`(N+1)/2`，对于偶数是`N/2`）和 `(N+2)/2`（整数除法，对于奇数是`(N+1)/2`，对于偶数是`N/2+1`）。
为保持与TA-Lib的`TA_TRIMA`实现方式一致，通常使用如下规则：
令 \(N_1 = \lceil N/2 \rceil\) （向上取整到最接近的整数）
令 \(N_2 = \lfloor N/2 \rfloor + 1\) (向下取整后加1，这与 \(N_1\) 对奇数 \(N\) 相等，对偶数 \(N\) 时 \(N_2 = N/2+1\), \(N_1 = N/2\))
实际上，TA-Lib TRIMA的实现方式是：
令 \(p_1 = (N+1)/2\) (使用整数除法)
令 \(p_2 = (N+2)/2\) (使用整数除法)
\(SMA1_t = SMA(P, p_1)_t\)
\(TRIMA_t = SMA(SMA1, p_2)_t\)
如果N是奇数, N=5, p1=3, p2=3. SMA(SMA(P,3),3)
如果N是偶数, N=4, p1=2, p2=3. SMA(SMA(P,2),3)
这与更常见的定义  \(N_S = \lceil (N+1)/2 \rceil\)，然后 \(TRIMA_t = SMA(SMA(P, N_S), N_S)_t\)略有不同。
本卡片采用TA-Lib的实现思路，但将其统一表达为：
设 \(k_1 = \text{ceil}(N/2)\)
设 \(k_2 = \text{ceil}((N+1)/2)\) (对于TA-Lib的TRIMA实现，通常 \(k_1\) 用于第一个SMA，\(k_2\) 用于第二个SMA，或者反之，只要总的平滑效果等同于三角权重)。
我们将采用一个常见的实现方式：
如果 N 是奇数： \(k = (N+1)/2\)。则 \(TRIMA_t = SMA(SMA(P, k), k)_t\)。
如果 N 是偶数： \(k_1 = N/2\), \(k_2 = N/2+1\)。则 \(TRIMA_t = SMA(SMA(P, k_1), k_2)_t\)。

【3. 变量定义】

*   \(TRIMA_t\): \(t\) 时刻的三角移动平均值。
*   \(P\): 原始价格数据序列。
*   \(N\): TRIMA的整体周期。
*   \(SMA(X, k)_t\): 对序列 \(X\) 计算的周期为 \(k\) 的简单移动平均在 \(t\) 时刻的值。
*   \(k, k_1, k_2\): 内部SMA计算所使用的周期。

【4. 函数与方法说明】

*   **简单移动平均 (SMA)**:
    \[ SMA_t(X, k) = \frac{1}{k} \sum_{i=0}^{k-1} X_{t-i} \]
    其中 \(X_t\) 是输入序列在 \(t\) 时刻的值。 第一个SMA值需要至少\(k\)个数据点才能计算。

【5. 计算步骤】

1.  **数据准备**: 获取价格数据序列 \(P\)。
2.  **确定周期 \(N\)**: 选择TRIMA的整体周期 \(N\)。
3.  **确定内部SMA周期**:
    *   如果 \(N\) 是奇数: \(k_1 = (N+1)/2\), \(k_2 = (N+1)/2\)。
    *   如果 \(N\) 是偶数: \(k_1 = N/2\), \(k_2 = N/2+1\)。
4.  **计算第一个SMA (SMA1)**:
    *   \(SMA1_t = SMA(P, k_1)_t\)。
    *   计算SMA1序列 (详见SMA因子卡片MA001的计算步骤)。
5.  **计算第二个SMA (即TRIMA)**:
    *   \(TRIMA_t = SMA(SMA1, k_2)_t\)。
    *   以 \(SMA1\) 序列为输入，计算 \(TRIMA\) 序列。

【6. 备注与参数说明】

*   **参数选择**:
    *   `optInTimePeriod` (周期 \(N\)): TRIMA的整体周期。
*   **数据预处理**: 通常使用收盘价。
*   **起始点**: Aufgrund der zweifachen SMA-Berechnung ist die Anzahl der für den ersten gültigen TRIMA-Wert erforderlichen ursprünglichen Datenpunkte \(k_1 + k_2 - 1\). Für ungerade \(N\) ist dies \((N+1)/2 + (N+1)/2 - 1 = N\). Für gerade \(N\) ist dies \(N/2 + N/2+1 - 1 = N\). TA-Lib的lookback是 \((N-1)\)。
*   **权重特性**: 这种双重SMA的计算方式产生的权重分布近似于一个三角形，中间的权重最高。
*   **特殊情况**: 如果周期 \(N=1\)，则 \(k_1=1, k_2=1\)。 \(SMA(SMA(P,1),1)_t = SMA(P,1)_t = P_t\)。所以 \(TRIMA_t = P_t\)。

【关联因子信息结束】===============================================================

---

【关联因子信息开始】===============================================================

【因子编号和名称】

因子编号: MA007
因子名称: 考夫曼自适应移动平均 (Kaufman Adaptive Moving Average, KAMA)

【1. 因子名称详情】

考夫曼自适应移动平均 (KAMA) 由 Perry Kaufman 开发，它根据市场波动性自动调整其平滑程度。在趋势市场中，KAMA紧随价格；在震荡市场中，KAMA变得更平滑以滤除噪音。

【2. 核心公式】

1.  **效率比率 (Efficiency Ratio, ER)**:
    \[ ER_t = \frac{|\text{方向}_t|}{\text{波动率}_t} \]
    其中：
    *   方向 (Direction): \( \text{方向}_t = P_t - P_{t-N_{ER}} \) (周期 \(N_{ER}\) 内的价格变化)
    *   波动率 (Volatility): \( \text{波动率}_t = \sum_{i=0}^{N_{ER}-1} |P_{t-i} - P_{t-i-1}| \) (周期 \(N_{ER}\) 内每日价格绝对变化之和)
    如果 \(\text{波动率}_t = 0\)，则 \(ER_t\) 通常设为1（如果方向也为0）或根据实现特定处理。

2.  **平滑常数 (Smoothing Constant, SC)**:
    \[ SC_t = ER_t \cdot (\text{Alpha}_{\text{fast}} - \text{Alpha}_{\text{slow}}) + \text{Alpha}_{\text{slow}} \]
    或者，一些文献中描述为：
    \[ SC_t = \left( ER_t \cdot \left( \frac{2}{n_{fast}+1} - \frac{2}{n_{slow}+1} \right) + \frac{2}{n_{slow}+1} \right) \]
    TA-Lib的实现中不包含对SC的平方操作。
    其中：
    *   \( \text{Alpha}_{\text{fast}} = \frac{2}{n_{fast}+1} \) (对应快速EMA的平滑系数)
    *   \( \text{Alpha}_{\text{slow}} = \frac{2}{n_{slow}+1} \) (对应慢速EMA的平滑系数)

3.  **KAMA 计算**:
    \[ KAMA_t = KAMA_{t-1} + SC_t \cdot (P_t - KAMA_{t-1}) \]
    这与EMA的计算形式类似，但平滑常数 \(SC_t\) 是动态变化的。
    初始 \(KAMA_0\) 通常设置为序列中的第一个价格值或者是在 \(N_{ER}\) 周期后的价格或SMA。

【3. 变量定义】

*   \(KAMA_t\): \(t\) 时刻的考夫曼自适应移动平均值。
*   \(KAMA_{t-1}\): \(t-1\) 时刻的KAMA值。
*   \(P_t\): \(t\) 时刻的价格。
*   \(N_{ER}\): 计算效率比率 ER 的周期 (TA-Lib中对应 `optInTimePeriod`)。
*   \(n_{fast}\): 快速EMA对应的周期 (TA-Lib默认为2)。
*   \(n_{slow}\): 慢速EMA对应的周期 (TA-Lib默认为30)。
*   \(ER_t\): \(t\) 时刻的效率比率。
*   \(SC_t\): \(t\) 时刻的动态平滑常数。

【4. 函数与方法说明】

*   \(\sum\): 求和运算符。
*   \(|\cdot|\): 绝对值运算符。

【5. 计算步骤】

1.  **数据准备**: 获取价格数据序列 \(P\)。
2.  **设定参数**:
    *   ER周期 \(N_{ER}\) (例如：10)。
    *   快速EMA周期 \(n_{fast}\) (例如：2)。
    *   慢速EMA周期 \(n_{slow}\) (例如：30)。
3.  **计算 \(\text{Alpha}_{\text{fast}}\) 和 \(\text{Alpha}_{\text{slow}}\)**:
    *   \( \text{Alpha}_{\text{fast}} = 2 / (n_{fast}+1) \)
    *   \( \text{Alpha}_{\text{slow}} = 2 / (n_{slow}+1) \)
4.  **初始化KAMA**: 第一个KAMA值 (例如 \(KAMA_{N_{ER}}\)) 通常取在有足够数据计算第一个ER后的价格 \(P_{N_{ER}}\) 或 \(N_{ER}\) 周期的SMA。TA-Lib的第一个KAMA值是在 `startIdx + LOOKBACK_CALL(KAMA)(...) - 1` 处的价格。
5.  **迭代计算后续KAMA值 (对于 \(t > N_{ER}\))**:
    a.  **计算方向**: \( \text{方向}_t = P_t - P_{t-N_{ER}} \)。
    b.  **计算波动率**: \( \text{波动率}_t = \sum_{i=0}^{N_{ER}-1} |P_{t-i} - P_{t-i-1}| \)。若 \(\text{波动率}_t = 0\)，处理（如设 \(ER_t=1\) 或 \(ER_t=0\)）。
    c.  **计算效率比率 ER**: \( ER_t = |\text{方向}_t| / \text{波动率}_t \)。
    d.  **计算平滑常数 SC**: \( SC_t = ER_t \cdot (\text{Alpha}_{\text{fast}} - \text{Alpha}_{\text{slow}}) + \text{Alpha}_{\text{slow}} \)。 (注意：某些版本会对这个结果再平方，但TA-Lib不这么做)。
    e.  **计算KAMA**: \( KAMA_t = KAMA_{t-1} + SC_t \cdot (P_t - KAMA_{t-1}) \)。

【6. 备注与参数说明】

*   **参数选择**:
    *   `optInTimePeriod` (周期 \(N_{ER}\)): 用于计算效率比率ER。
    *   `optInFastPeriod` (\(n_{fast}\)): 内部参数，定义最快响应速度，TA-Lib中KAMA函数有此参数，但此MA通用接口不直接暴露，通常使用默认值。
    *   `optInSlowPeriod` (\(n_{slow}\)): 内部参数，定义最慢响应速度，TA-Lib中KAMA函数有此参数，通常使用默认值。
*   **数据预处理**: 通常使用收盘价。
*   **起始点**: 计算KAMA需要至少 \(N_{ER}\) 个周期的数据来计算第一个ER。TA-Lib KAMA的lookback是 \(N_{ER}\) + `TA_SMA_Lookback(n_slow)` - 1 (因内部有EMA的EMA的预热过程)。对于通用接口，lookback可能简化为 \(N_{ER}\)。
*   **特殊情况**: 如果周期 \(N_{ER}=1\)，则方向为 \(P_t - P_{t-1}\)，波动率为 \(|P_t - P_{t-1}|\)，所以ER=1。SC将等于\(\text{Alpha}_{\text{fast}}\)，KAMA将表现为一个快速EMA。

【关联因子信息结束】===============================================================

---

【关联因子信息开始】===============================================================

【因子编号和名称】

因子编号: MA008
因子名称: MESA自适应移动平均 (MESA Adaptive Moving Average, MAMA)

【1. 因子名称详情】

MESA自适应移动平均 (MAMA) 由 John Ehlers 开发，是一种先进的自适应移动平均线。它使用希尔伯特变换 (Hilbert Transform) 来测量市场的主导周期，并根据该周期动态调整其平滑度。FAMA (Following Adaptive Moving Average) 是MAMA的伴随指标，通常用于产生交易信号。此处的因子仅指MAMA线。

【2. 核心思想（非完整公式，因其复杂性）】

MAMA的核心思想是通过一系列数字信号处理技术来估计当前市场价格波动的主导周期，然后利用这个周期信息来调整一个类似EMA的平滑滤波器的参数。

主要步骤概览：
1.  **价格平滑预处理**: 通常对原始价格进行初步平滑处理。
    \( SmoothPrice_t = (4 \cdot P_t + 3 \cdot P_{t-1} + 2 \cdot P_{t-2} + P_{t-3}) / 10 \)
2.  **高通滤波 (Detrender)**: 移除价格序列中的低频趋势成分，得到周期性分量。
    \( Detrender_t = (0.0962 \cdot SP_t + 0.5769 \cdot SP_{t-2} - 0.5769 \cdot SP_{t-4} - 0.0962 \cdot SP_{t-6}) \cdot (0.075 \cdot Period_{t-1}^* + 0.54) \)
    其中 \(SP\) 是 \(SmoothPrice\)，\(Period_{t-1}^*\) 是前一时刻计算并平滑后的周期。
3.  **希尔伯特变换器组件 (InPhase - I, Quadrature - Q)**: 对高通滤波后的序列计算其同相分量 (I1) 和正交分量 (Q1)。
    \( Q1_t = (0.0962 \cdot Detrender_t + \dots) \cdot (0.075 \cdot Period_{t-1}^* + 0.54) \)
    \( I1_t = Detrender_{t-3} \)
4.  **相位计算**: 通过I1和Q1分量，进一步处理（例如再次滤波、计算幅角）以估计瞬时相位变化。
    I1和Q1会再次通过类似希尔伯特变换的滤波器得到 \(jI\) 和 \(jQ\)。
    \( I2_t = I1_t - jQ_t \)
    \( Q2_t = Q1_t + jI_t \)
    然后对I2和Q2进行平滑。
5.  **主导周期估计**: 根据相位变化的速率（相位导数）计算市场的主导周期 \(Period_t\)。
    \( Re_t = I2_t \cdot I2_{t-1} + Q2_t \cdot Q2_{t-1} \)
    \( Im_t = I2_t \cdot Q2_{t-1} - Q2_t \cdot I2_{t-1} \)
    对Re和Im平滑。然后 \(Period_t = 2\pi / \operatorname{atan2}(Im_t, Re_t)\) (弧度转换为周期)。周期值通常会被限制在一定范围内（如6到50），并进行平滑得到 \(Period_t^*\)。
6.  **自适应平滑系数 (\(\alpha_t\))**: 根据估计的主导周期 \(Period_t^*\) 和预设的快慢限制 (FastLimit, SlowLimit) 来计算平滑系数。
    \( \alpha_t = \text{FastLimit} / Period_t^* \)
    然后将 \(\alpha_t\) 限制在 \([\text{SlowLimit}, \text{FastLimit}]\) 区间内。
    (TA-Lib `TA_MA` 调用 `TA_MAMA` 时固定 `FastLimit = 0.5`, `SlowLimit = 0.05`)
7.  **MAMA 计算**: 使用类似EMA的递归公式，但使用动态的 \(\alpha_t\)。
    \( MAMA_t = \alpha_t \cdot P_t + (1-\alpha_t) \cdot MAMA_{t-1} \)
    (TA-Lib实现中，MAMA的计算会更复杂，可能直接用 \(P_t\) 或 \(SmoothPrice_t\))。

【3. 变量定义】

*   \(MAMA_t\): \(t\) 时刻的MESA自适应移动平均值。
*   \(P_t\): \(t\) 时刻的价格。
*   \(SmoothPrice_t\): \(t\) 时刻的平滑价格。
*   \(Detrender_t\): \(t\) 时刻的趋势剔除（高通滤波后）序列。
*   \(I1_t, Q1_t, I2_t, Q2_t\): 希尔伯特变换中间分量。
*   \(Period_t^*\): \(t\) 时刻平滑后的主导周期估计。
*   \(\alpha_t\): \(t\) 时刻的自适应平滑系数。
*   `FastLimit`: \(\alpha\) 的上限 (TA-Lib 中此接口固定为 0.5)。
*   `SlowLimit`: \(\alpha\) 的下限 (TA-Lib 中此接口固定为 0.05)。

【4. 函数与方法说明】

*   **希尔伯特变换 (近似)**: 一种数学变换，用于产生信号的解析表示，从而分离出幅度和相位信息。在MAMA中用于辅助周期检测。
*   **数字滤波器**: 包括低通、高通滤波器，用于信号的预处理和分量提取。
*   **递归平滑**: 类似EMA的平滑方式，广泛用于中间变量和最终MAMA值的计算。

【5. 计算步骤】

由于MAMA的计算非常复杂且涉及多个内部状态和反馈回路，这里仅提供高度概括的步骤顺序：
1.  **数据准备与初始化**: 获取价格序列 \(P\)。初始化MAMA的各种内部状态变量（例如历史价格缓冲区、历史I/Q分量、历史周期等）。第一个MAMA值需要较长的数据预热期。
2.  **对每个新的价格点 \(P_t\)**:
    a.  计算平滑价格 \(SmoothPrice_t\)。
    b.  计算高通滤波后的Detrender序列 \(Detrender_t\)。
    c.  通过近似希尔伯特变换计算I1, Q1分量，进而得到I2, Q2分量。
    d.  平滑I2, Q2分量。
    e.  使用平滑后的I2, Q2计算瞬时相位变化，并由此估算主导周期 \(Period_t\)。
    f.  对 \(Period_t\) 进行合理性约束和平滑，得到 \(Period_t^*\)。
    g.  根据 \(Period_t^*\), `FastLimit` (0.5), `SlowLimit` (0.05) 计算自适应平滑系数 \(\alpha_t\)。
    h.  使用 \(\alpha_t\) 更新MAMA值： \(MAMA_t = \alpha_t \cdot \text{InputForMAMA}_t + (1-\alpha_t) \cdot MAMA_{t-1}\)。(\(\text{InputForMAMA}_t\) 可能是 \(P_t\) 或 \(SmoothPrice_t\) )。

【6. 备注与参数说明】

*   **参数**:
    *   `optInTimePeriod`: 对于TA-Lib中 `TA_MA` 函数调用 `TA_MAMA` 的情况，此参数被忽略。
    *   `optInFastLimit`: 固定为 0.5。决定了MAMA对价格变化的最大响应速率（最小平滑周期对应的alpha）。
    *   `optInSlowLimit`: 固定为 0.05。决定了MAMA对价格变化的最小响应速率（最大平滑周期对应的alpha）。
*   **数据预处理**: 通常使用收盘价。
*   **起始点**: MAMA的计算非常复杂，需要大量的初始数据（TA-Lib中MAMA的lookback通常是32，但可能因内部平滑周期而变化）来“预热”其内部滤波器状态和周期估计。
*   **FAMA**: MAMA通常伴随一个FAMA (Following Adaptive Moving Average) 输出，它是由MAMA进一步平滑得到的。但此处的MA通用接口只输出MAMA。
*   **复杂性**: MAMA是计算最复杂的移动平均线之一，其目的是提供高度自适应的平滑效果。

【关联因子信息结束】===============================================================

---

【关联因子信息开始】===============================================================

【因子编号和名称】

因子编号: MA009
因子名称: T3 移动平均 (T3 Moving Average)

【1. 因子名称详情】

T3 移动平均由 Tim Tillson 开发，是一种平滑且响应迅速的移动平均线。它通过对指数移动平均 (EMA) 进行多次迭代平滑并应用特定系数来实现，旨在减少滞后性。

【2. 核心公式】

T3 的计算基于多次应用一种广义DEMA (GD) 的概念。
设 \(GD(X, N, vFactor)\) 为对序列 \(X\) 计算的广义DEMA，周期为 \(N\)，因子为 \(vFactor\):
\[ EMA1(X)_t = EMA(X, N)_t \]
\[ EMA2(X)_t = EMA(EMA1(X), N)_t \]
\[ GD(X, N, vFactor)_t = (1 + vFactor) \cdot EMA1(X)_t - vFactor \cdot EMA2(X)_t \]

T3 是对此 GD 运算进行三次迭代：
\[ GD1_t = GD(P, N, vFactor)_t \]
\[ GD2_t = GD(GD1, N, vFactor)_t \]
\[ T3_t = GD(GD2, N, vFactor)_t \]

展开后，T3的最终形式是六个级联EMA的加权组合：
令 \(e_1 = EMA(P, N)\)
令 \(e_2 = EMA(e_1, N)\)
令 \(e_3 = EMA(e_2, N)\)
令 \(e_4 = EMA(e_3, N)\)
令 \(e_5 = EMA(e_4, N)\)
令 \(e_6 = EMA(e_5, N)\)
则T3的计算公式为：
\[ T3_t = -vFactor^3 \cdot e_{6,t} + (3 \cdot vFactor^2 + 3 \cdot vFactor^3) \cdot e_{5,t} - (6 \cdot vFactor^2 + 3 \cdot vFactor + 3 \cdot vFactor^3) \cdot e_{4,t} + (1 + 3 \cdot vFactor + 3 \cdot vFactor^2 + vFactor^3) \cdot e_{3,t} \]
(TA-Lib中 `TA_MA` 调用 `TA_T3` 时固定 `vFactor = 0.7`)

【3. 变量定义】

*   \(T3_t\): \(t\) 时刻的T3移动平均值。
*   \(P\): 原始价格数据序列。
*   \(N\): 计算EMA时使用的基础周期长度 (对应 `optInTimePeriod`)。
*   \(vFactor\): 体积因子或平滑因子 (TA-Lib中此接口固定为0.7)。
*   \(e_{k,t}\): 第 \(k\) 次迭代的EMA在 \(t\) 时刻的值。
*   \(EMA(X,N)_t\): 对序列X计算的周期为N的指数移动平均在t时刻的值。

【4. 函数与方法说明】

*   **指数移动平均 (EMA)**:
    \[ EMA_t(X) = \alpha \cdot X_t + (1-\alpha) \cdot EMA_{t-1}(X) \]
    其中 \(\alpha = \frac{2}{N+1}\)，\(X_t\) 是输入序列在 \(t\) 时刻的值。第一个 \(EMA\) 值通常使用前 \(N\) 个 \(X\) 值的简单移动平均 (SMA) 初始化。

【5. 计算步骤】

1.  **数据准备**: 获取价格数据序列 \(P\)。
2.  **设定参数**:
    *   周期 \(N\) (`optInTimePeriod`)。
    *   \(vFactor\) (在此接口中固定为 0.7)。
3.  **计算基础平滑系数 \(\alpha\)**: \(\alpha = \frac{2}{N+1}\)。
4.  **计算六级级联EMA**:
    a.  \(e_{1,t} = EMA(P, N)_t\)。 (解释EMA计算: 初始化 \(e_1\) 通常用前 \(N\) 个 \(P\) 的SMA，然后递归 \(e_{1,t} = \alpha P_t + (1-\alpha)e_{1,t-1}\))
    b.  \(e_{2,t} = EMA(e_1, N)_t\)。 (以 \(e_1\) 序列为输入，用相同 \(\alpha\) 计算)
    c.  \(e_{3,t} = EMA(e_2, N)_t\)。
    d.  \(e_{4,t} = EMA(e_3, N)_t\)。
    e.  \(e_{5,t} = EMA(e_4, N)_t\)。
    f.  \(e_{6,t} = EMA(e_5, N)_t\)。
5.  **计算T3系数 (使用 \(v = vFactor\))**:
    *   \(c_1 = -v^3\)
    *   \(c_2 = 3v^2 + 3v^3 = 3v^2(1+v)\)
    *   \(c_3 = -(6v^2 + 3v + 3v^3) = -3v(1+v)^2 - 3v^2\)  (Correction: original Tillson formula: \(c3 = - (6v^2 + 3v(1+v) + v^3)\), or simply coefficients derived from expanding the GD form: \(c_3 = -3v - 6v^2 - 3v^3 = -3v(1+v)^2\) )
    *   \(c_4 = 1 + 3v + 3v^2 + v^3 = (1+v)^3\)
    The coefficients used in TA-Lib for T3 are (using `b` for `vFactor`):
    *   `gdValue1 = e3_val * (1+b) - (e4_val * b)`
    *   `gdValue2 = e2_val * (1+b) - (gdValue1 * b)`
    *   `gdValue3 = e1_val * (1+b) - (gdValue2 * b)` (this is from `ta_T3.c` if it was one level of GD)
    No, this is incorrect. TA-Lib T3 directly uses the six EMAs with coefficients. For `vFactor = b`:
    *   `k = 2.0 / (optInTimePeriod + 1.0)`
    *   `e1 = EMA(price, k)`
    *   `e2 = EMA(e1, k)`
    *   `e3 = EMA(e2, k)`
    *   `e4 = EMA(e3, k)`
    *   `e5 = EMA(e4, k)`
    *   `e6 = EMA(e5, k)`
    *   `coeff1 = - (b*b*b)`
    *   `coeff2 = 3*(b*b) + 3*(b*b*b)`
    *   `coeff3 = -6*(b*b) - 3*b - 3*(b*b*b)`
    *   `coeff4 = 1 + 3*b + 3*(b*b) + (b*b*b)`
    *   `T3 = coeff1 * e6 + coeff2 * e5 + coeff3 * e4 + coeff4 * e3`

6.  **计算T3值**:
    \[ T3_t = c_1 \cdot e_{6,t} + c_2 \cdot e_{5,t} + c_3 \cdot e_{4,t} + c_4 \cdot e_{3,t} \]
    确保所有 \(e_{k,t}\) 对应同一时刻 \(t\)。

【6. 备注与参数说明】

*   **参数选择**:
    *   `optInTimePeriod` (周期 \(N\))：用于所有内部EMA计算。
    *   `vFactor`: 体积因子，控制平滑程度。此通用MA接口中固定为0.7。值越小，T3越接近EMA；值越大，T3越平滑但可能引入过冲。0.7是常用值。
*   **数据预处理**: 通常使用收盘价。
*   **起始点**: T3需要六次EMA迭代。因此，第一个有效的T3值需要大量的原始数据点进行预热。TA-Lib T3的lookback是 \( (N-1) \cdot 6 \)。
*   **特殊情况**: 如果周期 \(N=1\)，则所有 \(e_k = P_t\)。那么 \(T3_t = (-v^3 + 3v^2+3v^3 -6v^2-3v-3v^3 + 1+3v+3v^2+v^3)P_t = P_t\)。

【关联因子信息结束】===============================================================

【因子信息结束】===============================================================
