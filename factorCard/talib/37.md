【因子信息开始】===============================================================

【因子编号和名称】

因子编号: HTDCPHASE01: 希尔伯特变换瞬时相位 (Hilbert Transform - Dominant Cycle Phase, HT_DCPHASE)

【1. 因子名称详情】

因子1: 希尔伯特变换瞬时相位 (Hilbert Transform - Dominant Cycle Phase, HT_DCPHASE)。该因子通过希尔伯特变换计算输入时间序列（通常是价格）的主导周期相位，用于识别市场周期和潜在的转折点。

【2. 核心公式】

该因子的计算比较复杂，涉及到多个中间步骤。核心思想是利用希尔伯特变换将原始信号分解为同相分量 (In-phase, I) 和正交分量 (Quadrature, Q)，然后基于这些分量估计主导周期及其相位。

最终的瞬时相位 `DCPhase_t` 的计算可以概括为：
1.  对输入价格进行平滑处理。
2.  应用希尔伯特变换器获取同相分量 (I) 和正交分量 (Q) 的多个变体。
3.  根据I、Q分量计算并平滑主导周期 `SmoothPeriod_t`。
4.  使用 `SmoothPeriod_t` 和最近的平滑价格序列计算瞬时相位。

具体步骤中的公式将在后续章节详细展开。

【3. 变量定义】

*   `P_t`: t时刻的输入价格（例如，收盘价）。
*   `SP_t`: t时刻的平滑价格，通过对`P_t`进行4周期加权移动平均得到。
*   `Detrender_t`: t时刻的去趋势价格序列。
*   `I1_t`: t时刻的同相分量（去趋势价格的3周期延迟）。
*   `Q1_t`: t时刻的正交分量（基于`Detrender_t`）。
*   `jI_t`: t时刻`I1_t`的希尔伯特变换。
*   `jQ_t`: t時刻`Q1_t`的希尔伯特变换。
*   `I2_t`: t时刻平滑后的同相分量。
*   `Q2_t`: t时刻平滑后的正交分量。
*   `Re_t`: t时刻用于周期计算的实部。
*   `Im_t`: t时刻用于周期计算的虚部。
*   `Period_t`: t时刻计算出的瞬时周期。
*   `SmoothPeriod_t`: t时刻平滑后的主导周期。
*   `DCPeriod_t`: t时刻用于计算相位的主导周期（`SmoothPeriod_t`取整）。
*   `DCPeriodInt_t`: `DCPeriod_t` 的整数部分。
*   `RealPart_t`: t时刻计算相位用的傅里叶变换实部。
*   `ImagPart_t`: t时刻计算相位用的傅里叶变换虚部。
*   `DCPhase_t`: t时刻的主导周期相位（最终输出）。
*   `a`: 希尔伯特变换器系数，固定为 0.0962。
*   `b`: 希尔伯特变换器系数，固定为 0.5769。

【4. 函数与方法说明】

1.  **4周期加权移动平均 (WMA-4)**:
    用于平滑输入价格。对于t时刻的价格`P_t`，其4周期加权移动平均`SP_t`计算如下：
    `SP_t = (4*P_t + 3*P_{t-1} + 2*P_{t-2} + 1*P_{t-3}) / (4+3+2+1) = 0.1 * (4*P_t + 3*P_{t-1} + 2*P_{t-2} + 1*P_{t-3})`

2.  **希尔伯特变换器组件计算 (Adaptive Filter)**:
    该计算用于从一个输入序列 `X` 生成一个输出序列 `Y`，并根据前一时刻的周期 `PrevPeriod` 进行自适应调整。
    `Y_t = (a * X_t + b * X_{t-2} - b * X_{t-4} - a * X_{t-6}) * (0.075 * PrevPeriod_t + 0.54)`
    其中 `a = 0.0962`，`b = 0.5769`。`PrevPeriod_t` 是上一时刻平滑后的周期 `Period_{t-1}`。
    此滤波器结构用于计算 `Detrender_t` (输入为 `SP_t`), `Q1_t` (输入为 `Detrender_t`), `jI_t` (输入为 `I1_t`), 和 `jQ_t` (输入为 `Q1_t`)。

3.  **指数平滑 (EMA-like smoothing)**:
    公式形式为 `NewValue = alpha * CurrentValue + (1-alpha) * PreviousValue`。
    在此因子中，常用 `alpha = 0.2` (即 `0.2 * Current + 0.8 * Previous`) 或 `alpha = 0.33` (即 `0.33 * Current + 0.67 * Previous`)。

4.  **反正切函数 (atan)**:
    `atan(Y/X)` 用于计算角度。结果通常以弧度为单位，可以通过乘以 `180/π` 转换为角度。
    注意：当X接近0时，需要特殊处理。当X为0时：
    *   若 Y > 0, 角度为 90 度。
    *   若 Y < 0, 角度为 -90 度。
    *   若 Y = 0, 角度未定义或为0。
    通常使用 `atan2(Y,X)` 函数可以更好地处理所有象限和X=0的情况，但此处源码中使用 `atan(Y/X)` 并附加了调整逻辑。

【5. 计算步骤】

因子计算需要一个较长的初始化期（在TA-Lib中为63个数据点，外加一个不确定长度的稳定期）。以下步骤描述了在有足够历史数据后，如何计算每个时刻 `t` 的 `DCPhase_t` 值。

**初始化**:
*   `Period_0 = 0.0`
*   `SmoothPeriod_0 = 0.0`
*   `Re_0 = 0.0`, `Im_0 = 0.0`
*   `I2_0 = 0.0`, `Q2_0 = 0.0`
*   所有希尔伯特变换器的历史缓冲区（如 `detrender`, `Q1`, `jI`, `jQ`的过去6个值）初始化为0。
*   `smoothPrice` 缓冲区（长度50）初始化为0。

**对于每个新的数据点 `P_t` (从`t = lookbackTotal`开始):**

1.  **价格平滑**:
    使用4周期加权移动平均计算平滑价格 `SP_t`:
    `SP_t = 0.1 * (4*P_t + 3*P_{t-1} + 2*P_{t-2} + 1*P_{t-3})`
    将 `SP_t` 存入一个名为 `smoothPrice` 的循环缓冲区中，该缓冲区保留最近50个 `SP` 值。

2.  **计算希尔伯特变换组件**:
    令 `PrevPeriodCalc_t = 0.075 * Period_{t-1} + 0.54` (`Period_{t-1}` 是上一个时间步计算的周期值)。
    *   **计算去趋势价格 (Detrender)**:
        `Detrender_t = (a * SP_t + b * SP_{t-2} - b * SP_{t-4} - a * SP_{t-6}) * PrevPeriodCalc_t`
    *   **计算同相分量 `I1` (滞后3期的Detrender)**:
        `I1_t = Detrender_{t-3}`
    *   **计算正交分量 `Q1` (基于Detrender)**:
        `Q1_t = (a * Detrender_t + b * Detrender_{t-2} - b * Detrender_{t-4} - a * Detrender_{t-6}) * PrevPeriodCalc_t`
    *   **计算 `jI` (I1的希尔伯特变换)**:
        `jI_t = (a * I1_t + b * I1_{t-2} - b * I1_{t-4} - a * I1_{t-6}) * PrevPeriodCalc_t`
    *   **计算 `jQ` (Q1的希尔伯特变换)**:
        `jQ_t = (a * Q1_t + b * Q1_{t-2} - b * Q1_{t-4} - a * Q1_{t-6}) * PrevPeriodCalc_t`

3.  **平滑同相和正交分量 (`I2`, `Q2`)**:
    `I2_t = 0.2 * (I1_t - jQ_t) + 0.8 * I2_{t-1}`
    `Q2_t = 0.2 * (Q1_t + jI_t) + 0.8 * Q2_{t-1}`

4.  **计算周期辅助变量 (`Re`, `Im`)**:
    `Re_t = 0.2 * (I2_t * I2_{t-1} + Q2_t * Q2_{t-1}) + 0.8 * Re_{t-1}`
    `Im_t = 0.2 * (I2_t * Q2_{t-1} - Q2_t * I2_{t-1}) + 0.8 * Im_{t-1}`

5.  **计算瞬时周期 (`Period_t`)**:
    *   如果 `Im_t != 0` 且 `Re_t != 0`:
        `Period_t = 360.0 / (atan(Im_t / Re_t) * (180.0 / \pi))`
    *   否则，`Period_t` 保持 `Period_{t-1}` 的值（或根据具体实现，可能为0或一个默认值）。
    *   **周期值限制与平滑**:
        1.  `Period_t = min(Period_t, 1.5 * Period_{t-1})`
        2.  `Period_t = max(Period_t, 0.67 * Period_{t-1})`
        3.  `Period_t = max(Period_t, 6.0)`
        4.  `Period_t = min(Period_t, 50.0)`
        5.  `Period_t = 0.2 * Period_t + 0.8 * Period_{t-1}` (最终平滑)

6.  **计算平滑主导周期 (`SmoothPeriod_t`)**:
    `SmoothPeriod_t = 0.33 * Period_t + 0.67 * SmoothPeriod_{t-1}`

7.  **计算主导周期相位 (`DCPhase_t`)**:
    *   `DCPeriod_t = SmoothPeriod_t + 0.5`
    *   `DCPeriodInt_t = floor(DCPeriod_t)` (取整数部分)
    *   确保 `DCPeriodInt_t` 不超过 `smoothPrice` 缓冲区的最大容量 (50)。
    *   初始化 `RealPart_t = 0.0`, `ImagPart_t = 0.0`
    *   迭代计算傅里叶分量 (从 `k=0` 到 `DCPeriodInt_t - 1`):
        `angle_k = (k * 2 * \pi) / DCPeriodInt_t` (注意：源码中使用 `constDeg2RadBy360` 实际上是 `2*\pi`，即 `(k * 360_degrees_in_radians) / DCPeriodInt_t`)
        `currentSmoothPrice = smoothPrice` 缓冲区中从最近往前数第 `k` 个值 (`SP_{t-k}`).
        `RealPart_t = RealPart_t + sin(angle_k) * SP_{t-k}`
        `ImagPart_t = ImagPart_t + cos(angle_k) * SP_{t-k}`
    *   **计算初始相位角**:
        如果 `abs(ImagPart_t) > 0.01`:
            `DCPhase_t = atan(RealPart_t / ImagPart_t) * (180.0 / \pi)`
        否则 (如果 `abs(ImagPart_t) <= 0.01`):
            `DCPhase_t` （保持之前的值或根据下面逻辑调整）
            如果 `RealPart_t < 0.0`: `DCPhase_t = DCPhase_{t-1} - 90.0` (此处原文逻辑是直接DCPhase -= 90)
            如果 `RealPart_t > 0.0`: `DCPhase_t = DCPhase_{t-1} + 90.0` (此处原文逻辑是直接DCPhase += 90)
            (注意：当`ImagPart_t`接近0时，`DCPhase`的直接赋值更安全，例如 `DCPhase = (RealPart_t > 0) ? 90.0 : ((RealPart_t < 0)? -90.0 : 0.0);`)
    *   **相位调整**:
        1.  `DCPhase_t = DCPhase_t + 90.0`
        2.  补偿WMA的滞后: `DCPhase_t = DCPhase_t + (360.0 / SmoothPeriod_t)`
        3.  象限调整: 如果 `ImagPart_t < 0.0`, 则 `DCPhase_t = DCPhase_t + 180.0`
        4.  归一化相位角: 如果 `DCPhase_t > 315.0`, 则 `DCPhase_t = DCPhase_t - 360.0`

8.  **更新状态**:
    保存 `I2_t`, `Q2_t`, `Re_t`, `Im_t`, `Period_t`, `SmoothPeriod_t` 等作为下一次计算的 `_{t-1}` 值。
    将 `SP_t` 存入 `smoothPrice` 循环缓冲区的头部，并移除最旧的值。

【6. 备注与参数说明】

*   **参数**:
    *   希尔伯特变换器系数 `a = 0.0962` 和 `b = 0.5769` 是固定的。
    *   各种平滑因子（如0.2, 0.8, 0.33, 0.67）是固定的。
    *   周期值限制范围：最小6，最大50。
*   **窗口期/Lookback**:
    该因子需要大量的初始数据（至少63个周期，加上TA-Lib内部定义的不稳定周期长度）才能产生第一个有效输出。这是因为涉及到多级延迟和反馈回路。
*   **数据预处理**:
    输入数据 `inReal` 通常是价格序列，如每日收盘价。
*   **平滑价格缓冲区**:
    `smoothPrice` 缓冲区大小固定为50，这意味着计算相位时使用的最大周期 `DCPeriodInt_t` 也被有效地限制在50以内（或者说，傅里叶分析的最大回溯长度）。
*   **相位解释**:
    `DCPhase_t` 的值通常在特定范围内（例如，经过归一化后可能在 -45 到 315 度之间，或调整到其他常用范围）。相位的变化可以指示市场周期的进展。例如，相位从0度到360度（或等效范围）的变化代表一个完整的主导周期。

【因子信息结束】===============================================================