【因子信息开始】===============================================================

【因子编号和名称】

因子编号: PPO001
因子中文名称: 百分比价格振荡器 (Percentage Price Oscillator, PPO)

【1. 因子名称详情】

百分比价格振荡器 (Percentage Price Oscillator, PPO) 是一个动量振荡器，用于衡量两个移动平均线之间的差异，并将该差异表示为较长周期移动平均线的百分比。

【2. 核心公式】

PPO 的计算公式如下：

<tex>$PPO_t = \left( \frac{MA_{fast,t}(Price, N_{fast}) - MA_{slow,t}(Price, N_{slow})}{MA_{slow,t}(Price, N_{slow})} \right) \times 100\%$</tex>

其中：
*   <tex>$MA_{fast,t}(Price, N_{fast})$</tex> 是价格序列在时间点 <tex>$t$</tex> 的短期移动平均值。
*   <tex>$MA_{slow,t}(Price, N_{slow})$</tex> 是价格序列在时间点 <tex>$t$</tex> 的长期移动平均值。

【3. 变量定义】

*   <tex>$PPO_t$</tex>: 时间点 <tex>$t$</tex> 的百分比价格振荡器值。
*   <tex>$Price_i$</tex>: 时间点 <tex>$i$</tex> 的输入价格序列，通常为收盘价。
*   <tex>$N_{fast}$</tex>: 短期移动平均线的计算周期（例如：12天）。
*   <tex>$N_{slow}$</tex>: 长期移动平均线的计算周期（例如：26天）。
*   <tex>$MA_{fast,t}(Price, N_{fast})$</tex>: 在时间点 <tex>$t$</tex>，使用周期 <tex>$N_{fast}$</tex> 计算得到的关于 <tex>$Price$</tex> 序列的短期移动平均值。
*   <tex>$MA_{slow,t}(Price, N_{slow})$</tex>: 在时间点 <tex>$t$</tex>，使用周期 <tex>$N_{slow}$</tex> 计算得到的关于 <tex>$Price$</tex> 序列的长期移动平均值。

【4. 函数与方法说明】

计算 PPO 因子需要使用移动平均线 (Moving Average, MA)。移动平均线的类型可以有多种，常见的包括：

1.  **简单移动平均 (Simple Moving Average, SMA)**:
    对于给定的价格序列 <tex>$Price$</tex> 和周期 <tex>$N$</tex>，在时间点 <tex>$t$</tex> 的 SMA 计算如下：
    <tex>$SMA_t(Price, N) = \frac{1}{N} \sum_{i=0}^{N-1} Price_{t-i}$</tex>
    即，当前及之前 <tex>$N-1$</tex> 个价格数据的算术平均值。

2.  **指数移动平均 (Exponential Moving Average, EMA)**:
    对于给定的价格序列 <tex>$Price$</tex> 和周期 <tex>$N$</tex>，在时间点 <tex>$t$</tex> 的 EMA 计算如下：
    首先计算平滑系数 <tex>$\alpha$</tex> (也常表示为 <tex>$k$</tex>):
    <tex>$\alpha = \frac{2}{N+1}$</tex>
    然后，EMA 的计算公式为：
    <tex>$EMA_t(Price, N) = \alpha \times Price_t + (1-\alpha) \times EMA_{t-1}(Price, N)$</tex>
    对于第一个 EMA 值 (<tex>$EMA_0$</tex>)，通常使用该周期的 SMA 值作为初始值，即 <tex>$EMA_0 = SMA_0(Price, N)$</tex>。

根据源码，PPO 的计算可以采用多种不同类型的移动平均线 (optInMAType 参数指定)，如 SMA, EMA, WMA (加权移动平均), DEMA (双指数移动平均), TEMA (三指数移动平均), TRIMA (三角移动平均), KAMA (考夫曼自适应移动平均), MAMA (MESA 自适应移动平均), T3 (三重指数移动平均)。具体使用哪种 MA 类型，将影响 <tex>$MA_{fast,t}$</tex> 和 <tex>$MA_{slow,t}$</tex> 的计算方式。在实际应用中，EMA 是较常用的一种。

【5. 计算步骤】

1.  **数据准备**:
    获取历史价格序列 <tex>$Price = \{Price_0, Price_1, ..., Price_k\}$</tex>，通常为收盘价。

2.  **参数设定**:
    *   设定短期移动平均周期 <tex>$N_{fast}$</tex> (例如：12)。
    *   设定长期移动平均周期 <tex>$N_{slow}$</tex> (例如：26)。
    *   选择移动平均线的类型 (例如：EMA)。

3.  **计算短期移动平均线 (<tex>$MA_{fast}$</tex> )**:
    根据选择的 MA 类型和周期 <tex>$N_{fast}$</tex>，对价格序列 <tex>$Price$</tex> 计算得到短期移动平均线序列 <tex>$MA_{fast} = \{MA_{fast, N_{fast}-1}, MA_{fast, N_{fast}}, ..., MA_{fast, k}\}$</tex>。
    例如，如果使用 EMA：
    *   计算平滑系数 <tex>$\alpha_{fast} = \frac{2}{N_{fast}+1}$</tex>。
    *   计算初始 <tex>$EMA_{fast}$</tex>：通常取前 <tex>$N_{fast}$</tex> 个价格的 SMA 作为第一个 <tex>$EMA_{fast}$</tex> 值。
    *   对于后续的每个时间点 <tex>$t$</tex> (<tex>$t \ge N_{fast}$</tex>)：<tex>$EMA_{fast,t} = \alpha_{fast} \times Price_t + (1-\alpha_{fast}) \times EMA_{fast,t-1}$</tex>。

4.  **计算长期移动平均线 (<tex>$MA_{slow}$</tex> )**:
    根据选择的 MA 类型和周期 <tex>$N_{slow}$</tex>，对价格序列 <tex>$Price$</tex> 计算得到长期移动平均线序列 <tex>$MA_{slow} = \{MA_{slow, N_{slow}-1}, MA_{slow, N_{slow}}, ..., MA_{slow, k}\}$</tex>。
    例如，如果使用 EMA：
    *   计算平滑系数 <tex>$\alpha_{slow} = \frac{2}{N_{slow}+1}$</tex>。
    *   计算初始 <tex>$EMA_{slow}$</tex>：通常取前 <tex>$N_{slow}$</tex> 个价格的 SMA 作为第一个 <tex>$EMA_{slow}$</tex> 值。
    *   对于后续的每个时间点 <tex>$t$</tex> (<tex>$t \ge N_{slow}$</tex>)：<tex>$EMA_{slow,t} = \alpha_{slow} \times Price_t + (1-\alpha_{slow}) \times EMA_{slow,t-1}$</tex>。

5.  **计算 PPO 值**:
    对于每个有足够数据计算出 <tex>$MA_{fast,t}$</tex> 和 <tex>$MA_{slow,t}$</tex> 的时间点 <tex>$t$</tex> (即 <tex>$t \ge max(N_{fast}, N_{slow})-1$</tex>，更准确地说，是当两条MA线都有有效值时，通常是从第 <tex>$N_{slow}-1$</tex> 个周期开始，假设 <tex>$N_{slow} > N_{fast}$</tex>)：
    *   如果 <tex>$MA_{slow,t} \neq 0$</tex>:
        <tex>$PPO_t = \left( \frac{MA_{fast,t} - MA_{slow,t}}{MA_{slow,t}} \right) \times 100\%$</tex>
    *   如果 <tex>$MA_{slow,t} = 0$</tex>:
        为了避免除以零错误，此时 <tex>$PPO_t$</tex> 通常被定义为 0，或者视为无定义/无效值。在实践中，如果价格非负，MA值通常也不会为0，除非所有历史价格都为0。源码中的实现倾向于：如果慢速MA为0，而快速MA不为0，结果可能为正负无穷大或一个极大的数；如果两者都为0，结果为0。一个稳健的处理方式是将此时的PPO值设为0或前一个有效值。

【6. 备注与参数说明】

*   **参数选择**:
    *   <tex>$N_{fast}$</tex> (optInFastPeriod): 短期MA周期。源码中默认值为12，取值范围为2至100000。
    *   <tex>$N_{slow}$</tex> (optInSlowPeriod): 长期MA周期。源码中默认值为26，取值范围为2至100000。通常 <tex>$N_{slow} > N_{fast}$</tex>。
    *   <tex>$optInMAType$</tex>: 移动平均线的类型。源码中默认为 SMA (Simple Moving Average)，但支持多种类型 (如 EMA, WMA, DEMA, TEMA 等)。EMA 是 PPO 和 MACD 指标中非常常用的MA类型。

*   **起始期 (Lookback Period)**:
    PPO 的计算需要足够的前期数据来初始化两条移动平均线。因此，PPO 序列的起始点会晚于原始价格序列的起始点。所需的最小数据量由较长的移动平均周期 (<tex>$N_{slow}$</tex>) 以及所选 MA 类型的特性决定。例如，对于 SMA，至少需要 <tex>$N_{slow}$</tex> 个数据点才能计算出第一个 <tex>$MA_{slow}$</tex> 值，从而计算出第一个 PPO 值。

*   **与 MACD 的关系**:
    PPO 与 MACD (Moving Average Convergence Divergence) 指标非常相似。MACD 计算的是两条指数移动平均线之间的绝对差值 (<tex>$MACD = EMA_{fast} - EMA_{slow}$</tex>)。而 PPO 将此差值表示为慢速（长期）EMA 的百分比，这使得 PPO 可以在不同价格水平的证券之间进行更有效的比较，因为它对价格水平进行了标准化。

*   **数据预处理**:
    输入的价格数据应连续且无缺失值。如果存在缺失值，应进行插值或剔除处理。

*   **应用**:
    PPO 可用于识别趋势方向、趋势强度以及潜在的买卖信号（例如，通过观察 PPO 线与信号线的交叉、PPO 与价格的背离等）。

【因子信息结束】===============================================================