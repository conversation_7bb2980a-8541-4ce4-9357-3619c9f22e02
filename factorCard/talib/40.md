【因子信息开始】===============================================================

【因子编号和名称】
因子编号: HTSINE_001: 希尔伯特变换正弦波 (Hilbert Transform SineWave, HT_SINE)

【1. 因子名称详情】
因子1: 希尔伯特变换正弦波 (Hilbert Transform SineWave, HT_SINE)
这因子通过希尔伯特变换分析输入价格序列，提取市场周期信息，并将其表示为正弦波形态。

【2. 核心公式】
对于每个时间点 t:
    HT_SINE_t = sin(DCPhase_t * (π/180))

【3. 变量定义】
• P_t: 时间点 t 的输入价格（例如，收盘价）。
• SP_t: 时间点 t 的平滑价格，通过对 P_t 应用4周期加权移动平均得到。
• smoothPriceBuffer: 存储最近 N_smooth (例如50) 个周期的 SP_t 值。
• a, b: 希尔伯特滤波器的系数，通常 a = 0.0962, b = 0.5769。
• detrender_t, Q1_t, I1_t, jI_t, jQ_t, I2_t, Q2_t: 分别为经过希尔伯特滤波器和相应平滑处理后的各阶段变量。
• Re_t, Im_t: 用于周期计算的复数信号的实部和虚部。
• period_t, smoothPeriod_t: 分别为瞬时周期和平滑周期。
• DCPeriodInt_t: 取整后的主导周期长度。
• RealPartSum_t, ImagPartSum_t: 用于计算主导周期相位的傅里叶分量累加值。
• DCPhase_t: 主导周期相位（角度制）。
• π/180: 度转弧度的转换因子。

【4. 函数与方法说明】
1. 4周期加权移动平均 (4-Period WMA):
   SP_t = 0.1 * (4*P_t + 3*P_(t-1) + 2*P_(t-2) + P_(t-3))
2. 希尔伯特滤波器 (Hilbert Filter Stage, HF):
   Y_t = (a * X_t) + (b * S_X[prev_idx])，并更新状态 S_X[current_idx] = X_t - Y_t
   (该滤波器用于 detrender_t, Q1_t, jI_t, jQ_t 的计算)
3. 指数平滑 (Exponential Smoothing):
   V_t = α * CurrentValue + (1-α) * V_(t-1)  (例如，α=0.2 用于 I2, Q2, Re, Im, period; α=0.33 用于 smoothPeriod)
4. 反正切函数 (Arctangent):
   angle = atan2(Y, X) 或者 atan(Y/X)（并根据 X, Y 确定象限）

【5. 计算步骤】
1. 初始化阶段：初始化各状态变量和状态缓冲区，预填充平滑价格数据。
2. 对每个时间点 t:
   a. 计算平滑价格 SP_t：0.1*(4*P_t + 3*P_(t-1) + 2*P_(t-2) + P_(t-3)) 并存入 smoothPriceBuffer。
   b. 应用希尔伯特滤波器：计算 detrender_t, Q1_t, 获取 I1_t = detrender_(t-3)，并计算 jI_t, jQ_t。
   c. 计算平滑后的同相与正交分量 I2_t, Q2_t：
        Q2_t = 0.2*(Q1_t + jI_t) + 0.8*Q2_(t-1)
        I2_t = 0.2*(I1_t - jQ_t) + 0.8*I2_(t-1)
   d. 计算复平面分量 Re_t, Im_t：
        Re_t = 0.2*(I2_t*I2_(t-1) + Q2_t*Q2_(t-1)) + 0.8*Re_(t-1)
        Im_t = 0.2*(I2_t*Q2_(t-1) - Q2_t*I2_(t-1)) + 0.8*Im_(t-1)
   e. 计算瞬时周期 period_t：利用 Re_t, Im_t 计算 period_raw_t，再经过约束和平滑得到 period_t
   f. 计算平滑周期 smoothPeriod_t：smoothPeriod_t = 0.33*period_t + 0.67*smoothPeriod_(t-1)
   g. 计算 DCPeriodInt_t：DCPeriodInt_t = floor(smoothPeriod_t + 0.5) 并确保 ≥ 1
   h. 计算傅里叶分量 RealPartSum_t 与 ImagPartSum_t：对 k 从 0 到 DCPeriodInt_t-1 进行累加
   i. 计算主导周期相位 DCPhase_t：
       若 |ImagPartSum_t| > 0.01，则 DCPhase_t = atan(RealPartSum_t/ImagPartSum_t)*(180/π)；否则根据 RealPartSum_t 的符号设定 ±90°；随后进行相位调整：+90°（基于初始设置）、+ (360/ smoothPeriod_t)（WMA延迟补偿）、若 ImagPartSum_t < 0 则再加 180°，最后进行归一化（若 DCPhase_t > 315° 则减 360°）
   j. 计算因子值 HT_SINE_t = sin(DCPhase_t * (π/180))

【6. 备注与参数说明】
• 参数：希尔伯特滤波器系数 a = 0.0962, b = 0.5769；平滑价格 WMA 周期为4；周期约束：最小6，最大50；平滑因子：0.2（用于 I2, Q2, Re, Im, period）与 0.33（用于 smoothPeriod）；LeadSine 相位提前量 45°（适用于另一个因子）。
• 数据预处理：需足够历史数据（最小回溯约63个周期）以初始化所有滤波器和平滑器。
• 应用：用于识别市场周期和潜在交易信号，HT_SINE 用于观察周期性波动，其值范围在 -1 到 +1之间。



【7. 关联因子补充说明】

【关联因子信息开始】===============================================================

【因子编号和名称】
因子编号: HTSINE_001: 希尔伯特变换正弦波 (Hilbert Transform SineWave, HT_SINE)
因子编号: HTSINE_002: 希尔伯特变换领先正弦波 (Hilbert Transform Lead SineWave, HT_LEADSINE)

【1. 因子名称详情】
因子1: 希尔伯特变换正弦波 (Hilbert Transform SineWave, HT_SINE)
因子2: 希尔伯特变换领先正弦波 (Hilbert Transform Lead SineWave, HT_LEADSINE)

这两个因子通过希尔伯特变换分析输入价格序列，提取市场周期信息，并将其表示为正弦波形态。领先正弦波是正弦波基础上相位提前45度的版本，用于预测潜在的周期转折。

【2. 核心公式】
对于每个时间点 $t$:
1.  **希尔伯特变换正弦波 (HT_SINE):**
    $ \text{HT_SINE}_t = \sin(\text{DCPhase}_t \cdot \frac{\pi}{180}) $

2.  **希尔伯特变换领先正弦波 (HT_LEADSINE):**
    $ \text{HT_LEADSINE}_t = \sin((\text{DCPhase}_t + 45^\circ) \cdot \frac{\pi}{180}) $

其中，$\text{DCPhase}_t$ 是在时间点 $t$ 计算得到的主导周期相位（Dominant Cycle Phase）。核心在于如何计算 $\text{DCPhase}_t$。

【3. 变量定义】
*   $P_t$: 时间点 $t$ 的输入价格（例如，收盘价）。
*   $SP_t$: 时间点 $t$ 的平滑价格，通过对 $P_t$ 应用一个4周期加权移动平均得到。
*   $\text{smoothPriceBuffer}$: 一个长度为 $N_{smooth}$ (例如50) 的循环缓冲区，存储最近 $N_{smooth}$ 个周期的 $SP_t$ 值。
*   $a, b$: 希尔伯特滤波器的系数，通常 $a = 0.0962$, $b = 0.5769$。
*   $\text{detrender}_t$: 时间点 $t$ 的经希尔伯特滤波器处理后的趋势移除分量。
*   $\text{Q1}_t$: 时间点 $t$ 的经希尔伯特滤波器处理后的 $\text{detrender}_t$ 的正交分量。
*   $I1_t$: 时间点 $t$ 的同相分量，等于 $\text{detrender}_{t-3}$。
*   $jI_t$: 时间点 $t$ 的经希尔伯特滤波器处理后的 $I1_t$ 的正交分量。
*   $jQ_t$: 时间点 $t$ 的经希尔伯特滤波器处理后的 $Q1_t$ 的正交分量。
*   $I2_t, Q2_t$: 时间点 $t$ 的进一步平滑后的同相和正交分量。
*   $Re_t, Im_t$: 时间点 $t$ 的用于周期计算的复数信号的实部和虚部。
*   $\text{period}_t$: 时间点 $t$ 计算得到的瞬时周期长度。
*   $\text{smoothPeriod}_t$: 时间点 $t$ 平滑后的周期长度。
*   $\text{DCPeriodInt}_t$: 时间点 $t$ 取整后的主导周期长度，用于后续傅里叶变换型计算。
*   $\text{RealPartSum}_t, \text{ImagPartSum}_t$: 时间点 $t$ 计算主导周期相位时，累加的正弦和余弦加权平滑价格。
*   $\text{DCPhase}_t$: 时间点 $t$ 计算得到的主导周期相位 (角度制)。
*   $\frac{\pi}{180}$: 度转弧度的转换因子。

【4. 函数与方法说明】
1.  **4周期加权移动平均 (4-Period WMA):**
    用于计算平滑价格 $SP_t$:
    $ SP_t = \frac{4 \cdot P_t + 3 \cdot P_{t-1} + 2 \cdot P_{t-2} + 1 \cdot P_{t-3}}{4+3+2+1} = 0.1 \cdot (4 P_t + 3 P_{t-1} + 2 P_{t-2} + P_{t-3}) $

2.  **希尔伯特滤波器 (Hilbert Filter Stage, HF):**
    这是一种数字滤波器，应用于一个输入序列。对于输入 $X_t$ 和一个内部状态（循环）缓冲 $S_X$，滤波器的输出 $Y_t$ 和新的状态 $S_X[\text{current\_idx}]$ 计算如下:
    $ Y_t = (a \cdot X_t) + (b \cdot S_X[\text{prev\_idx}]) $
    $ S_X[\text{current\_idx}] = X_t - Y_t $
    其中 `prev_idx` 指向状态缓冲区中先前的值。该滤波器用于计算 $\text{detrender}_t, Q1_t, jI_t, jQ_t$。每个变量有其自己的状态缓冲。

3.  **指数平滑 (Exponential Smoothing):**
    用于平滑计算出的值，例如 $I2_t, Q2_t, Re_t, Im_t, \text{period}_t, \text{smoothPeriod}_t$。
    通用公式: $ V_t = \alpha \cdot \text{CurrentValue} + (1-\alpha) \cdot V_{t-1} $
    其中 $\alpha$ 是平滑因子。例如，对于 $I2_t, Q2_t, Re_t, Im_t, \text{period}_t$，$\alpha=0.2$。对于 $\text{smoothPeriod}_t$，$\alpha=0.33$。

4.  **反正切函数 (Arctangent, atan2 or atan with adjustments):**
    用于从复数信号的实部和虚部计算相位或周期相关的角度。
    $\text{angle} = \operatorname{atan2}(Y, X)$ (给出 $Y/X$ 的反正切，并根据 $X,Y$ 的符号确定正确象限)。
    或者 $\text{angle} = \operatorname{atan}(Y/X)$，然后根据 $X,Y$ 的符号进行调整。

【5. 计算步骤】
**初始化阶段:**
*   初始化所有状态变量为0，例如 $I2_{0}, Q2_{0}, Re_{0}, Im_{0}, \text{period}_{0}, \text{smoothPeriod}_{0}$。
*   初始化希尔伯特滤波器状态缓冲。
*   预填充平滑价格计算所需的初始价格数据。计算所需的最小数据回溯期约为63个周期，加上不稳定期的额外数据。

**对于每个时间点 $t$ (从有足够历史数据开始):**

1.  **计算平滑价格 ($SP_t$):**
    使用4周期WMA公式计算当前价格 $P_t$ 的平滑值 $SP_t$。
    $ SP_t = 0.1 \cdot (4 P_t + 3 P_{t-1} + 2 P_{t-2} + P_{t-3}) $
    将 $SP_t$ 存入 $\text{smoothPriceBuffer}$。

2.  **应用希尔伯特滤波器计算核心分量:**
    *   $\text{detrender}_t = \text{HF}(SP_t, \text{detrender_state_buffer})$
    *   $\text{Q1}_t = \text{HF}(\text{detrender}_t, \text{Q1_state_buffer})$
    *   $I1_t = \text{detrender}_{t-3}$ (取3个周期前的 $\text{detrender}$ 值)
    *   $jI_t = \text{HF}(I1_t, \text{jI_state_buffer})$
    *   $jQ_t = \text{HF}(Q1_t, \text{jQ_state_buffer})$

3.  **计算平滑后的同相和正交分量 ($I2_t, Q2_t$):**
    *   $Q2_t = 0.2 \cdot (\text{Q1}_t + jI_t) + 0.8 \cdot Q2_{t-1}$
    *   $I2_t = 0.2 \cdot (I1_t - jQ_t) + 0.8 \cdot I2_{t-1}$

4.  **计算周期相关的复平面分量 ($Re_t, Im_t$):**
    *   $Re_t = 0.2 \cdot (I2_t \cdot I2_{t-1} + Q2_t \cdot Q2_{t-1}) + 0.8 \cdot Re_{t-1}$
    *   $Im_t = 0.2 \cdot (I2_t \cdot Q2_{t-1} - Q2_t \cdot I2_{t-1}) + 0.8 \cdot Im_{t-1}$

5.  **计算瞬时周期 ($\text{period}_t$):**
    *   $\text{period\_raw}_t = 0$
    *   If $Im_t \neq 0$ and $Re_t \neq 0$:
        $\text{period\_raw}_t = \frac{360^\circ}{\operatorname{atan}(Im_t / Re_t) \cdot (180^\circ/\pi)}$
    *   Else (如果 $Im_t$ 或 $Re_t$ 为零), $\text{period\_raw}_t$ 沿用 $\text{period}_{t-1}$ 或保持为0 (取决于具体实现细节)。
    *   **周期平滑与约束1:**
        If $\text{period\_raw}_t > 1.5 \cdot \text{period}_{t-1}$ (且 $\text{period}_{t-1} \neq 0$), then $\text{period\_raw}_t = 1.5 \cdot \text{period}_{t-1}$.
        If $\text{period\_raw}_t < 0.67 \cdot \text{period}_{t-1}$ (且 $\text{period}_{t-1} \neq 0$), then $\text{period\_raw}_t = 0.67 \cdot \text{period}_{t-1}$.
    *   **周期范围约束:**
        If $\text{period\_raw}_t < 6$, then $\text{period\_raw}_t = 6$.
        If $\text{period\_raw}_t > 50$, then $\text{period\_raw}_t = 50$.
    *   **周期平滑2:**
        $\text{period}_t = 0.2 \cdot \text{period\_raw}_t + 0.8 \cdot \text{period}_{t-1}$

6.  **计算平滑周期 ($\text{smoothPeriod}_t$):**
    *   $\text{smoothPeriod}_t = 0.33 \cdot \text{period}_t + 0.67 \cdot \text{smoothPeriod}_{t-1}$

7.  **计算主导周期整数长度 ($\text{DCPeriodInt}_t$):**
    *   $\text{DCPeriodInt}_t = \text{floor}(\text{smoothPeriod}_t + 0.5)$ (即四舍五入到最近整数)
    *   确保 $\text{DCPeriodInt}_t \ge 1$ 以避免除零错误。

8.  **计算主导周期相位的傅里叶分量 ($\text{RealPartSum}_t, \text{ImagPartSum}_t$):**
    *   $\text{RealPartSum}_t = 0$
    *   $\text{ImagPartSum}_t = 0$
    *   For $k = 0$ to $\text{DCPeriodInt}_t - 1$:
        *   $\text{angle}_k = (k \cdot 2\pi) / \text{DCPeriodInt}_t$
        *   $\text{price\_component} = \text{smoothPriceBuffer}[t-k]$ (从平滑价格缓冲区中取值)
        *   $\text{RealPartSum}_t = \text{RealPartSum}_t + \sin(\text{angle}_k) \cdot \text{price\_component}$
        *   $\text{ImagPartSum}_t = \text{ImagPartSum}_t + \cos(\text{angle}_k) \cdot \text{price\_component}$

9.  **计算主导周期相位 ($\text{DCPhase}_t$):**
    *   初始化 $\text{DCPhase}_t$ (例如，可以沿用 $\text{DCPhase}_{t-1}$ 或特定值)。
    *   If $|\text{ImagPartSum}_t| > 0.01$:
        $\text{DCPhase}_t = \operatorname{atan}(\text{RealPartSum}_t / \text{ImagPartSum}_t) \cdot (180^\circ/\pi)$
    *   Else if $|\text{ImagPartSum}_t| \le 0.01$:
        If $\text{RealPartSum}_t < 0.0$, $\text{DCPhase}_t = -90.0^\circ$.
        If $\text{RealPartSum}_t > 0.0$, $\text{DCPhase}_t = 90.0^\circ$.
        (如果两者都接近零，$\text{DCPhase}_t$ 可能保持不变或为0)。
    *   **相位调整1:**
        $\text{DCPhase}_t = \text{DCPhase}_t + 90.0^\circ$
    *   **相位调整2 (WMA延迟补偿):**
        If $\text{smoothPeriod}_t \neq 0$:
        $\text{DCPhase}_t = \text{DCPhase}_t + (360.0^\circ / \text{smoothPeriod}_t)$
    *   **相位调整3 (象限规范化):**
        If $\text{ImagPartSum}_t < 0.0$:
        $\text{DCPhase}_t = \text{DCPhase}_t + 180.0^\circ$
    *   **相位归一化 (确保在特定范围内):**
        If $\text{DCPhase}_t > 315.0^\circ$:
        $\text{DCPhase}_t = \text{DCPhase}_t - 360.0^\circ$

10. **计算因子值:**
    *   **HT_SINE Faktor:**
        $ \text{HT_SINE}_t = \sin(\text{DCPhase}_t \cdot \frac{\pi}{180}) $
    *   **HT_LEADSINE Faktor:**
        $ \text{HT_LEADSINE}_t = \sin((\text{DCPhase}_t + 45^\circ) \cdot \frac{\pi}{180}) $

【6. 备注与参数说明】
*   **参数:**
    *   希尔伯特滤波器系数: $a=0.0962$, $b=0.5769$。这些是Ehlers提出的标准值。
    *   平滑价格WMA周期: 4周期。
    *   周期约束: 最小周期6，最大周期50。
    *   平滑因子: 在多个EMA类型的平滑步骤中使用，例如0.2 (用于 $I2, Q2, Re, Im, \text{period}$), 0.33 (用于 $\text{smoothPeriod}$)。
    *   相位提前量 (LeadSine): $45^\circ$。
*   **数据预处理:**
    *   输入数据通常是价格序列（如每日收盘价）。
    *   需要足够的历史数据来初始化所有滤波器和平滑器，回溯期通常为63个数据点，另加TA-Lib中称为“不稳定周期”的部分，以确保输出稳定。
*   **计算细节:**
    *   希尔伯特滤波器的状态变量通常存储在大小为3的循环缓冲区中。
    *   平滑价格 $SP_t$ 存储在大小为 $N_{smooth}=50$ 的循环缓冲区中，用于计算主导周期相位的傅里叶分量。
    *   代码中区分奇偶数日期处理 $I1$（即 $\text{detrender}_{t-3}$）的传递，是为了优化和精确地获取3周期前的信号状态。从概念上讲，就是使用3周期前的 $\text{detrender}$ 值。
*   **应用:**
    *   这两个因子可用于识别市场周期和潜在的交易信号。当HT_SINE从谷底上升或从峰顶下降时，可能预示趋势变化。HT_LEADSINE由于其相位提前，可以提供更早的信号，但可能伴随更多假信号。
    *   因子值的范围在 -1 到 +1之间。

【关联因子信息结束】===============================================================

【因子信息结束】===============================================================