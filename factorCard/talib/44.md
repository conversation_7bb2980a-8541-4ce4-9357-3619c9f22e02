【因子信息开始】===============================================================

【因子编号和名称】

因子编号: IMI001
因子中文名称: 日内动量指标 (Intraday Momentum Index, IMI)

【1. 因子名称详情】

因子1: 日内动量指标 (Intraday Momentum Index, IMI)。该指标衡量在给定周期内，价格上涨日的日内收益总和在总的日内价格变动（上涨日的日内收益与下跌日的日内损失之和）中所占的百分比。它类似于相对强弱指数(RSI)，但关注的是日内（开盘价到收盘价）的动量，而非日间（昨日收盘价到今日收盘价）的动量。

【2. 核心公式】

给定计算周期 \(N\)，在时刻 \(t\) 的日内动量指标 \(IMI_t\) 计算如下：

\[ IMI_t = 100 \times \frac{\text{GainSum}_N(t)}{\text{GainSum}_N(t) + \text{LossSum}_N(t)} \]

其中：
\[ \text{GainSum}_N(t) = \sum_{i=t-N+1}^{t} \text{IntradayGain}_i \]
\[ \text{LossSum}_N(t) = \sum_{i=t-N+1}^{t} \text{IntradayLoss}_i \]

对于每一天 \(i\):
\[ \text{IntradayGain}_i = \begin{cases} C_i - O_i & \text{if } C_i > O_i \\ 0 & \text{if } C_i \le O_i \end{cases} \]
\[ \text{IntradayLoss}_i = \begin{cases} O_i - C_i & \text{if } O_i > C_i \\ 0 & \text{if } O_i \le C_i \end{cases} \]

特别地：
\begin{itemize}
    \item 如果 \( \text{GainSum}_N(t) + \text{LossSum}_N(t) = 0 \) (即窗口期内所有交易日的开盘价均等于收盘价)，则 \( IMI_t \) 可定义为 50。
    \item 如果 \( \text{GainSum}_N(t) > 0 \) 且 \( \text{LossSum}_N(t) = 0 \)，则 \( IMI_t = 100 \)。
    \item 如果 \( \text{GainSum}_N(t) = 0 \) 且 \( \text{LossSum}_N(t) > 0 \)，则 \( IMI_t = 0 \)。
\end{itemize}

【3. 变量定义】
\begin{itemize}
    \item \(IMI_t\): 时刻 \(t\) (通常指第 \(t\) 天) 的日内动量指标值。
    \item \(N\): 计算 \(IMI\) 的时间窗口长度（周期数），例如14天。
    \item \(O_i\): 第 \(i\) 天的开盘价。
    \item \(C_i\): 第 \(i\) 天的收盘价。
    \item \(\text{IntradayGain}_i\): 第 \(i\) 天的日内上涨幅度。如果当天收盘价高于开盘价，则为两者之差；否则为0。
    \item \(\text{IntradayLoss}_i\): 第 \(i\) 天的日内下跌幅度（取正值）。如果当天开盘价高于收盘价，则为两者之差；否则为0。
    \item \(\text{GainSum}_N(t)\): 在从 \(t-N+1\) 到 \(t\) 的 \(N\) 天窗口期内，所有 \(\text{IntradayGain}_i\) 的总和。
    \item \(\text{LossSum}_N(t)\): 在从 \(t-N+1\) 到 \(t\) 的 \(N\) 天窗口期内，所有 \(\text{IntradayLoss}_i\) 的总和。
\end{itemize}

【4. 函数与方法说明】
\begin{itemize}
    \item \(\sum_{i=t-N+1}^{t} (\cdot)\): 表示对窗口期内（从第 \(t-N+1\) 天到第 \(t\) 天，共 \(N\) 天）的相应数值进行求和。
    \item 条件判断: 用于根据开盘价和收盘价的关系确定 \(\text{IntradayGain}_i\) 和 \(\text{IntradayLoss}_i\) 的值。
\end{itemize}

【5. 计算步骤】
1.  **数据准备**:
    获取计算所需时间序列的每日开盘价 (\(O_i\)) 和收盘价 (\(C_i\))。
    确定参数 \(N\) (时间窗口长度)，例如 \(N=14\)。

2.  **计算每日日内涨跌幅**:
    对于历史数据中的每一天 \(i\):
    *   如果 \(C_i > O_i\)，则该日的日内上涨幅度 \(\text{IntradayGain}_i = C_i - O_i\)，日内下跌幅度 \(\text{IntradayLoss}_i = 0\)。
    *   如果 \(C_i \le O_i\)，则该日的日内上涨幅度 \(\text{IntradayGain}_i = 0\)，日内下跌幅度 \(\text{IntradayLoss}_i = O_i - C_i\)。 (注意这里 \(O_i-C_i\) 已经是非负值)

3.  **计算日内动量指标 (\(IMI_t\))**:
    对于能够形成一个完整窗口期 \(N\) 的每个交易日 \(t\) (即从第 \(N\) 个数据点开始):
    a.  **确定计算窗口**: 选取从第 \(t-N+1\) 天到第 \(t\) 天的 \(N\) 天数据。
    b.  **计算窗口内总日内涨幅 (\(\text{GainSum}_N(t)\))**:
        将选定窗口期内所有天的 \(\text{IntradayGain}_i\) 相加：
        \[ \text{GainSum}_N(t) = \sum_{i=t-N+1}^{t} \text{IntradayGain}_i \]
    c.  **计算窗口内总日内跌幅 (\(\text{LossSum}_N(t)\))**:
        将选定窗口期内所有天的 \(\text{IntradayLoss}_i\) 相加：
        \[ \text{LossSum}_N(t) = \sum_{i=t-N+1}^{t} \text{IntradayLoss}_i \]
    d.  **计算 \(IMI_t\)**:
        *   计算分母 \(D = \text{GainSum}_N(t) + \text{LossSum}_N(t)\)。
        *   如果 \(D > 0\)，则 \(IMI_t = 100 \times \frac{\text{GainSum}_N(t)}{D}\)。
        *   如果 \(D = 0\) (这意味着在过去的N天中，每天的开盘价都等于收盘价)，则 \(IMI_t\) 可以设定为一个中性值，例如50。

4.  **输出**:
    得到每个计算点 \(t\) 的 \(IMI_t\) 值。注意，前 \(N-1\) 个数据点由于无法形成完整的长度为 \(N\) 的窗口，将不会产生 \(IMI\) 值。

【6. 备注与参数说明】
*   **时间周期 (N)**: 这是IMI计算中的核心参数，决定了回顾的窗口长度。TALib中默认值为14。用户可以根据不同市场和交易策略的需要调整此参数。较短的周期对价格变化更敏感，较长的周期则更平滑。
*   **指标解释**: IMI的取值范围是0到100。
    *   当IMI接近100时，表示在考察期内，日内上涨动能远大于日内下跌动能，市场可能处于强势或超买状态。
    *   当IMI接近0时，表示在考察期内，日内下跌动能远大于日内上涨动能，市场可能处于弱势或超卖状态。
    *   IMI值接近50时，表示日内上涨和下跌动能相对均衡。
*   **数据预处理**: 确保输入的开盘价和收盘价数据是准确且对应同一交易日的。无需其他特殊预处理。
*   **与RSI的区别**: IMI与RSI的核心思想相似，都是衡量一方力量在总力量中的占比。主要区别在于RSI通常基于日间收盘价的变动（\(C_t\) vs \(C_{t-1}\)），而IMI基于日内开盘价到收盘价的变动（\(C_t\) vs \(O_t\)）。

【因子信息结束】===============================================================