【因子信息开始】===============================================================

【因子编号和名称】
因子编号: HT001: 希尔伯特瞬时趋势线 (Hilbert Transform Instantaneous Trendline, HT_TRENDLINE)

【1. 因子名称详情】
因子1: 希尔伯特瞬时趋势线 (Hilbert Transform Instantaneous Trendline, HT_TRENDLINE)

【2. 核心公式】
该因子的计算涉及多个步骤，包括价格平滑、希尔伯特变换、主导周期计算和趋势线生成。

**步骤 1: 价格平滑 (Price Smoothing)**
使用4周期加权移动平均 (WMA) 对输入价格进行平滑：
$SP_t = \frac{4 \cdot P_t + 3 \cdot P_{t-1} + 2 \cdot P_{t-2} + 1 \cdot P_{t-3}}{4+3+2+1}$
其中 $P_t$ 是当前周期的价格， $SP_t$ 是当前周期的平滑价格。

**步骤 2: 希尔伯特变换分量 (Hilbert Transform Components)**
对平滑价格 $SP_t$ 应用希尔伯特变换滤波器，得到同相分量 (In-Phase, $I1_t$) 和正交分量 (Quadrature, $Q1_t$) 的基础。
常量: $a = 0.0962$, $b = 0.5769$

$Detrender_t = (a \cdot SP_t) + (b \cdot Detrender_{t-2}) - (a \cdot SP_{t-4}) - Detrender_{t-4}$
(Detrender 实际上是 $I1_t$ 的初步形式，它代表了去除直流分量后的价格波动)

$Q1_t = (a \cdot Detrender_t) + (b \cdot Q1_{t-2}) - (a \cdot Detrender_{t-4}) - Q1_{t-4}$

**步骤 3: 同相和正交分量 (I & Q Components) 及周期计算**
$I1_t = Detrender_{t-3}$ (将 $Detrender$ 信号延迟3个周期作为 $I1_t$)

计算辅助分量 $jI_t$ 和 $jQ_t$:
$jI_t = (a \cdot I1_t) + (b \cdot jI_{t-2}) - (a \cdot I1_{t-4}) - jI_{t-4}$
$jQ_t = (a \cdot Q1_t) + (b \cdot jQ_{t-2}) - (a \cdot Q1_{t-4}) - jQ_{t-4}$

平滑 $I1_t$ 和 $Q1_t$ 以获得 $I2_t$ 和 $Q2_t$:
$I2_t = 0.2 \cdot (I1_t - jQ_t) + 0.8 \cdot I2_{t-1}$
$Q2_t = 0.2 \cdot (Q1_t + jI_t) + 0.8 \cdot Q2_{t-1}$

计算相位信息的实部 ($Re_t$) 和虚部 ($Im_t$):
$Re_t = 0.2 \cdot (I2_t \cdot I2_{t-1} + Q2_t \cdot Q2_{t-1}) + 0.8 \cdot Re_{t-1}$
$Im_t = 0.2 \cdot (I2_t \cdot Q2_{t-1} - Q2_t \cdot I2_{t-1}) + 0.8 \cdot Im_{t-1}$

计算周期 ($Period_t$):
若 $Im_t \neq 0$ 且 $Re_t \neq 0$:
$Period_t = \frac{360}{\operatorname{atan2}(Im_t, Re_t) \cdot \frac{180}{\pi}}$
(其中 $\operatorname{atan2}(Im_t, Re_t)$ 返回以弧度为单位的角度, $\frac{180}{\pi}$ 用于将弧度转为角度)
否则，$Period_t = Period_{t-1}$ (保持上一周期值)

周期值限制与平滑:
$Period'_t = Period_t$
若 $Period'_t > 1.5 \cdot Period_{t-1\_limited}$, 则 $Period'_t = 1.5 \cdot Period_{t-1\_limited}$
若 $Period'_t < 0.67 \cdot Period_{t-1\_limited}$, 则 $Period'_t = 0.67 \cdot Period_{t-1\_limited}$
若 $Period'_t < 6$, 则 $Period'_t = 6$
若 $Period'_t > 50$, 则 $Period'_t = 50$
$Period_{final,t} = 0.2 \cdot Period'_t + 0.8 \cdot Period_{t-1\_limited}$
(其中 $Period_{t-1\_limited}$ 是上一周期经过限制后的周期值)

$SmoothPeriod_t = 0.33 \cdot Period_{final,t} + 0.67 \cdot SmoothPeriod_{t-1}$

**步骤 4: 瞬时趋势线 (Instantaneous Trendline)**
$DCPeriod_t = SmoothPeriod_t + 0.5$
$DCPeriodInt_t = \lfloor DCPeriod_t \rfloor$ (取整数部分)

计算 $DCPeriodInt_t$ 周期内的原始价格均值 (作为未平滑的趋势值):
$iTrend_{raw,t} = \frac{1}{DCPeriodInt_t} \sum_{k=0}^{DCPeriodInt_t-1} P_{t-k}$ (如果 $DCPeriodInt_t > 0$)

对 $iTrend_{raw,t}$ 进行加权移动平均得到最终的瞬时趋势线:
$HT\_TRENDLINE_t = \frac{4 \cdot iTrend_{raw,t} + 3 \cdot iTrend_{raw,t-1} + 2 \cdot iTrend_{raw,t-2} + 1 \cdot iTrend_{raw,t-3}}{10}$

【3. 变量定义】
*   $P_t$: 在 $t$ 时刻的输入价格（通常为收盘价）。
*   $SP_t$: 在 $t$ 时刻的平滑价格。
*   $a, b$: 希尔伯特变换滤波器的常数，$a=0.0962, b=0.5769$。
*   $Detrender_t$: 在 $t$ 时刻的去趋势信号，希尔伯特变换的中间步骤。
*   $Q1_t$: 在 $t$ 时刻的初步正交分量。
*   $I1_t$: 在 $t$ 时刻的同相分量，为 $Detrender_{t-3}$。
*   $jI_t, jQ_t$: 在 $t$ 时刻，用于计算 $I2, Q2$ 的辅助变换分量。
*   $I2_t, Q2_t$: 在 $t$ 时刻，经过平滑处理的同相和正交分量。
*   $Re_t, Im_t$: 在 $t$ 时刻，用于计算相位的复数信号的实部和虚部。
*   $Period_t$: 在 $t$ 时刻，计算得到的原始周期长度。
*   $Period'_t$: 在 $t$ 时刻，经过限制条件调整后的周期长度。
*   $Period_{t-1\_limited}$: 上一周期 $t-1$ 经过限制后的周期值。
*   $Period_{final,t}$: 在 $t$ 时刻，对 $Period'_t$ 和 $Period_{t-1\_limited}$ 进行加权平均得到的周期。
*   $SmoothPeriod_t$: 在 $t$ 时刻，对 $Period_{final,t}$ 进行进一步平滑得到的周期长度。
*   $DCPeriod_t$: 在 $t$ 时刻的主导周期 (Dominant Cycle Period)。
*   $DCPeriodInt_t$: $DCPeriod_t$ 的整数部分。
*   $iTrend_{raw,t}$: 在 $t$ 时刻，根据主导周期计算的未平滑的瞬时趋势值。
*   $HT\_TRENDLINE_t$: 在 $t$ 时刻的最终希尔伯特瞬时趋势线值。

【4. 函数与方法说明】
*   **加权移动平均 (WMA - Weighted Moving Average)**:
    对于N周期WMA，计算公式为: $WMA_t = \frac{\sum_{i=0}^{N-1} (N-i) \cdot P_{t-i}}{\sum_{i=0}^{N-1} (N-i)}$。
    在步骤1和步骤4中，均使用4周期WMA，其权重分别为4, 3, 2, 1，权重之和为10。
    $WMA_{4,t} = \frac{4 \cdot X_t + 3 \cdot X_{t-1} + 2 \cdot X_{t-2} + 1 \cdot X_{t-3}}{10}$，其中X为被平均的序列。

*   **希尔伯特变换滤波器 (Hilbert Transform Filter)**:
    $Detrender_t$ 和 $Q1_t$ (以及 $jI_t, jQ_t$) 的计算使用了一种特定的数字滤波器结构，形式为：
    $Y_t = (a \cdot X_t) + (b \cdot Y_{t-2}) - (a \cdot X_{t-4}) - Y_{t-4}$
    其中 $X_t$ 是输入序列，$Y_t$ 是输出序列，$Y_{t-2}$ 是前2个周期的输出，$X_{t-4}$ 是前4个周期的输入，$Y_{t-4}$ 是前4个周期的输出。常数 $a=0.0962, b=0.5769$。这是一种简化的数字实现，旨在近似希尔伯特变换的效果。

*   **指数平滑 (Exponential Smoothing / EMA-like smoothing)**:
    $I2_t, Q2_t, Re_t, Im_t, Period_{final,t}, SmoothPeriod_t$ 的计算采用了类似指数移动平均的平滑方式。
    通用形式为 $Y_t = \alpha \cdot X_t + (1-\alpha) \cdot Y_{t-1}$。
    例如，对于 $I2_t = 0.2 \cdot (Value) + 0.8 \cdot I2_{t-1}$，平滑因子 $\alpha = 0.2$。

*   **$\operatorname{atan2}(y, x)$**:
    此函数计算 $y/x$ 的反正切值，但它使用两个参数的符号来确定结果的象限，从而返回 $(-\pi, \pi]$ 范围内的角度（弧度）。

*   **$\lfloor x \rfloor$ (Floor Function)**:
    取小于或等于 $x$ 的最大整数。

【5. 计算步骤】
1.  **数据准备**: 获取历史价格序列 $P$ (例如收盘价)。
2.  **初始化**:
    *   为 $Detrender_0, Detrender_{-1}, \dots, Detrender_{-4}$ 设置初始值 (通常为0或使用早期数据通过滤波器启动)。
    *   为 $Q1_0, Q1_{-1}, \dots, Q1_{-4}$ 设置初始值。
    *   为 $jI_0, jI_{-1}, \dots, jI_{-4}$ 和 $jQ_0, jQ_{-1}, \dots, jQ_{-4}$ 设置初始值。
    *   为 $I2_0, Q2_0, Re_0, Im_0, Period_0, SmoothPeriod_0$ 设置初始值 (通常为0或根据前几期数据计算的合理值)。
    *   为 $iTrend_{raw, -1}, iTrend_{raw, -2}, iTrend_{raw, -3}$ 设置初始值 (通常为0或早期价格)。
    *   需要至少63个数据点作为回看期来稳定输出。

3.  **迭代计算 (对每个时间点 $t$)**:
    a.  **计算平滑价格 $SP_t$**: 使用4周期WMA公式计算当前输入价格 $P_t$ 的平滑值 $SP_t$。
        $SP_t = (4 \cdot P_t + 3 \cdot P_{t-1} + 2 \cdot P_{t-2} + 1 \cdot P_{t-3}) / 10$

    b.  **计算 $Detrender_t$**:
        $Detrender_t = (0.0962 \cdot SP_t) + (0.5769 \cdot Detrender_{t-2}) - (0.0962 \cdot SP_{t-4}) - Detrender_{t-4}$

    c.  **计算 $Q1_t$**:
        $Q1_t = (0.0962 \cdot Detrender_t) + (0.5769 \cdot Q1_{t-2}) - (0.0962 \cdot Detrender_{t-4}) - Q1_{t-4}$

    d.  **获取 $I1_t$**:
        $I1_t = Detrender_{t-3}$

    e.  **计算 $jI_t$**:
        $jI_t = (0.0962 \cdot I1_t) + (0.5769 \cdot jI_{t-2}) - (0.0962 \cdot I1_{t-4}) - jI_{t-4}$

    f.  **计算 $jQ_t$**:
        $jQ_t = (0.0962 \cdot Q1_t) + (0.5769 \cdot jQ_{t-2}) - (0.0962 \cdot Q1_{t-4}) - jQ_{t-4}$

    g.  **计算 $I2_t$ 和 $Q2_t$**:
        $I2_t = 0.2 \cdot (I1_t - jQ_t) + 0.8 \cdot I2_{t-1}$
        $Q2_t = 0.2 \cdot (Q1_t + jI_t) + 0.8 \cdot Q2_{t-1}$

    h.  **计算 $Re_t$ 和 $Im_t$**:
        $Re_t = 0.2 \cdot (I2_t \cdot I2_{t-1} + Q2_t \cdot Q2_{t-1}) + 0.8 \cdot Re_{t-1}$
        $Im_t = 0.2 \cdot (I2_t \cdot Q2_{t-1} - Q2_t \cdot I2_{t-1}) + 0.8 \cdot Im_{t-1}$

    i.  **计算原始周期 $Period_t$**:
        如果 $Im_t$ 和 $Re_t$ 均不为零，则 $Period_t = 360 / (\operatorname{atan2}(Im_t, Re_t) \cdot 180/\pi)$。否则，$Period_t = Period_{t-1\_limited}$ (使用上一个限制后的周期)。

    j.  **限制和初步平滑周期 $Period'_t$ 和 $Period_{final,t}$**:
        $Period'_t = Period_t$
        $Period'_t = \max(0.67 \cdot Period_{t-1\_limited}, \min(1.5 \cdot Period_{t-1\_limited}, Period'_t))$
        $Period'_t = \max(6, \min(50, Period'_t))$
        $Period_{final,t} = 0.2 \cdot Period'_t + 0.8 \cdot Period_{t-1\_limited}$
        (赋值：$Period_{t\_limited} = Period'_t$ 以供下一周期使用 $Period_{t-1\_limited}$）

    k.  **计算平滑周期 $SmoothPeriod_t$**:
        $SmoothPeriod_t = 0.33 \cdot Period_{final,t} + 0.67 \cdot SmoothPeriod_{t-1}$

    l.  **计算主导周期整数 $DCPeriodInt_t$**:
        $DCPeriod_t = SmoothPeriod_t + 0.5$
        $DCPeriodInt_t = \lfloor DCPeriod_t \rfloor$

    m. **计算未平滑的趋势值 $iTrend_{raw,t}$**:
        如果 $DCPeriodInt_t > 0$， $iTrend_{raw,t} = (\sum_{k=0}^{DCPeriodInt_t-1} P_{t-k}) / DCPeriodInt_t$。
        否则，$iTrend_{raw,t} = P_t$ (或一个合理的默认值，例如前一个 $iTrend_{raw}$ 值)。

    n.  **计算最终瞬时趋势线 $HT\_TRENDLINE_t$**:
        $HT\_TRENDLINE_t = (4 \cdot iTrend_{raw,t} + 3 \cdot iTrend_{raw,t-1} + 2 \cdot iTrend_{raw,t-2} + 1 \cdot iTrend_{raw,t-3}) / 10$

    o.  **更新历史值**: 将当前计算得到的 $SP_t, Detrender_t, Q1_t, I1_t, jI_t, jQ_t, I2_t, Q2_t, Re_t, Im_t, Period_{final,t}, Period'_t (作为下一轮的 Period_{t-1\_limited}), SmoothPeriod_t, iTrend_{raw,t}$ 保存，供下一周期计算使用。

【6. 备注与参数说明】
*   **回看期 (Lookback Period)**: 该指标具有较长的有效回看期，TA-Lib中指定为63个周期，外加一个不影响结果的“不稳定周期”调整。这意味着前63个（或更多）周期的输出值可能无效或不稳定。
*   **希尔伯特变换常数**: $a = 0.0962$ 和 $b = 0.5769$ 是Ehlers在其著作中提出的经典值。
*   **平滑因子**: 计算I2, Q2, Re, Im, Period, SmoothPeriod时使用的平滑因子 (例如0.2, 0.8, 0.33, 0.67) 是经验值，可以调整以改变指标的灵敏度。
*   **周期限制**: 计算出的周期 ($Period_t$) 被限制在6到50之间，并且其相对于前一周期值的变化率也受到限制 (不超过1.5倍，不低于0.67倍)。这些限制是为了防止周期值出现极端或不稳定的跳动。
*   **数据预处理**: 对输入价格序列通常不需要特殊预处理，但要确保数据是连续的。
*   **应用**: 该指标旨在识别价格的主要周期性波动，并据此构建一条能够快速响应趋势变化的平滑趋势线。

【因子信息结束】===============================================================