【因子信息开始】===============================================================

【因子编号和名称】

因子编号: F001: 随机震荡指标K线 (Stochastic Oscillator %K, SlowK)

【1. 因子名称详情】

因子名称为随机震荡指标K线，也称为慢速K线（SlowK）。它是随机震荡指标（Stochastic Oscillator）的一部分，用于衡量收盘价在一定周期内高低价区间的相对位置。

【2. 核心公式】

计算SlowK主要包含两个步骤：首先计算FastK（也称为 %K 或原始K值），然后对FastK进行平滑得到SlowK。

1.  **FastK 计算**:
    \[
    \text{FastK}_t = 100 \times \frac{C_t - L_N}{H_N - L_N}
    \]
    如果 \(H_N - L_N = 0\)，则 \(\text{FastK}_t = 0\)。

2.  **SlowK 计算**:
    \[
    \text{SlowK}_t = \text{MA}(\text{FastK}, P_K, \text{Type}_K)_t
    \]
    其中 \(\text{MA}\) 代表移动平均。

【3. 变量定义】

*   \(C_t\): 第 \(t\) 期的收盘价。
*   \(L_N\): 过去 \(N\) 期内的最低价（包括第 \(t\) 期）。 \(N\) 对应参数 `optInFastK_Period`。
*   \(H_N\): 过去 \(N\) 期内的最高价（包括第 \(t\) 期）。 \(N\) 对应参数 `optInFastK_Period`。
*   \(\text{FastK}_t\): 第 \(t\) 期的快速K值。
*   \(P_K\): 计算SlowK时，对FastK进行移动平均的周期。对应参数 `optInSlowK_Period`。
*   \(\text{Type}_K\): 计算SlowK时，对FastK进行移动平均的类型。对应参数 `optInSlowK_MAType`。
*   \(\text{SlowK}_t\): 第 \(t\) 期的慢速K值。

【4. 函数与方法说明】

*   **最低价 \(L_N\)**:
    在当前观察期 \(t\) 向前回溯 \(N\) 个周期（包括当前周期 \(t\)），找出这些周期内最低价序列中的最小值。
    \[
    L_N = \min(Low_{t-N+1}, Low_{t-N+2}, \dots, Low_t)
    \]
    其中 \(Low_i\) 是第 \(i\) 期的最低价。

*   **最高价 \(H_N\)**:
    在当前观察期 \(t\) 向前回溯 \(N\) 个周期（包括当前周期 \(t\)），找出这些周期内最高价序列中的最大值。
    \[
    H_N = \max(High_{t-N+1}, High_{t-N+2}, \dots, High_t)
    \]
    其中 \(High_i\) 是第 \(i\) 期的最高价。

*   **移动平均 (MA)**:
    \(\text{MA}(\text{Data}, P, \text{Type})\) 表示对序列 \(\text{Data}\) 计算周期为 \(P\) 的特定类型 (\(\text{Type}\)) 的移动平均。
    *   **简单移动平均 (SMA)** 是最常见的类型之一：
        \[
        \text{SMA}(\text{Data}, P)_t = \frac{1}{P} \sum_{i=0}^{P-1} \text{Data}_{t-i}
        \]
    *   其他移动平均类型包括指数移动平均 (EMA)、加权移动平均 (WMA) 等，它们有各自不同的权重分配方式。源码中支持多种MA类型，如SMA, EMA, WMA, DEMA, TEMA, TRIMA, KAMA, MAMA, T3。卡片中以SMA为例进行说明。

【5. 计算步骤】

1.  **数据准备**:
    收集时间序列数据，包括每日的最高价 (`inHigh`)、最低价 (`inLow`) 和收盘价 (`inClose`)。
    确定参数 `optInFastK_Period` (即 \(N\))。

2.  **计算FastK序列**:
    对于时间序列中的每一个交易日 \(t\)，从第 \(N\) 个数据点开始：
    a.  找出从 \(t-N+1\) 到 \(t\) 这 \(N\) 个周期内的最低价 \(L_N\)。
    b.  找出从 \(t-N+1\) 到 \(t\) 这 \(N\) 个周期内的最高价 \(H_N\)。
    c.  计算 \(H_N - L_N\)。
    d.  如果 \(H_N - L_N\) 大于0 (源码中判断 `diff != 0.0`)，则计算:
        \[
        \text{FastK}_t = 100 \times \frac{C_t - L_N}{H_N - L_N}
        \]
    e.  如果 \(H_N - L_N\) 等于0 (源码中判断 `diff == 0.0`)，则 \(\text{FastK}_t = 0\)。

3.  **计算SlowK序列**:
    a.  确定参数 `optInSlowK_Period` (即 \(P_K\)) 和 `optInSlowK_MAType` (即 \(\text{Type}_K\))。
    b.  对上一步生成的 FastK 序列，应用选定的移动平均类型 (\(\text{Type}_K\)) 和周期 (\(P_K\)) 进行计算，得到 SlowK 序列。例如，若使用SMA：
        \[
        \text{SlowK}_t = \text{SMA}(\text{FastK}, P_K)_t = \frac{1}{P_K} \sum_{i=0}^{P_K-1} \text{FastK}_{t-i}
        \]

【6. 备注与参数说明】

*   `optInFastK_Period` (\(N\)): 计算FastK时回溯的周期长度。典型值为5或14。
*   `optInSlowK_Period` (\(P_K\)): 对FastK进行平滑以得到SlowK的移动平均周期。典型值为3。
*   `optInSlowK_MAType` (\(\text{Type}_K\)): 计算SlowK时使用的移动平均类型。常用的为SMA (简单移动平均)。
*   因子的计算需要足够的前置数据。所需的最少数据点数量为 `(optInFastK_Period - 1) + MA_Lookback(optInSlowK_Period, optInSlowK_MAType)`。这里的 `MA_Lookback` 是计算该类型移动平均所需的前置周期数 (例如SMA的lookback是 `period-1`)。
*   当周期内最高价等于最低价时，FastK被处理为0。这是一种处理分母为零情况的常见方法。



【7. 关联因子补充说明】

【关联因子信息开始】===============================================================

【因子编号和名称】

因子编号: F001: 随机震荡指标K线 (Stochastic Oscillator %K, SlowK)

【1. 因子名称详情】

因子名称为随机震荡指标K线，也称为慢速K线（SlowK）。它是随机震荡指标（Stochastic Oscillator）的一部分，用于衡量收盘价在一定周期内高低价区间的相对位置。

【2. 核心公式】

计算SlowK主要包含两个步骤：首先计算FastK（也称为 %K 或原始K值），然后对FastK进行平滑得到SlowK。

1.  **FastK 计算**:
    \[
    \text{FastK}_t = 100 \times \frac{C_t - L_N}{H_N - L_N}
    \]
    如果 \(H_N - L_N = 0\)，则 \(\text{FastK}_t = 0\)。

2.  **SlowK 计算**:
    \[
    \text{SlowK}_t = \text{MA}(\text{FastK}, P_K, \text{Type}_K)_t
    \]
    其中 \(\text{MA}\) 代表移动平均。

【3. 变量定义】

*   \(C_t\): 第 \(t\) 期的收盘价。
*   \(L_N\): 过去 \(N\) 期内的最低价（包括第 \(t\) 期）。 \(N\) 对应参数 `optInFastK_Period`。
*   \(H_N\): 过去 \(N\) 期内的最高价（包括第 \(t\) 期）。 \(N\) 对应参数 `optInFastK_Period`。
*   \(\text{FastK}_t\): 第 \(t\) 期的快速K值。
*   \(P_K\): 计算SlowK时，对FastK进行移动平均的周期。对应参数 `optInSlowK_Period`。
*   \(\text{Type}_K\): 计算SlowK时，对FastK进行移动平均的类型。对应参数 `optInSlowK_MAType`。
*   \(\text{SlowK}_t\): 第 \(t\) 期的慢速K值。

【4. 函数与方法说明】

*   **最低价 \(L_N\)**:
    在当前观察期 \(t\) 向前回溯 \(N\) 个周期（包括当前周期 \(t\)），找出这些周期内最低价序列中的最小值。
    \[
    L_N = \min(Low_{t-N+1}, Low_{t-N+2}, \dots, Low_t)
    \]
    其中 \(Low_i\) 是第 \(i\) 期的最低价。

*   **最高价 \(H_N\)**:
    在当前观察期 \(t\) 向前回溯 \(N\) 个周期（包括当前周期 \(t\)），找出这些周期内最高价序列中的最大值。
    \[
    H_N = \max(High_{t-N+1}, High_{t-N+2}, \dots, High_t)
    \]
    其中 \(High_i\) 是第 \(i\) 期的最高价。

*   **移动平均 (MA)**:
    \(\text{MA}(\text{Data}, P, \text{Type})\) 表示对序列 \(\text{Data}\) 计算周期为 \(P\) 的特定类型 (\(\text{Type}\)) 的移动平均。
    *   **简单移动平均 (SMA)** 是最常见的类型之一：
        \[
        \text{SMA}(\text{Data}, P)_t = \frac{1}{P} \sum_{i=0}^{P-1} \text{Data}_{t-i}
        \]
    *   其他移动平均类型包括指数移动平均 (EMA)、加权移动平均 (WMA) 等，它们有各自不同的权重分配方式。源码中支持多种MA类型，如SMA, EMA, WMA, DEMA, TEMA, TRIMA, KAMA, MAMA, T3。卡片中以SMA为例进行说明。

【5. 计算步骤】

1.  **数据准备**:
    收集时间序列数据，包括每日的最高价 (`inHigh`)、最低价 (`inLow`) 和收盘价 (`inClose`)。
    确定参数 `optInFastK_Period` (即 \(N\))。

2.  **计算FastK序列**:
    对于时间序列中的每一个交易日 \(t\)，从第 \(N\) 个数据点开始：
    a.  找出从 \(t-N+1\) 到 \(t\) 这 \(N\) 个周期内的最低价 \(L_N\)。
    b.  找出从 \(t-N+1\) 到 \(t\) 这 \(N\) 个周期内的最高价 \(H_N\)。
    c.  计算 \(H_N - L_N\)。
    d.  如果 \(H_N - L_N\) 大于0 (源码中判断 `diff != 0.0`)，则计算:
        \[
        \text{FastK}_t = 100 \times \frac{C_t - L_N}{H_N - L_N}
        \]
    e.  如果 \(H_N - L_N\) 等于0 (源码中判断 `diff == 0.0`)，则 \(\text{FastK}_t = 0\)。

3.  **计算SlowK序列**:
    a.  确定参数 `optInSlowK_Period` (即 \(P_K\)) 和 `optInSlowK_MAType` (即 \(\text{Type}_K\))。
    b.  对上一步生成的 FastK 序列，应用选定的移动平均类型 (\(\text{Type}_K\)) 和周期 (\(P_K\)) 进行计算，得到 SlowK 序列。例如，若使用SMA：
        \[
        \text{SlowK}_t = \text{SMA}(\text{FastK}, P_K)_t = \frac{1}{P_K} \sum_{i=0}^{P_K-1} \text{FastK}_{t-i}
        \]

【6. 备注与参数说明】

*   `optInFastK_Period` (\(N\)): 计算FastK时回溯的周期长度。典型值为5或14。
*   `optInSlowK_Period` (\(P_K\)): 对FastK进行平滑以得到SlowK的移动平均周期。典型值为3。
*   `optInSlowK_MAType` (\(\text{Type}_K\)): 计算SlowK时使用的移动平均类型。常用的为SMA (简单移动平均)。
*   因子的计算需要足够的前置数据。所需的最少数据点数量为 `(optInFastK_Period - 1) + MA_Lookback(optInSlowK_Period, optInSlowK_MAType)`。这里的 `MA_Lookback` 是计算该类型移动平均所需的前置周期数 (例如SMA的lookback是 `period-1`)。
*   当周期内最高价等于最低价时，FastK被处理为0。这是一种处理分母为零情况的常见方法。

【关联因子信息结束】===============================================================

---

【关联因子信息开始】===============================================================

【因子编号和名称】

因子编号: F002: 随机震荡指标D线 (Stochastic Oscillator %D, SlowD)

【1. 因子名称详情】

因子名称为随机震荡指标D线，也称为慢速D线（SlowD）。它是随机震荡指标（Stochastic Oscillator）的信号线，通过对SlowK线进行再次平滑得到。

【2. 核心公式】

SlowD 的计算公式为：
\[
\text{SlowD}_t = \text{MA}(\text{SlowK}, P_D, \text{Type}_D)_t
\]
其中 \(\text{MA}\) 代表移动平均。

【3. 变量定义】

*   \(\text{SlowK}_t\): 第 \(t\) 期的慢速K值（即因子F001的值）。
*   \(P_D\): 计算SlowD时，对SlowK进行移动平均的周期。对应参数 `optInSlowD_Period`。
*   \(\text{Type}_D\): 计算SlowD时，对SlowK进行移动平均的类型。对应参数 `optInSlowD_MAType`。
*   \(\text{SlowD}_t\): 第 \(t\) 期的慢速D值。

【4. 函数与方法说明】

*   **移动平均 (MA)**:
    \(\text{MA}(\text{Data}, P, \text{Type})\) 表示对序列 \(\text{Data}\) 计算周期为 \(P\) 的特定类型 (\(\text{Type}\)) 的移动平均。
    *   **简单移动平均 (SMA)** 是最常见的类型之一：
        \[
        \text{SMA}(\text{Data}, P)_t = \frac{1}{P} \sum_{i=0}^{P-1} \text{Data}_{t-i}
        \]
    *   其他移动平均类型包括指数移动平均 (EMA)、加权移动平均 (WMA) 等，它们有各自不同的权重分配方式。源码中支持多种MA类型，如SMA, EMA, WMA, DEMA, TEMA, TRIMA, KAMA, MAMA, T3。卡片中以SMA为例进行说明。

【5. 计算步骤】

1.  **计算SlowK序列 (依赖步骤)**:
    a.  **数据准备**: 收集时间序列数据，包括每日的最高价 (`inHigh`)、最低价 (`inLow`) 和收盘价 (`inClose`)。确定参数 `optInFastK_Period` (\(N\))。
    b.  **计算FastK序列**: 对于时间序列中的每一个交易日 \(t\)，从第 \(N\) 个数据点开始：
        i.  找出从 \(t-N+1\) 到 \(t\) 这 \(N\) 个周期内的最低价 \(L_N = \min(Low_{t-N+1}, \dots, Low_t)\)。
        ii. 找出从 \(t-N+1\) 到 \(t\) 这 \(N\) 个周期内的最高价 \(H_N = \max(High_{t-N+1}, \dots, High_t)\)。
        iii. 计算 \(H_N - L_N\)。
        iv. 如果 \(H_N - L_N\) 大于0，则计算:
            \[
            \text{FastK}_t = 100 \times \frac{C_t - L_N}{H_N - L_N}
            \]
        v.  如果 \(H_N - L_N\) 等于0，则 \(\text{FastK}_t = 0\)。
    c.  **计算SlowK序列**: 确定参数 `optInSlowK_Period` (\(P_K\)) 和 `optInSlowK_MAType` (\(\text{Type}_K\))。对上一步生成的 FastK 序列，应用选定的移动平均类型 (\(\text{Type}_K\)) 和周期 (\(P_K\)) 进行计算，得到 SlowK 序列。例如，若使用SMA：
        \[
        \text{SlowK}_t = \text{SMA}(\text{FastK}, P_K)_t = \frac{1}{P_K} \sum_{i=0}^{P_K-1} \text{FastK}_{t-i}
        \]

2.  **计算SlowD序列**:
    a.  获得步骤1中计算得到的 SlowK 序列。
    b.  确定参数 `optInSlowD_Period` (即 \(P_D\)) 和 `optInSlowD_MAType` (即 \(\text{Type}_D\))。
    c.  对 SlowK 序列，应用选定的移动平均类型 (\(\text{Type}_D\)) 和周期 (\(P_D\)) 进行计算，得到 SlowD 序列。例如，若使用SMA：
        \[
        \text{SlowD}_t = \text{SMA}(\text{SlowK}, P_D)_t = \frac{1}{P_D} \sum_{i=0}^{P_D-1} \text{SlowK}_{t-i}
        \]

【6. 备注与参数说明】

*   `optInFastK_Period` (\(N\)): 计算FastK时回溯的周期长度。典型值为5或14。（此参数影响SlowK，进而影响SlowD）。
*   `optInSlowK_Period` (\(P_K\)): 对FastK进行平滑以得到SlowK的移动平均周期。典型值为3。（此参数影响SlowK，进而影响SlowD）。
*   `optInSlowK_MAType` (\(\text{Type}_K\)): 计算SlowK时使用的移动平均类型。常用的为SMA。（此参数影响SlowK，进而影响SlowD）。
*   `optInSlowD_Period` (\(P_D\)): 对SlowK进行平滑以得到SlowD的移动平均周期。典型值为3。
*   `optInSlowD_MAType` (\(\text{Type}_D\)): 计算SlowD时使用的移动平均类型。常用的为SMA。
*   因子的计算需要足够的前置数据。所需的最少数据点数量为 `(optInFastK_Period - 1) + MA_Lookback(optInSlowK_Period, optInSlowK_MAType) + MA_Lookback(optInSlowD_Period, optInSlowD_MAType)`。这里的 `MA_Lookback` 是计算该类型移动平均所需的前置周期数。
*   SlowD 是 SlowK 的平滑版本，常作为其信号线使用。

【关联因子信息结束】===============================================================

【因子信息结束】===============================================================