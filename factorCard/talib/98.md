【因子信息开始】===============================================================

【因子编号和名称】

因子编号: TA_ROCR_001
因子中文名称: 变动率比率 (Rate of Change Ratio, ROCR)

【1. 因子名称详情】

因子1: 变动率比率 (Rate of Change Ratio, ROCR)
该指标衡量当前价格相对于指定周期前价格的比率。

【2. 核心公式】

给定时间序列价格数据 \(P\)，以及一个时间窗口 `period`，在时间点 \(t\) 的变动率比率 \(\text{ROCR}_t\) 计算如下：

\[
\text{ROCR}_t =
\begin{cases}
\frac{P_t}{P_{t-\text{period}}} & \text{如果 } P_{t-\text{period}} \neq 0 \\
0 & \text{如果 } P_{t-\text{period}} = 0
\end{cases}
\]

其中：
*   \(P_t\) 是当前周期的价格。
*   \(P_{t-\text{period}}\) 是 `period` 个周期前的价格。

【3. 变量定义】

*   \(P_t\): 在计算当前因子值时的价格（例如，当日收盘价）。
*   \(P_{t-\text{period}}\): 从当前时间点向前追溯 `period` 个周期的价格。
*   `period`: 计算变动率的时间窗口长度，是一个正整数。

【4. 函数与方法说明】

该因子主要使用基本的算术除法运算。没有涉及到复杂的特殊函数或统计方法。

【5. 计算步骤】

1.  **数据准备**:
    *   获取输入的价格时间序列数据（例如，每日收盘价列表 `inReal`）。
2.  **参数设定**:
    *   确定时间窗口长度 `optInTimePeriod` (即公式中的 `period`)。例如，如果 `optInTimePeriod` 为 10，则表示与10个周期前的价格进行比较。
3.  **初始化输出**:
    *   准备一个用于存储计算结果的序列 `outReal`。
    *   第一个有效的 \(\text{ROCR}\) 值会从输入序列的第 `optInTimePeriod` 个数据点开始计算（索引从0开始）。
4.  **迭代计算**:
    *   从输入数据序列的第 `optInTimePeriod` 个位置开始遍历到数据序列的末尾（即 `inIdx` 从 `startIdx` 遍历到 `endIdx`，其中 `startIdx` 不能小于 `optInTimePeriod`）。
    *   对于每一个当前价格 \(P_t\) (对应 `inReal[inIdx]`):
        a.  找到 `optInTimePeriod` 个周期前的价格 \(P_{t-\text{period}}\) (对应 `inReal[trailingIdx]`，其中 `trailingIdx = inIdx - optInTimePeriod`)。
        b.  **检查除数**:
            *   如果 \(P_{t-\text{period}}\) (即 `inReal[trailingIdx]`) 不等于 0：
                计算 \(\text{ROCR}_t = \frac{P_t}{P_{t-\text{period}}}\) (即 `outReal[outIdx] = inReal[inIdx] / inReal[trailingIdx]`)。
            *   如果 \(P_{t-\text{period}}\) (即 `inReal[trailingIdx]`) 等于 0：
                设置 \(\text{ROCR}_t = 0\) (即 `outReal[outIdx] = 0.0`)，以避免除以零的错误。
        c.  将计算得到的 \(\text{ROCR}_t\) 值存入结果序列 `outReal` 中。
        d.  移动到下一个价格数据点，并重复步骤 a-c。
5.  **输出结果**:
    *   最终得到 \(\text{ROCR}\) 的时间序列。

【6. 备注与参数说明】

*   **时间周期 (`optInTimePeriod`)**:
    *   这是计算ROCR时回溯的周期数。TA-LIB中的默认值为10。
    *   取值范围通常是从1到100000。
    *   较短的周期（如5或10）会使ROCR对价格的短期波动更敏感。
    *   较长的周期（如20或50）会产生更平滑的ROCR曲线，反映中长期趋势。
*   **数据输入 (`inReal`)**:
    *   通常使用收盘价序列作为输入，但也可以应用于开盘价、最高价、最低价等。
*   **输出值解释**:
    *   ROCR值围绕1.0波动。
    *   \(\text{ROCR} > 1.0\) 表示当前价格高于 دوره `period` 之前的价格，通常视为价格上涨。
    *   \(\text{ROCR} < 1.0\) 表示当前价格低于 دوره `period` 之前的价格，通常视为价格下跌。
    *   \(\text{ROCR} = 1.0\) 表示当前价格与 دوره `period` 之前的价格相同。
    *   例如，ROCR值为1.05意味着当前价格是 `period` 周期前价格的105%，即上涨了5%。ROCR值为0.95意味着当前价格是 `period` 周期前价格的95%，即下跌了5%。
*   **与其他变动率指标的区别**:
    TA-LIB中定义了多种变动率相关的指标，它们之间有细微差别：
    *   MOM (Momentum): \(P_t - P_{t-\text{period}}\) (价格差值)
    *   ROC (Rate of Change): \((\frac{P_t}{P_{t-\text{period}}} - 1) \times 100\) (百分比变动率，以0为中心)
    *   ROCP (Rate of Change Percentage): \(\frac{P_t - P_{t-\text{period}}}{P_{t-\text{period}}}\) (小数形式的变动率，以0为中心，乘以100即为百分比)
    *   **ROCR (Rate of Change Ratio)**: \(\frac{P_t}{P_{t-\text{period}}}\) (价格比率，以1为中心)
    *   ROCR100 (Rate of Change Ratio 100 Scale): \(\frac{P_t}{P_{t-\text{period}}} \times 100\) (价格比率乘以100，以100为中心)
    ROCR因其直接表示价格比率的特性而被使用。

【因子信息结束】===============================================================