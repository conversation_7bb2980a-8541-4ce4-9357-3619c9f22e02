【因子信息开始】===============================================================

【因子编号和名称】

因子编号: DMI001
因子中文名称: 正向方向指标 (Plus Directional Indicator, +DI)

【1. 因子名称详情】

DMI001: 正向方向指标 (Plus Directional Indicator, +DI)。该指标是动向指标（Directional Movement Index, DMI）系统的一部分，由 J. <PERSON> Wilder Jr. 开发，用于衡量价格上涨的强度。

【2. 核心公式】

该因子的核心计算分为几个步骤：首先计算单周期的真实波幅 (TR) 和单周期的正向动向 (+DM)，然后对这两者进行 N 周期平滑处理，最后计算 +DI。

1.  **单周期真实波幅 (True Range, $TR_1$)**:
    $TR_1(t) = \max(H_t - L_t, |H_t - C_{t-1}|, |L_t - C_{t-1}|)$

2.  **单周期正向动向 ($+DM_1$)**:
    首先计算当日最高价与前一日最高价之差（UpMove）和前一日最低价与当日最低价之差（DownMove）：
    $UpMove_t = H_t - H_{t-1}$
    $DownMove_t = L_{t-1} - L_t$

    则 $+DM_1(t)$ 定义为：
    $+DM_1(t) = \begin{cases} UpMove_t & \text{if } UpMove_t > DownMove_t \text{ and } UpMove_t > 0 \\ 0 & \text{otherwise} \end{cases}$

3.  **N 周期平滑正向动向 ($+DM_N$) 和 N 周期平滑真实波幅 ($TR_N$)**:
    采用 Welles Wilder 的平滑方法（一种特殊的指数移动平均）。
    *   **初始值** (计算序列中的第一个 $+DM_N$ 和 $TR_N$):
        $+DM_N(\text{initial_sum}) = \sum_{i=1}^{N-1} +DM_1(\text{period } i+1)$
        $TR_N(\text{initial_sum}) = \sum_{i=1}^{N-1} TR_1(\text{period } i+1)$
        (注意: 这里是累加了 N-1 个周期的值，因为第一个周期无法计算 $DM_1$ 和 $TR_1$)

        紧接着，这些初始累加值会作为前一期的平滑值，与第 N 个周期的 $+DM_1$ 和 $TR_1$ 一起，进行第一次平滑计算：
        $+DM_N(\text{first_smoothed}) = +DM_N(\text{initial_sum}) - \frac{+DM_N(\text{initial_sum})}{N} + +DM_1(\text{period } N)$
        $TR_N(\text{first_smoothed}) = TR_N(\text{initial_sum}) - \frac{TR_N(\text{initial_sum})}{N} + TR_1(\text{period } N)$

    *   **后续值** (对于 $k > \text{first_smoothed period}$):
        $+DM_N(k) = +DM_N(k-1) - \frac{+DM_N(k-1)}{N} + +DM_1(k)$
        $TR_N(k) = TR_N(k-1) - \frac{TR_N(k-1)}{N} + TR_1(k)$

4.  **正向方向指标 ($+DI_N$)**:
    $+DI_N(t) = 100 \times \frac{+DM_N(t)}{TR_N(t)}$
    如果 $TR_N(t) = 0$，则 $+DI_N(t) = 0$。

【3. 变量定义】

*   $t$: 当前时间周期（例如, 天）。
*   $H_t$: 当前周期的最高价。
*   $L_t$: 当前周期的最低价。
*   $C_t$: 当前周期的收盘价。
*   $H_{t-1}$: 前一交易周期的最高价。
*   $L_{t-1}$: 前一交易周期的最低价。
*   $C_{t-1}$: 前一交易周期的收盘价。
*   $TR_1(t)$: $t$ 周期的单周期真实波幅。
*   $UpMove_t$: $t$ 周期的向上移动值。
*   $DownMove_t$: $t$ 周期的向下移动值。
*   $+DM_1(t)$: $t$ 周期的单周期正向动向。
*   $N$: 计算 +DI 所选定的周期长度（例如, 14 天）。
*   $+DM_N(t)$: $t$ 周期的 N 周期平滑正向动向。
*   $TR_N(t)$: $t$ 周期的 N 周期平滑真实波幅。
*   $+DI_N(t)$: $t$ 周期的 N 周期正向方向指标。

【4. 函数与方法说明】

*   $\max(\cdot)$: 取括号内所有参数中的最大值。
*   $|\cdot|$: 绝对值函数。
*   **Welles Wilder 平滑法**:
    这是一种特殊的指数移动平均（EMA）的变体，常用于 Wilder 开发的技术指标。其计算方法如下：
    对于一个时间序列 $X_t$，其 N 周期 Wilder 平滑值 $S_t$ 的计算：
    *   第一个 $S_N$ 通常是前 N 个 $X_t$ 值的简单平均值或特定初始累加值（如本因子中对 $+DM_1$ 和 $TR_1$ 的处理）。
    *   后续的 $S_t$ (对于 $t > N$): $S_t = S_{t-1} - \frac{S_{t-1}}{N} + X_t$。
        这等价于 $S_t = \frac{(N-1)S_{t-1} + X_t}{N}$。
        TALIB C 源码中的实现是 `PreviousValue - (PreviousValue/N) + CurrentValue`，这符合 Wilder 的原始定义。

【5. 计算步骤】

假设我们有一系列每日的最高价 (High)、最低价 (Low) 和收盘价 (Close) 数据，以及参数 N (例如, 14)。

1.  **数据准备**:
    获取至少 `2*N` 个周期的 `H`, `L`, `C` 数据，以确保计算的稳定性和有效输出。

2.  **计算每个周期的单周期真实波幅 ($TR_1(t)$)**:
    从第二个数据点开始（因为第一个数据点没有 $C_{t-1}$）：
    对于每个周期 $t$ (从 2 开始)：
    $TR_1(t) = \max(H_t - L_t, |H_t - C_{t-1}|, |L_t - C_{t-1}|)$

3.  **计算每个周期的单周期正向动向 ($+DM_1(t)$)**:
    从第二个数据点开始：
    对于每个周期 $t$ (从 2 开始)：
    $UpMove_t = H_t - H_{t-1}$
    $DownMove_t = L_{t-1} - L_t$
    如果 $UpMove_t > DownMove_t$ 并且 $UpMove_t > 0$，则 $+DM_1(t) = UpMove_t$。
    否则，$+DM_1(t) = 0$。

4.  **计算 N 周期平滑正向动向 ($+DM_N$) 和 N 周期平滑真实波幅 ($TR_N$)**:
    a.  **初始化累加**:
        计算从第 2 个周期到第 $N$ 个周期（共 $N-1$ 个值）的 $+DM_1$ 和 $TR_1$ 的总和。
        令 $prev\_plusDM = \sum_{k=2}^{N} +DM_1(k)$
        令 $prev\_TR = \sum_{k=2}^{N} TR_1(k)$

    b.  **不稳定期平滑计算**:
        从第 $N+1$ 个周期开始，到第 $2N-1$ 个周期（这个阶段是 Wilder 平滑方法的不稳定期或“预热”期，共 $N-1$ 次迭代，加上第一次平滑即N次迭代），迭代计算平滑值：
        对于每个周期 $t$ （从 $N+1$ 到 $2N-1$）：
        读取当前周期的 $H_t, L_t, C_t$ 以及前一期 $H_{t-1}, L_{t-1}, C_{t-1}$。
        计算当前周期的 $current\_DM1(t)$ 和 $current\_TR1(t)$ (按步骤 2 和 3 的方法)。
        $prev\_plusDM = prev\_plusDM - \frac{prev\_plusDM}{N} + current\_DM1(t)$
        $prev\_TR = prev\_TR - \frac{prev\_TR}{N} + current\_TR1(t)$
        (注：C代码中此处还有一个额外的迭代，即共进行 `TA_GLOBALS_UNSTABLE_PERIOD + 1` 次迭代，其中 `TA_GLOBALS_UNSTABLE_PERIOD` 通常为 `N-1`，所以共 `N` 次迭代。上述描述中，从N+1到2N-1是N-1次，加上第一次初始累积后进行的第一次平滑（对应第N个DM1和TR1），总共是N次平滑迭代）。

    c.  **稳定期计算与输出**:
        从第 $2N$ 个周期开始（此时 `prev_plusDM` 和 `prev_TR` 是经过 $N$ 次平滑迭代后的值，对应第 $2N-1$ 个数据点的结果），计算第一个 $+DI_N$ 值：
        如果 $prev\_TR \neq 0$，则 $+DI_N(\text{first output}) = 100 \times \frac{prev\_plusDM}{prev\_TR}$。
        否则，$+DI_N(\text{first output}) = 0$。
        这个值对应于输入序列的第 $2N-1$ 个数据点（如果从1开始计数）。

        对于后续的每个周期 $t$ (从 $2N+1$ 开始)：
        读取当前周期的 $H_t, L_t, C_t$ 以及前一期 $H_{t-1}, L_{t-1}, C_{t-1}$。
        计算当前周期的 $current\_DM1(t)$ 和 $current\_TR1(t)$。
        $prev\_plusDM = prev\_plusDM - \frac{prev\_plusDM}{N} + current\_DM1(t)$
        $prev\_TR = prev\_TR - \frac{prev\_TR}{N} + current\_TR1(t)$
        如果 $prev\_TR \neq 0$，则 $+DI_N(t) = 100 \times \frac{prev\_plusDM}{prev\_TR}$。
        否则，$+DI_N(t) = 0$。

【6. 备注与参数说明】

*   **周期 N (optInTimePeriod)**: 最常用的参数值是 14。不同的市场和分析目标可能需要调整此参数。较短的周期使指标更敏感，较长的周期则更平滑。
*   **数据预处理**: 输入数据（高、低、收盘价）应为连续有效的时间序列。缺失值或异常值可能会影响计算结果。
*   **不稳定期 (Unstable Period)**: Wilder 的平滑方法在计算初期存在一个不稳定期。TALIB 的实现通过延迟输出起始点来确保输出的指标值是经过充分平滑的稳定值。第一个有效的 +DI 值通常在输入序列的第 `2*N-1` 个数据点（如果 N > 1）之后才开始。
*   **当 N=1 时的特殊情况**:
    如果周期 N 设置为 1，计算将简化：
    $+DI_1(t) = \frac{+DM_1(t)}{TR_1(t)}$ (如果 $TR_1(t) \neq 0$，否则为 0，通常乘以100)。
    其中 $+DM_1(t)$ 和 $TR_1(t)$ 按上述单周期方法计算。此时不存在平滑过程。
*   **取整**: J. Welles Wilder Jr. 在其原著中，中间计算步骤可能涉及取整。TALIB C 源码中，默认不进行这种取整（`#undef round_pos`），以保持浮点数精度。

【因子信息结束】===============================================================