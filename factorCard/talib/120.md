【因子信息开始】===============================================================

【因子编号和名称】

因子编号: F001 (此编号为示例，您可以自行定义)
因子中文名称: 真实波幅 (True Range, TR)

【1. 因子名称详情】

因子F001: 真实波幅 (True Range, TR)，该指标用于衡量价格波动的幅度。

【2. 核心公式】

对于每一个时间周期 `t`，真实波幅 `TR_t` 的计算公式如下：

`TR_t = \max(H_t - L_t, |H_t - C_{t-1}|, |L_t - C_{t-1}|)`

其中：
*   `H_t - L_t` 代表当期最高价与当期最低价的差值。
*   `|H_t - C_{t-1}|` 代表当期最高价与前期收盘价差值的绝对值。
*   `|L_t - C_{t-1}|` 代表当期最低价与前期收盘价差值的绝对值。
*   `\max(A, B, C)` 表示取 A, B, C 三者中的最大值。
*   `|x|` 表示 `x` 的绝对值。

【3. 变量定义】

*   `TR_t`: 时间周期 `t` 的真实波幅。
*   `H_t`: 时间周期 `t` 的最高价 (High Price)。
*   `L_t`: 时间周期 `t` 的最低价 (Low Price)。
*   `C_{t-1}`: 时间周期 `t-1` (即前一个时间周期) 的收盘价 (Closing Price)。
*   `t`: 表示当前时间周期。

【4. 函数与方法说明】

*   **绝对值函数 (`|x|`)**:
    计算一个数值的非负值。如果数值为正或零，则结果是其本身；如果数值为负，则结果是其相反数。
    数学表达式:
    `|x| = x, \text{ if } x \ge 0`
    `|x| = -x, \text{ if } x < 0`

*   **最大值函数 (`\max(v_1, v_2, ..., v_n)`)**:
    从一组数值 `v_1, v_2, ..., v_n` 中选择最大的一个值。在本因子中，是比较三个计算出的波幅值，取其中最大的一个。

【5. 计算步骤】

真实波幅的计算需要至少两个连续时间周期的数据，因为它涉及到前期收盘价。计算从数据序列的第二个时间周期开始。

1.  **数据准备**:
    *   获取当前时间周期 `t` 的最高价 `H_t` 和最低价 `L_t`。
    *   获取前一个时间周期 `t-1` 的收盘价 `C_{t-1}`。

2.  **计算第一个波幅值 (当日振幅)**:
    计算当期最高价与当期最低价之间的差值：
    `Term_1 = H_t - L_t`

3.  **计算第二个波幅值 (当日最高价与前期收盘价的振幅)**:
    计算当期最高价与前期收盘价之间差值的绝对值：
    `Term_2 = |H_t - C_{t-1}|`

4.  **计算第三个波幅值 (当日最低价与前期收盘价的振幅)**:
    计算当期最低价与前期收盘价之间差值的绝对值：
    `Term_3 = |L_t - C_{t-1}|`

5.  **确定真实波幅**:
    比较 `Term_1`, `Term_2`, 和 `Term_3` 这三个值，取其中最大的一个作为当期 `t` 的真实波幅 `TR_t`：
    `TR_t = \max(Term_1, Term_2, Term_3)`

6.  **迭代计算**:
    对于时间序列中的后续每个时间周期，重复步骤 1 至 5 进行计算。

【6. 备注与参数说明】

*   **数据起始点**: 由于计算真实波幅需要前一期的收盘价，因此对于时间序列的第一个数据点，无法计算其真实波幅。有效计算从第二个数据点开始。
*   **数据要求**: 计算该因子需要输入每个时间周期的高 (`High`)、低 (`Low`)、收 (`Close`) 价格数据。
*   **无平滑参数**: 真实波幅本身是一个未经平滑的逐期波动衡量指标，不包含可调整的窗口期参数。它常作为其他指标（如平均真实波幅 ATR）的计算基础，而这些派生指标通常会引入窗口期参数。
*   **波动性衡量**: 真实波幅体现了价格在一定程度上的真实波动范围，考虑了价格跳空缺口的情况（通过比较当日高/低价与昨日收盘价）。
*   **应用场景**: 主要用于衡量市场波动性，是构建多种技术分析指标（如ATR, ADX, ADXR等）的重要组成部分。

【因子信息结束】===============================================================