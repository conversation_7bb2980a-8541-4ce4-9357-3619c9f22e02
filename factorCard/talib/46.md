【因子信息开始】===============================================================

【因子编号和名称】

因子编号: F001
因子中文名称: 线性回归预测值 (Linear Regression Value, LRV)

【1. 因子名称详情】

因子1: 线性回归预测值 (Linear Regression Value, LRV)
该因子计算在给定周期内，通过线性回归方法拟合数据点后，在最后一个数据点（即当前时间点）上的预测值。

【2. 核心公式】

在时间点 `t`，考虑过去 `N` 期的数据序列 `P_1, P_2, ..., P_N` (其中 `P_N` 是当前时间点 `t` 的数据，`P_1` 是 `t-N+1` 时点的数据)。
我们为这些数据点拟合一条线性回归直线：
`\hat{P}(x) = b + m \cdot x`
其中 `x` 是时间序数，取值为 `0, 1, ..., N-1`，分别对应数据点 `P_1, P_2, ..., P_N`。

回归线的斜率 `m` 和截距 `b` 的计算公式如下：
`m = \frac{N \sum_{i=0}^{N-1} (x_i P_{i+1}) - (\sum_{i=0}^{N-1} x_i) (\sum_{i=0}^{N-1} P_{i+1})}{N \sum_{i=0}^{N-1} x_i^2 - (\sum_{i=0}^{N-1} x_i)^2}`

`b = \frac{\sum_{i=0}^{N-1} P_{i+1} - m \sum_{i=0}^{N-1} x_i}{N}`
或者等价地：`b = \bar{P} - m \bar{x}`，其中 `\bar{P}` 是 `P` 的均值，`\bar{x}` 是 `x` 的均值。

线性回归预测值 `LRV_t` (即因子值) 是该回归直线在最后一个时间序数（即 `x = N-1`）处的值：
`LRV_t = b + m \cdot (N-1)`

【3. 变量定义】

*   `P_k`: 输入的时间序列数据点，通常为资产的收盘价。`k` 表示在当前计算窗口内的第 `k` 个数据点 (从1到N)。
*   `N`: 计算线性回归的周期长度（窗口大小）。
*   `x_i`: 时间序数变量，用于线性回归计算。对于窗口内 `N` 个数据点，`x_i` 取值为 `0, 1, 2, \dots, N-1`。`x_i` 对应于窗口中的第 `i+1` 个数据点。
*   `m`: 线性回归线的斜率。
*   `b`: 线性回归线的截距。
*   `LRV_t`: 在时间点 `t` 计算得到的线性回归预测值。

【4. 函数与方法说明】

1.  **求和 (Summation, `\sum`)**:
    标准的求和运算。

2.  **时间序数 `x` 的相关求和**:
    对于时间序数 `x_i = i`，其中 `i` 从 `0` 到 `N-1`：
    *   `SumX = \sum_{i=0}^{N-1} x_i = \sum_{i=0}^{N-1} i = \frac{N(N-1)}{2}`
    *   `SumXSqr = \sum_{i=0}^{N-1} x_i^2 = \sum_{i=0}^{N-1} i^2 = \frac{(N-1)N(2N-1)}{6}`

3.  **线性回归 (Linear Regression)**:
    一种统计方法，用于找到一组数据点之间的最佳拟合直线。它通过最小化每个数据点到直线的垂直距离的平方和（即最小二乘法）来实现。
    给定 `N` 个数据对 `(x_0, P_1), (x_1, P_2), \dots, (x_{N-1}, P_N)`，回归直线为 `\hat{P}(x) = b + m \cdot x`。
    斜率 `m` 和截距 `b` 通过上述核心公式计算得到。

【5. 计算步骤】

对于每个计算日 `t`（从数据序列的第 `N` 天开始）：
1.  **数据准备**:
    获取当前日 `t` 及之前的 `N-1` 日的输入数据 `P`，形成一个长度为 `N` 的数据窗口：`P_{window} = [P_{t-N+1}, P_{t-N+2}, \dots, P_t]`。
    为了计算方便，我们将窗口内的数据重新索引为 `P'_0, P'_1, \dots, P'_{N-1}`，其中 `P'_j = P_{t-N+1+j}`。
    定义对应的时间序数 `x_j = j`，其中 `j = 0, 1, \dots, N-1`。

2.  **计算必要的求和项**:
    a.  计算时间序数 `x` 的和 `SumX`:
        `SumX = \sum_{j=0}^{N-1} x_j = \frac{N(N-1)}{2}`
    b.  计算时间序数 `x` 的平方和 `SumXSqr`:
        `SumXSqr = \sum_{j=0}^{N-1} x_j^2 = \frac{N(N-1)(2N-1)}{6}`
    c.  计算窗口内数据 `P'` 的和 `SumY` (对应源码中的 `SumY`):
        `SumY = \sum_{j=0}^{N-1} P'_j`
    d.  计算 `x_j` 与 `P'_j` 乘积的和 `SumXY` (对应源码中的 `optInTimePeriod * SumXY - SumX * SumY` 分子项，和`Divisor`的计算方式，经过代数化简后与标准公式等价，这里直接使用标准定义表述)：
        源码中 `SumXY` 的计算方式是 `SumXY_{talib} = \sum_{j=0}^{N-1} P'_j \cdot ((N-1)-j)`。
        为了清晰，我们使用标准公式中的 `SumXY_{std} = \sum_{j=0}^{N-1} P'_j \cdot j`。
        (在源码分析中已证明，虽然Ta-lib的 `SumXY` 计算项的构造方式不同，但最终计算出的 `m` 和 `b` 与标准定义一致。)
        所以，这里我们按标准方法计算：
        `SumXY_{std} = \sum_{j=0}^{N-1} (x_j \cdot P'_j)`

3.  **计算回归参数 `m` 和 `b`**:
    a.  计算分母项 `Denominator = N \cdot SumXSqr - (SumX)^2`。
        注意：源码中的 `Divisor = SumX^2 - N \cdot SumXSqr`，所以 `Divisor = -Denominator`。
    b.  计算斜率 `m`:
        `m = \frac{N \cdot SumXY_{std} - SumX \cdot SumY}{Denominator}`
        (这与源码中 `m = (N \cdot SumXY_{talib} - SumX \cdot SumY) / Divisor` 等价。)
    c.  计算截距 `b`:
        `b = \frac{SumY - m \cdot SumX}{N}`

4.  **计算最终因子值**:
    因子值 `LRV_t` 是回归直线在时间序数 `x = N-1` (对应窗口中的最后一个数据点 `P'_ {N-1}` 或 `P_t`) 处的值:
    `LRV_t = b + m \cdot (N-1)`

5.  输出 `LRV_t` 作为当日的因子值。

【6. 备注与参数说明】

*   **参数 `N` (optInTimePeriod)**: 周期长度是该因子的核心参数。常见的取值如14天、20天等。较短的周期对价格变化更敏感，较长的周期则更平滑。
*   **输入数据 (inReal)**: 通常使用收盘价序列。也可以应用于其他时间序列数据，如开盘价、最高价、最低价等。
*   **数据预处理**: 至少需要 `N` 期数据才能开始计算第一个因子值。因此，输出序列会比输入序列短 `N-1` 期。
*   **用途**: 该因子值代表了基于过去N期数据拟合的趋势线在当前时刻的理论值。它可以用来判断当前价格相对于短期趋势的位置，或作为趋势跟踪系统的一部分。
*   **与特定TA-Lib函数的对应关系**:
    *   当前因子 `LRV_t` 对应 `TA_LINEARREG` 的输出: `b + m \cdot (N-1)`
    *   若要获取回归斜率 `m`，对应 `TA_LINEARREG_SLOPE`。
    *   若要获取回归截距 `b`，对应 `TA_LINEARREG_INTERCEPT`。
    *   若要获取下一期的预测值 (Time Series Forecast)，对应 `TA_TSF`: `b + m \cdot N`。
    此卡片仅描述 `TA_LINEARREG` 的逻辑。

【因子信息结束】===============================================================