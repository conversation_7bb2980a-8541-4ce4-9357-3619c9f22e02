【因子信息开始】===============================================================

【因子编号和名称】

因子编号: F001
因子名称: 周期内最小值 (Period Minimum, MIN)

【1. 因子名称详情】

因子F001: 周期内最小值 (Period Minimum, MIN)，亦可称为N日最低价。它计算在过去指定周期 N 内输入序列的最小值。

【2. 核心公式】

对于时间序列数据 $P = \{P_1, P_2, \dots, P_T\}$，在时刻 $t$ 的 $N$ 周期最小值 $MIN_t(N)$ 定义为：

$MIN_t(N) = \min(P_{t-N+1}, P_{t-N+2}, \dots, P_t)$

其中：
*   $MIN_t(N)$ 是在时刻 $t$ 计算得到的过去 $N$ 个周期（包括当前周期 $t$）的最小值。
*   $P_i$ 是在时刻 $i$ 的输入数据值。
*   $N$ 是计算最小值的周期长度。
*   $\min(\cdot)$ 函数返回括号内系列数值中的最小值。
*   公式成立的前提是 $t \ge N$。

【3. 变量定义】

*   $P_t$: 时刻 $t$ 的输入数据值。这通常是资产的价格（如收盘价、最低价），但也可以是其他任何数值型时间序列数据。
*   $N$: 计算最小值的周期长度（或称为窗口期）。这是一个正整数参数，通常 $N \ge 2$。
*   $MIN_t(N)$: 在时刻 $t$ 计算得到的，从时刻 $t-N+1$ 到时刻 $t$ （共 $N$ 个数据点）的输入数据的最小值。

【4. 函数与方法说明】

*   **最小值函数 ($\min$)**:
    该函数接受一组数值作为输入，并返回这组数值中最小的一个。例如, $\min(5, 2, 8, 3) = 2$。

【5. 计算步骤】

1.  **数据准备**:
    *   获取输入时间序列数据 $P = \{P_1, P_2, \dots, P_T\}$。
    *   确定参数——周期长度 $N$。

2.  **初始化**:
    *   由于计算 $N$ 周期最小值至少需要 $N$ 个数据点，因此因子值的输出将从输入序列的第 $N$ 个数据点开始。即，第一个可以计算出的 $MIN$ 值是 $MIN_N(N)$。
    *   设置一个变量 `current_min` 用于存储当前窗口的最小值，以及一个变量 `min_index` 用于存储当前最小值的在原始输入序列中的索引。

3.  **计算第一个 $N$ 周期最小值**:
    *   对于第一个有效的计算点，即时刻 $t=N$：
    *   考察窗口 $P_1, P_2, \dots, P_N$。
    *   遍历这个窗口中的所有数据点，找到最小值并将其赋给 `current_min`，同时记录其在输入序列中的索引到 `min_index`。
    *   $MIN_N(N) = \text{current\_min}$。

4.  **滑动窗口计算后续的最小值**:
    *   对于从 $t = N+1$ 到 $T$ 的每一个后续时刻：
        a.  **窗口滑动**: 当前的计算窗口是从 $P_{t-N+1}$ 到 $P_t$。
        b.  **获取新数据点**: 新进入窗口的数据点是 $PT_t$。
        c.  **检查旧的最小值是否仍在窗口内**:
            *   如果 `min_index` 小于当前窗口的起始索引（即 `min_index < t-N+1`），这意味着之前记录的最小值已经滑出当前窗口。此时，必须在当前窗口 $P_{t-N+1}, \dots, P_t$ 内重新全面搜索最小值。将找到的最小值更新到 `current_min`，并将其在输入序列中的索引更新到 `min_index`。
            *   如果 `min_index` 仍然在当前窗口内（即 `min_index \ge t-N+1`），则比较新进入窗口的数据点 $PT_t$ 与 `current_min`：
                *   如果 $PT_t \le \text{current\_min}$，则 $PT_t$ 成为新的最小值。更新 `current_min = PT_t`，并且更新 `min_index = t`。
                *   否则（如果 $PT_t > \text{current\_min}$），`current_min` 和 `min_index` 保持不变，因为旧的最小值仍然是当前窗口内最小的。
        d.  **输出**: 将当前的 `current_min` 作为时刻 $t$ 的因子值 $MIN_t(N)$。

5.  **重复**: 重复步骤4，直到处理完输入序列中的所有数据点。

【6. 备注与参数说明】

*   **参数 `optInTimePeriod` (周期长度 $N$)**: 这是该因子最重要的参数。它定义了回顾期的长度。
    *   取值范围：通常为 $\ge 2$ 的整数。在TALIB中，默认值为30，允许范围为2到100000。
    *   选择：较短的周期（如5, 10）对近期价格变动更敏感，可用于捕捉短期支撑。较长的周期（如20, 50, 200）则反映了更长期的支撑水平。
*   **数据预处理**:
    *   输入数据应为数值型时间序列。
    *   通常不需要特殊的数据预处理，但应确保数据序列中没有`NaN`或极端异常值，这些可能会影响最小值的正确计算。
*   **输出延迟**: 第一个有效的因子值出现在输入序列的第 $N$ 个周期。因此，输出序列的长度会比输入序列短 $N-1$ 个周期。
*   **应用场景**:
    *   常用于构建唐奇安通道 (Donchian Channels) 的下轨。
    *   可作为动态的支撑位参考。
    *   可用于设置基于波动性的止损位。
    *   与其他指标结合，判断市场是否处于超卖或支撑区域。
*   **效率考量**: 上述计算步骤中描述的“检查旧的最小值是否仍在窗口内”是对朴素的每次都重新扫描整个窗口的优化。如果旧的最小值移出窗口，则需要全窗口扫描；否则，仅需与新值比较。

【因子信息结束】===============================================================