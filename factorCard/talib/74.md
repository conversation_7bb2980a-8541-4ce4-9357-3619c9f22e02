【因子信息开始】===============================================================

【因子编号和名称】

因子编号: F_001
因子中文名称: 周期最高值位置 (Index of Highest Value, MAXINDEX)

【1. 因子名称详情】

因子名称：周期最高值位置 (Index of Highest Value, MAXINDEX)。该因子用于识别在过去指定周期内，输入序列中出现最大值的具体位置（索引）。

【2. 核心公式】

给定一个输入时间序列 $S = \{s_0, s_1, \dots, s_M\}$ 和一个时间周期 $N$。
在每个时间点 $t$（其中 $t \ge N-1$），因子 $MAXINDEX_t$ 的计算如下：

$MAXINDEX_t = \underset{j \in \{t-N+1, \dots, t\}}{\text{argmax*}} (s_j)$

其中，$\text{argmax*}$ 操作定义为：
寻找在索引区间 $[t-N+1, t]$ 内使得 $s_j$ 达到最大值的索引 $j$。如果存在多个这样的索引（即多个周期具有相同的最大值），则选择其中最大的索引值（即最接近当前时间点 $t$ 的那个）。

【3. 变量定义】

*   $S$: 输入的时间序列，例如每日的收盘价、最高价等。
*   $s_t$: 时间序列 $S$ 在时间点 $t$ 的值。时间点 $t$ 是从0开始的索引。
*   $N$: 计算周期，表示回顾的窗口大小（包含当前时间点）。$N \ge 2$。
*   $t$: 当前计算因子的时间点（基于0的索引）。
*   $MAXINDEX_t$: 在时间点 $t$ 计算得到的因子值。它表示在包含 $t$ 在内的过去 $N$ 个时间周期内（即从 $t-N+1$ 到 $t$），序列 $S$ 取得最大值的原始索引。这个索引是相对于整个输入序列 $S$ 的起始位置的。

【4. 函数与方法说明】

*   $\text{argmax*}_{j \in \{L, \dots, R\}} (X_j)$:
    这是一个自定义的 "取最大值参数" 函数。它考察序列 $X$ 在索引范围 $[L, R]$ 内的所有值 $\{X_L, X_{L+1}, \dots, X_R\}$。
    该函数返回使得 $X_j$ 达到最大值的索引 $j$。
    **关键细节**: 如果在考察的窗口内有多个不同的时间点 $j_1, j_2, \dots, j_k$ 都取得了相同的最大值，即 $X_{j_1} = X_{j_2} = \dots = X_{j_k} = \max(X_L, \dots, X_R)$，则该函数返回这些索引中最大的一个，即最靠右（最接近 $R$）的那个索引。例如，如果 $X_2=10, X_5=10$ 是窗口 $[1,5]$ 的最大值，则 $\text{argmax*}_{j \in \{1, \dots, 5\}} (X_j)$ 返回 5。

【5. 计算步骤】

1.  **数据准备**:
    *   获取输入时间序列 $S = \{s_0, s_1, s_2, \dots, s_M\}$。
    *   确定参数：时间周期 $N$。

2.  **确定有效计算起始点**:
    由于需要至少 $N$ 个数据点来形成第一个完整的计算窗口，因此第一个有效的因子值 $MAXINDEX_t$ 可以在 $t = N-1$ 时刻计算。对于 $t < N-1$ 的时刻，因子值无定义。

3.  **迭代计算**:
    从 $t = N-1$ 开始，遍历到序列的末尾 $M$：
    *   定义当前观察窗口的起始索引 $W_{start} = t - N + 1$ 和结束索引 $W_{end} = t$。
    *   初始化当前窗口内的最高值 `current_max_value` 为一个极小值（或窗口内第一个值 $s_{W_{start}}$）。
    *   初始化当前窗口内最高值对应的索引 `current_max_index` 为 $W_{start}$。
    *   遍历索引 $j$ 从 $W_{start}$ 到 $W_{end}$（即当前窗口内的所有数据点）：
        *   获取 $s_j$。
        *   如果 $s_j \ge \text{current\_max\_value}$ (注意是大于等于，确保当有相同最大值时取较新的索引):
            *   更新 $\text{current\_max\_value} = s_j$。
            *   更新 $\text{current\_max\_index} = j$。
    *   当前时间点 $t$ 的因子值为 $MAXINDEX_t = \text{current\_max\_index}$。

    **为提高效率，可以采用如下优化思路（与源码逻辑一致）：**
    设 `highest_seen_value` 为上一个窗口计算出的最高值，`index_of_highest` 为其对应的索引。
    对于当前时间点 $t$：
    a.  当前处理的数据值为 $s_t$。
    b.  窗口的起始索引 `window_start_index = t - N + 1`。
    c.  **检查之前的最高值是否仍在当前窗口内**：
        If `index_of_highest < window_start_index`:
        这意味着之前记录的最高值已经滑出当前窗口。此时必须重新扫描整个当前窗口 $[window\_start\_index, t]$ 来找到新的最高值及其索引。
        i.  设置 `index_of_highest = window_start_index`。
        ii. 设置 `highest_seen_value = s_{window\_start\_index}`。
        iii. 从 `k = window_start_index + 1` 遍历到 $t$:
            如果 $s_k > \text{highest\_seen\_value}$:
                更新 `highest_seen_value = s_k`。
                更新 `index_of_highest = k`。
    d.  **如果之前的最高值仍在窗口内，或者已通过c步骤更新**：
        比较当前值 $s_t$ 与 `highest_seen_value`：
        If $s_t \ge \text{highest\_seen\_value}$:
        当前值 $s_t$ 是新的（或并列的）最高值。
        更新 `highest_seen_value = s_t`。
        更新 `index_of_highest = t`。
    e.  此时的 `index_of_highest` 即为 $MAXINDEX_t$。

4.  **输出**:
    $MAXINDEX_t$ 序列即为所求。注意，输出的索引值是相对于原始输入序列 $S$ 的绝对索引起始的（例如，如果 $S$ 从索引0开始，则输出的也是0基索引）。

【6. 备注与参数说明】

*   **参数 $N$ (optInTimePeriod)**: 时间周期 $N$ 是核心参数，用于定义回顾窗口的长度。其取值范围通常建议从2到100000。常见的选择有5, 10, 20, 30天等，具体取决于分析的资产和策略周期。
*   **输出值**: 因子输出的是一个整数，代表在过去 $N$ 期内输入序列达到最大值的原始数据点索引。如果输入序列的索引从0开始，那么输出的索引也是从0开始的。
*   **数据预处理**: 输入序列中不应包含无效值（如NaN）。如果存在，可能导致不可预测的结果。
*   **无数据期 (Lookback Period)**: 对于时间序列的前 $N-1$ 个数据点，由于没有足够的历史数据形成一个完整的长度为 $N$ 的窗口，因此这些点无法计算出有效的因子值。第一个有效的因子值从第 $N-1$ 个索引（如果从0开始计数）的数据点开始产生。
*   **用途**: 此因子常用于识别阶段性的高点出现位置，可作为趋势分析、突破策略或与其他指标结合构建交易信号的辅助工具。例如，可以观察最高值索引是否持续更新，或是否停留在一个旧的索引上。

【因子信息结束】===============================================================