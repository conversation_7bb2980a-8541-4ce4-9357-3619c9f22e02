【因子信息开始】===============================================================

【因子编号和名称】
因子编号: F001
因子中文名称: 相对强弱指数 (Relative Strength Index, RSI)

【1. 因子名称详情】
F001: 相对强弱指数 (Relative Strength Index, RSI)，是一种用于衡量近期价格变动的速度和变化，以评估股票或其他资产价格超买或超卖状态的技术分析动量指标。

【2. 核心公式】
RSI的计算首先需要定义相对强度 (RS, Relative Strength)，然后将其转换为0到100范围内的指数。

1.  价格变化:
    `\Delta P_t = P_t - P_{t-1}`

2.  上涨幅度 (Gain) 和下跌幅度 (Loss):
    `\text{Gain}_t = \begin{cases} \Delta P_t & \text{if } \Delta P_t > 0 \\ 0 & \text{if } \Delta P_t \le 0 \end{cases}`
    `\text{Loss}_t = \begin{cases} |\Delta P_t| & \text{if } \Delta P_t < 0 \\ 0 & \text{if } \Delta P_t \ge 0 \end{cases}`

3.  平均上涨幅度 (`\text{AvgGain}_t`) 和平均下跌幅度 (`\text{AvgLoss}_t`) 的计算 (采用Wilder平滑法):
    *   对于第一个周期 (t=N, N为时间窗口长度): `\text{AvgGain}_N` 和 `\text{AvgLoss}_N` 是过去N个周期内`Gain`和`Loss`的简单算术平均值。
        `\text{AvgGain}_N = \frac{1}{N} \sum_{i=1}^{N} \text{Gain}_i`
        `\text{AvgLoss}_N = \frac{1}{N} \sum_{i=1}^{N} \text{Loss}_i`
    *   对于后续周期 (t > N):
        `\text{AvgGain}_t = \frac{(\text{AvgGain}_{t-1} \times (N-1)) + \text{Gain}_t}{N}`
        `\text{AvgLoss}_t = \frac{(\text{AvgLoss}_{t-1} \times (N-1)) + \text{Loss}_t}{N}`

4.  相对强度 (RS):
    `RS_t = \frac{\text{AvgGain}_t}{\text{AvgLoss}_t}`
    (注意：如果 `\text{AvgLoss}_t` 为0，则处理方式见计算步骤)

5.  相对强弱指数 (RSI):
    `RSI_t = 100 - \frac{100}{1 + RS_t}`
    该公式等价于 (且在实现中更常用，尤其为了处理 `\text{AvgLoss}_t = 0` 的情况):
    `RSI_t = 100 \times \frac{\text{AvgGain}_t}{\text{AvgGain}_t + \text{AvgLoss}_t}`
    当 `\text{AvgGain}_t + \text{AvgLoss}_t = 0` 时 (即连续N期价格无波动)，`RSI_t` 定义为0。

【3. 变量定义】
*   `P_t`: `t`时刻的价格 (通常为收盘价)。
*   `P_{t-1}`: `t-1`时刻的价格。
*   `\Delta P_t`: `t`时刻的价格变化量。
*   `N`: 计算RSI的时间窗口长度 (例如14天)。
*   `\text{Gain}_t`: 在`t`时刻的上涨幅度。如果价格上涨，则为价格上涨的绝对值；否则为0。
*   `\text{Loss}_t`: 在`t`时刻的下跌幅度。如果价格下跌，则为价格下跌的绝对值；否则为0。
*   `\text{AvgGain}_t`: 在`t`时刻，基于N周期的平均上涨幅度 (使用Wilder平滑法)。
*   `\text{AvgLoss}_t`: 在`t`时刻，基于N周期的平均下跌幅度 (使用Wilder平滑法)。
*   `RS_t`: 在`t`时刻的相对强度。
*   `RSI_t`: 在`t`时刻的相对强弱指数。

【4. 函数与方法说明】
*   **价格变化 (`\Delta P_t`)**: 当前周期的价格减去前一个周期的价格。
    `\Delta P_t = P_t - P_{t-1}`
*   **上涨幅度 (`\text{Gain}_t`)**: 取价格变化的正值部分。
    `\text{Gain}_t = \max(0, \Delta P_t)`
*   **下跌幅度 (`\text{Loss}_t`)**: 取价格变化的负值部分的绝对值。
    `\text{Loss}_t = \max(0, - \Delta P_t)` (等价于 `|\min(0, \Delta P_t)|`)
*   **Wilder平滑移动平均 (Wilder's Smoothing Method / SMMA)**:
    这是一种指数平滑的特殊形式。对于一个时间序列 `X`，其N周期Wilder平滑移动平均 `SMMA(X, N)_t` 计算如下：
    1.  第一个值 `SMMA(X, N)_N = \frac{1}{N} \sum_{i=1}^{N} X_i` (N周期简单移动平均)
    2.  后续值 `SMMA(X, N)_t = \frac{(SMMA(X, N)_{t-1} \times (N-1)) + X_t}{N}` for `t > N`
    此方法用于计算 `AvgGain` 和 `AvgLoss`。

【5. 计算步骤】
1.  **数据准备**: 准备至少 `N+1` 期的时间序列价格数据 `P_0, P_1, \ldots, P_k`。参数N为时间窗口。
2.  **计算价格变化**: 对于 `t` 从 1到 `k`，计算每日价格变化 `\Delta P_t = P_t - P_{t-1}`。这将产生 `k` 个价格变化值。
3.  **计算上涨和下跌幅度**: 对于 `t` 从 1到 `k`，根据 `\Delta P_t` 计算 `\text{Gain}_t` 和 `\text{Loss}_t`。
    *   `\text{Gain}_t = \max(0, \Delta P_t)`
    *   `\text{Loss}_t = \max(0, -\Delta P_t)`
4.  **计算初始平均上涨和下跌幅度**:
    第一个RSI值可以在有N个价格变化数据后计算（即在时间点 `t=N`，对应价格 `P_N`）。
    计算时间窗口内 (从第1个价格变化到第N个价格变化，即 `\text{Gain}_1 \ldots \text{Gain}_N` 和 `\text{Loss}_1 \ldots \text{Loss}_N`) 的简单算术平均值：
    `\text{AvgGain}_N = \frac{1}{N} \sum_{i=1}^{N} \text{Gain}_i`
    `\text{AvgLoss}_N = \frac{1}{N} \sum_{i=1}^{N} \text{Loss}_i`
5.  **计算第一个RSI值 (`RSI_N`)**: (对应数据点 `P_N`)
    令 `SumAvg = \text{AvgGain}_N + \text{AvgLoss}_N`。
    *   如果 `SumAvg` 不为0 (即 `!TA_IS_ZERO(SumAvg)`):
        `RSI_N = 100 \times \frac{\text{AvgGain}_N}{SumAvg}`
    *   如果 `SumAvg` 为0 (意味着过去N期价格无任何上涨或下跌，即所有 `\text{Gain}_i` 和 `\text{Loss}_i` 都为0):
        `RSI_N = 0`
6.  **迭代计算后续的RSI值**: 对于 `t` 从 `N+1` 到 `k`：
    a.  获取当期的 `\text{Gain}_t` 和 `\text{Loss}_t`。
    b.  使用Wilder平滑法更新平均上涨和下跌幅度：
        `\text{AvgGain}_t = \frac{(\text{AvgGain}_{t-1} \times (N-1)) + \text{Gain}_t}{N}`
        `\text{AvgLoss}_t = \frac{(\text{AvgLoss}_{t-1} \times (N-1)) + \text{Loss}_t}{N}`
    c.  计算当前的RSI值 (`RSI_t`):
        令 `SumAvg_t = \text{AvgGain}_t + \text{AvgLoss}_t`。
        *   如果 `SumAvg_t` 不为0:
            `RSI_t = 100 \times \frac{\text{AvgGain}_t}{SumAvg_t}`
        *   如果 `SumAvg_t` 为0:
            `RSI_t = 0`
7.  重复步骤6，直到处理完所有可用的价格数据。第一个有效的RSI值是在拥有 `N` 个价格变化（即 `N+1` 个价格点）之后计算出来的，对应于时间序列中的第 `N` 个索引位置 (0-indexed) 或第 `N+1` 个数据点。

【6. 备注与参数说明】
*   **时间窗口 (N)**: `optInTimePeriod` 参数，通常默认值为14。较短的周期会使RSI更敏感，较长的周期则更平滑。取值范围通常建议大于等于2。
*   **数据输入**: 通常使用收盘价 (`inReal`)。
*   **输出范围**: RSI的值域为0到100。
*   **经典解读**:
    *   RSI > 70 通常被认为是超买区域，可能预示价格将回调。
    *   RSI < 30 通常被认为是超卖区域，可能预示价格将反弹。
    *   RSI = 50 通常被认为是中性区域。
*   **数值稳定性**: 使用公式 `RSI_t = 100 \times \frac{\text{AvgGain}_t}{\text{AvgGain}_t + \text{AvgLoss}_t}` 比先计算 `RS_t` 再代入 `100 - \frac{100}{1 + RS_t}` 在 `\text{AvgLoss}_t` 趋近于0时具有更好的数值稳定性。当 `\text{AvgLoss}_t = 0` 且 `\text{AvgGain}_t > 0` 时，前者直接给出100；当两者都为0时，前者通过分母为0的检查给出0。
*   **起始点问题 (`lookbackTotal`)**: 为了计算第一个RSI值，需要 `N` 个周期的价格变化数据。因此，RSI序列的起始点会晚于原始价格序列的起始点。TA-Lib中，RSI的lookback通常等于`optInTimePeriod`。
*   **Metastock兼容性**: 源码中提到了与Metastock计算方式的兼容性选项。Metastock在计算第一个周期的平均涨跌幅时，会假设第一个价格点的前一个价格点与其自身相同（即第一天无涨跌）。本卡片描述的是更普遍的Wilder经典计算方法，即从实际的价格序列开始计算涨跌。

【因子信息结束】===============================================================