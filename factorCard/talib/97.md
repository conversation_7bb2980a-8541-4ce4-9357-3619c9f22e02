【因子信息开始】===============================================================

【因子编号和名称】

因子编号: 001
因子中文名称: 变动率百分比 (Rate of Change Percentage, ROCP)

【1. 因子名称详情】

因子001: 变动率百分比 (Rate of Change Percentage, ROCP)
该指标衡量了当前价格相对于N个周期前价格的变化百分比。

【2. 核心公式】

给定时间序列价格数据 $P$，其在时间点 $t$ 的变动率百分比 $ROCP_t$ 计算公式如下：

$ROCP_t = \frac{P_t - P_{t-N}}{P_{t-N}}$

其中：
如果 $P_{t-N} = 0$，为了避免除以零的错误，通常将 $ROCP_t$ 设为 $0$。

**公式解释：**
该公式计算当前价格 $P_t$ 与 $N$ 个周期前价格 $P_{t-N}$ 之间的差值，然后将这个差值除以 $N$ 个周期前的价格 $P_{t-N}$，得到一个比率，即价格在 $N$ 个周期内的相对变化幅度。

【3. 变量定义】

*   $P_t$: 在时间点 $t$ 的价格（例如：收盘价）。
*   $P_{t-N}$: 在时间点 $t-N$ 的价格，即当前时间点向前追溯 $N$ 个周期的价格。
*   $N$: 计算变动率的时间窗口（周期数），是一个正整数参数。
*   $ROCP_t$: 在时间点 $t$ 计算得到的变动率百分比值。

【4. 函数与方法说明】

该因子主要使用基本的算术运算：
*   **减法**：用于计算当前价格与历史价格的差值 $(P_t - P_{t-N})$。
*   **除法**：用于将价格差值相对于历史价格进行归一化 $\frac{(P_t - P_{t-N})}{P_{t-N}}$。
*   **条件判断**：在计算除法前，需判断除数 $P_{t-N}$ 是否为零。如果为零，则将结果直接设为0，以避免计算错误。

【5. 计算步骤】

1.  **数据准备**:
    *   获取输入的价格时间序列数据，例如每日收盘价 $\{P_0, P_1, P_2, ..., P_M\}$，其中 $M$ 是数据点的总数。
    *   确定参数 $N$ (时间窗口)。

2.  **遍历计算**:
    *   计算 $ROCP$ 指标需要至少 $N+1$ 个数据点。因此，输出的 $ROCP$ 序列将从原始价格序列的第 $N$ 个索引（假设索引从0开始）开始，即对应 $P_N$ 这个价格点。
    *   对于时间序列中的每个有效时间点 $t$ (即 $t \ge N$):
        a.  获取当前价格 $P_t$。
        b.  获取 $N$ 个周期前的价格 $P_{t-N}$。
        c.  **检查除数**：判断 $P_{t-N}$ 是否为 $0.0$。
        d.  **计算ROCP**:
            *   如果 $P_{t-N} \neq 0.0$，则计算 $ROCP_t = (P_t - P_{t-N}) / P_{t-N}$。
            *   如果 $P_{t-N} = 0.0$，则设定 $ROCP_t = 0.0$。
    *   将计算得到的 $ROCP_t$ 值存入结果序列。

3.  **输出结果**:
    *   输出的 $ROCP$ 序列的第一个有效值对应原始输入价格序列的第 $N$ 个价格数据点。
    *   输出序列的长度会比输入序列短 $N$ 个数据点。

**示例**：
假设价格序列 $P = [10, 11, 12, 11, 13, 14]$ 且 $N=2$。
*   $t=0, P_0=10$: 无法计算 $ROCP_0$ (需要 $P_{-2}$)
*   $t=1, P_1=11$: 无法计算 $ROCP_1$ (需要 $P_{-1}$)
*   $t=2, P_2=12$: $P_{t-N} = P_{2-2} = P_0 = 10$. $ROCP_2 = (12 - 10) / 10 = 0.2$.
*   $t=3, P_3=11$: $P_{t-N} = P_{3-2} = P_1 = 11$. $ROCP_3 = (11 - 11) / 11 = 0.0$.
*   $t=4, P_4=13$: $P_{t-N} = P_{4-2} = P_2 = 12$. $ROCP_4 = (13 - 12) / 12 \approx 0.0833$.
*   $t=5, P_5=14$: $P_{t-N} = P_{5-2} = P_3 = 11$. $ROCP_5 = (14 - 11) / 11 \approx 0.2727$.

输出 $ROCP = [0.2, 0.0, 0.0833, 0.2727]$

【6. 备注与参数说明】

*   **参数 `optInTimePeriod` ($N$)**: 这是该因子的核心参数，代表回溯的周期数。
    *   较短的周期（例如 $N=5$ 或 $N=10$）会对近期的价格变化更为敏感，可能产生更多的交易信号，但噪音也可能更多。
    *   较长的周期（例如 $N=20$ 或 $N=50$）则更能反映中长期的价格趋势变化，信号相对平滑，但可能滞后。
    *   参数 $N$ 的选择通常取决于分析的交易周期和资产特性。
*   **输入数据 (`inReal`)**: 通常使用收盘价，但也可以是开盘价、最高价、最低价、均价 (HL/2, HLC/3, OHLC/4) 等。
*   **值的解读**:
    *   $ROCP > 0$: 表示当前价格高于 $N$ 个周期前的价格，表明价格在过去 $N$ 个周期内呈上涨趋势。
    *   $ROCP < 0$: 表示当前价格低于 $N$ 个周期前的价格，表明价格在过去 $N$ 个周期内呈下跌趋势。
    *   $ROCP = 0$: 表示当前价格等于 $N$ 个周期前的价格，或 $N$ 个周期前的价格为0 (特殊处理)。
    *   $ROCP$ 的绝对值大小代表了价格变化的幅度。
*   **与 ROC 的关系**: 源码注释中提到 `ROC = ((price/prevPrice)-1)*100`。因此，$ROCP = ROC / 100$。这意味着 $ROCP$ 是一个比率值（例如0.05代表5%的增长），而 $ROC$ 是一个百分比值（例如5代表5%的增长）。当前源码实现的是比率值。
*   **数据起始点**: 由于计算需要 $N$ 个周期的历史数据，因此 $ROCP$ 序列的前 $N$ 个值是无法计算的。实际输出会从第 $N$ 个数据点（0-indexed）开始。

【因子信息结束】===============================================================