【因子信息开始】===============================================================

【因子编号和名称】

因子编号: 001A: 阿隆上升 (Aroon Up, AroonUp)

【1. 因子名称详情】

因子1: 阿隆上升 (Aroon Up, AroonUp)

这指标用于衡量价格自N周期内最高价出现以来所经过的周期数占整个窗口N的比例，从而反映上升趋势的强度。

【2. 核心公式】

对于阿隆上升 (Aroon Up):
\[ \text{AroonUp}_t = \left( \frac{N - (\text{当前周期位置} - \text{N周期内最高价位置})}{N} \right) \times 100 \]
或者更直观地：
\[ \text{AroonUp}_t = \left( \frac{N - \text{自N周期内最高价出现以来经过的周期数}}{N} \right) \times 100 \]

【3. 变量定义】

*   \(t\): 当前计算周期（例如，某一天）。
*   \(N\): 计算阿隆指标所选定的时间窗口长度（周期数）。
*   \(\text{AroonUp}_t\): 在周期 \(t\) 计算得到的阿隆上升指标值。
*   \(\text{High}_i\): 在周期 \(i\) 的最高价。
*   \(\text{当前周期位置}\): 当前计算周期 \(t\) 在时间序列中的索引或标识。
*   \(\text{N周期内最高价位置}\): 在从 \(\text{当前周期位置} - N + 1\) 到 \(\text{当前周期位置}\) 这 \(N\) 个周期内，出现最高价的那个周期的位置索引或标识。
*   \(\text{自N周期内最高价出现以来经过的周期数}\): \( \text{当前周期位置} - \text{N周期内最高价位置} \)。

【4. 函数与方法说明】

1. 寻找窗口内的最高价及其位置：
   - 对于当前周期 \(t\)，确定窗口为从 \(t-N+1\) 到 \(t\) 的 \(N\) 个周期。
   - 遍历窗口内每日的最高价 \(\text{High}_i\)，记录最大值及其对应周期位置；当出现相同最高价时通常选择最近出现的周期（索引最大的那个）。

【5. 计算步骤】

数据准备:
1. 获取历史最高价序列: \(H_1, H_2, ..., H_k\)。
2. 设定参数: 时间窗口 \(N\)（例如，14天）。
3. 从第 \(N\) 个数据点开始计算。

计算Aroon Up:
1. 确定当前计算窗口：从 \(t-N+1\) 到 \(t\)。
2. 在窗口内查找最高价及其位置。
3. 计算自最高价出现以来经过的周期数: \( \text{periodsSinceHigh} = t - \text{N周期内最高价位置} \)。
4. 根据公式计算: \( \text{AroonUp}_t = ((N - \text{periodsSinceHigh}) / N) \times 100 \)。

【6. 备注与参数说明】

*   参数 \(N\) 默认值为14，但可根据需求调整为20或25周期；
*   指标值范围为0到100，值越高表示上升趋势较强；
*   数据应确保连续且无缺失；
*   在滚动窗口中，若最高价移出窗口则需重新扫描窗口确认最高价及其位置。



【7. 关联因子补充说明】

【关联因子信息开始】===============================================================

【因子编号和名称】

因子编号: 001A: 阿隆上升 (Aroon Up, AroonUp)
因子编号: 001B: 阿隆下降 (Aroon Down, AroonDown)

【1. 因子名称详情】

*   因子1: 阿隆上升 (Aroon Up, AroonUp)
*   因子2: 阿隆下降 (Aroon Down, AroonDown)

这两个指标共同构成了阿隆指标系统，用于衡量价格达到近期最高点或最低点以来经过的时间周期百分比，从而判断趋势的强度和方向。

【2. 核心公式】

对于**阿隆上升 (Aroon Up)**:
\[ \text{AroonUp}_t = \left( \frac{N - (\text{当前周期位置} - \text{N周期内最高价位置})}{N} \right) \times 100 \]
或者更直观地：
\[ \text{AroonUp}_t = \left( \frac{N - \text{自N周期内最高价出现以来经过的周期数}}{N} \right) \times 100 \]

对于**阿隆下降 (Aroon Down)**:
\[ \text{AroonDown}_t = \left( \frac{N - (\text{当前周期位置} - \text{N周期内最低价位置})}{N} \right) \times 100 \]
或者更直观地：
\[ \text{AroonDown}_t = \left( \frac{N - \text{自N周期内最低价出现以来经过的周期数}}{N} \right) \times 100 \]

【3. 变量定义】

*   \(t\): 当前计算周期（例如，某一天）。
*   \(N\): 计算阿隆指标所选定的时间窗口长度（周期数）。
*   \(\text{AroonUp}_t\): 在周期 \(t\) 计算得到的阿隆上升指标值。
*   \(\text{AroonDown}_t\): 在周期 \(t\) 计算得到的阿隆下降指标值。
*   \(\text{High}_i\): 在周期 \(i\) 的最高价。
*   \(\text{Low}_i\): 在周期 \(i\) 的最低价。
*   \(\text{当前周期位置}\): 当前计算周期 \(t\) 在时间序列中的索引或标识。
*   \(\text{N周期内最高价位置}\): 在从 \(\text{当前周期位置} - N + 1\) 到 \(\text{当前周期位置}\) 这 \(N\) 个周期内，出现最高价的那个周期的位置索引或标识。
*   \(\text{N周期内最低价位置}\): 在从 \(\text{当前周期位置} - N + 1\) 到 \(\text{当前周期位置}\) 这 \(N\) 个周期内，出现最低价的那个周期的位置索引或标识。
*   \(\text{自N周期内最高价出现以来经过的周期数}\): \( \text{当前周期位置} - \text{N周期内最高价位置} \)。这个值最小为0（如果最高价出现在当前周期），最大为 \(N-1\)（如果最高价出现在 \(N\) 个周期前的第一个周期）。
*   \(\text{自N周期内最低价出现以来经过的周期数}\): \( \text{当前周期位置} - \text{N周期内最低价位置} \)。这个值最小为0（如果最低价出现在当前周期），最大为 \(N-1\)（如果最低价出现在 \(N\) 个周期前的第一个周期）。

【4. 函数与方法说明】

计算阿隆指标的核心在于确定在给定的时间窗口 \(N\) 内，最高价和最低价分别出现在哪个周期。

1.  **寻找窗口期内的最高价及其位置**:
    对于当前周期 \(t\)，考察从周期 \(t-N+1\) 到周期 \(t\) 这个包含 \(N\) 个周期的窗口。遍历这个窗口内每日的最高价 \(\text{High}_i\)，找到其中的最大值，并记录这个最大值出现的周期位置（索引）。如果窗口内有多个周期的最高价相同且均为最大值，通常选择最近出现的那个（即周期位置索引最大的那个）。

2.  **寻找窗口期内的最低价及其位置**:
    对于当前周期 \(t\)，考察从周期 \(t-N+1\) 到周期 \(t\) 这个包含 \(N\) 个周期的窗口。遍历这个窗口内每日的最低价 \(\text{Low}_i\)，找到其中的最小值，并记录这个最小值出现的周期位置（索引）。如果窗口内有多个周期的最低价相同且均为最小值，通常选择最近出现的那个（即周期位置索引最大的那个）。

【5. 计算步骤】

假设我们有一个包含每日最高价 (High) 和最低价 (Low) 的时间序列数据。参数为时间窗口长度 \(N\)。

**数据准备**:
1.  获取历史最高价序列: \(H_1, H_2, ..., H_k\)。
2.  获取历史最低价序列: \(L_1, L_2, ..., L_k\)。
3.  设定参数: 时间窗口 \(N\) (例如，14天)。
   计算将从第 \(N\) 个数据点开始，因为需要 \(N\) 个周期的数据来形成第一个计算窗口。

**对于每个计算日 \(t\) (从第 \(N\) 天开始到最后一天):**

**计算Aroon Up**:
1.  **确定当前计算窗口**: 包含从周期 \(t-N+1\) 到周期 \(t\) 的所有数据。
2.  **寻找窗口内的最高价及其位置**:
    a.  初始化一个变量 `highestHighValue` 为窗口内第一个周期的最高价 (\(H_{t-N+1}\))。
    b.  初始化一个变量 `highestHighPosition` 为窗口内第一个周期的位置 (\(t-N+1\))。
    c.  遍历窗口内从周期 \(t-N+2\) 到周期 \(t\) 的每一个周期 \(i\):
        i.  如果当前周期的最高价 \(H_i\) 大于或等于 `highestHighValue`，则更新 `highestHighValue = H_i`，并且更新 `highestHighPosition = i`。
3.  **计算自最高价以来经过的周期数**:
    `periodsSinceHigh = t - highestHighPosition`。
    （注意：如果 `highestHighPosition` 是从0开始的数组索引，而 `t` 也是从0开始的当前索引，则公式是 `t - highestHighPosition`。如果 `t` 代表第 `t` 天，`highestHighPosition` 代表第 `x` 天，那么就是 `t - x`。）
4.  **计算Aroon Up值**:
    `AroonUp_t = ((N - periodsSinceHigh) / N) * 100`。

**计算Aroon Down**:
1.  **确定当前计算窗口** (同上): 包含从周期 \(t-N+1\) 到周期 \(t\) 的所有数据。
2.  **寻找窗口内的最低价及其位置**:
    a.  初始化一个变量 `lowestLowValue` 为窗口内第一个周期的最低价 (\(L_{t-N+1}\))。
    b.  初始化一个变量 `lowestLowPosition` 为窗口内第一个周期的位置 (\(t-N+1\))。
    c.  遍历窗口内从周期 \(t-N+2\) 到周期 \(t\) 的每一个周期 \(i\):
        i.  Если当前周期的最低价 \(L_i\) 小于或等于 `lowestLowValue`，则更新 `lowestLowValue = L_i`，并且更新 `lowestLowPosition = i`。
3.  **计算自最低价以来经过的周期数**:
    `periodsSinceLow = t - lowestLowPosition`。
4.  **计算Aroon Down值**:
    `AroonDown_t = ((N - periodsSinceLow) / N) * 100`。

**优化说明**:
源码中采用了滚动窗口优化的方法：
当计算窗口向前滑动一个周期时（即从 \(t\) 到 \(t+1\)，窗口从 \([t-N+1, t]\) 变为 \([t-N+2, t+1]\)）：
*   检查原窗口的最高价/最低价位置是否仍在新的窗口内。
    *   如果仍在窗口内：只需将新进入周期的最高价/最低价与原窗口的最高价/最低价（及其位置）进行比较，看是否需要更新。
    *   如果已移出窗口：则需要重新扫描整个新窗口来找到新的最高价/最低价及其位置。
这种优化避免了在每个周期都完全重新扫描整个窗口，提高了计算效率。但其核心思想仍然是在当前窗口内寻找最高/最低价及其发生的时间。

【6. 备注与参数说明】

*   **参数选择 (N)**: `optInTimePeriod` 即为时间窗口长度 \(N\)。通常默认值为14个周期，但也可以根据分析的需要调整，例如常用的还有20或25个周期。较短的周期使指标对价格变化更敏感，较长的周期则更平滑。
*   **数据预处理**: 通常不需要特别的数据预处理，直接使用原始的最高价和最低价序列即可。确保数据没有缺失值。
*   **指标范围**: Aroon Up 和 Aroon Down 的值都在0到100之间。
    *   Aroon Up 接近100表示价格近期创下新高，上升趋势强劲。
    *   Aroon Down 接近100表示价格近期创下新低，下降趋势强劲。
    *   两者都较低表示市场处于盘整。
    *   当Aroon Up上穿Aroon Down时，可能预示上涨趋势的开始；反之，当Aroon Down上穿Aroon Up时，可能预示下跌趋势的开始。
*   **起始计算点**: 由于需要 \(N\) 个周期的数据来计算第一个Aroon值，因此输出序列会比输入序列短 \(N-1\) 个点，或者说前 \(N-1\) 个点没有有效的Aroon值。

【关联因子信息结束】===============================================================

【因子信息结束】===============================================================