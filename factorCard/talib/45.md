【因子信息开始】===============================================================

【因子编号和名称】

因子编号: KAMA01: 考夫曼自适应移动平均线 (Kaufman Adaptive Moving Average, KAMA)

【1. 因子名称详情】

考夫曼自适应移动平均线 (Kaufman Adaptive Moving Average, KAMA) 是一种根据市场波动情况自动调整平滑常数的移动平均线。当市场波动较小时，KAMA 的反应会变慢，以滤除噪音；当市场波动较大且趋势明显时，KAMA 的反应会变快，以紧跟趋势。

【2. 核心公式】

KAMA 的计算是递归的，其核心思想类似于指数移动平均线(EMA)，但其平滑常数是动态变化的。

1.  **效率比率 (Efficiency Ratio, ER)**:
    $ER_t = \frac{|\text{Direction}_t|}{\text{Volatility}_t}$
    其中：
    $\text{Direction}_t = \text{Price}_t - \text{Price}_{t-n}$
    $\text{Volatility}_t = \sum_{i=t-n+1}^{t} |\text{Price}_i - \text{Price}_{i-1}|$

    注意：如果 $\text{Volatility}_t = 0$，则 $ER_t = 1$。

2.  **平滑常数 (Smoothing Constant, SC)**:
    $SC_t = ER_t \cdot (\alpha_{fast} - \alpha_{slow}) + \alpha_{slow}$
    其中：
    $\alpha_{fast} = \frac{2}{N_{fast} + 1}$
    $\alpha_{slow} = \frac{2}{N_{slow} + 1}$

3.  **自适应平滑系数 ($\alpha_{adaptive}$)**:
    $\alpha_{adaptive\_t} = (SC_t)^2$

4.  **考夫曼自适应移动平均线 (KAMA)**:
    $KAMA_t = KAMA_{t-1} + \alpha_{adaptive\_t} \cdot (\text{Price}_t - KAMA_{t-1})$
    这等价于：
    $KAMA_t = \alpha_{adaptive\_t} \cdot \text{Price}_t + (1 - \alpha_{adaptive\_t}) \cdot KAMA_{t-1}$

【3. 变量定义】

*   $t$: 当前时间点（例如，天）。
*   $\text{Price}_t$: $t$ 时刻的输入价格（通常为收盘价）。
*   $n$: 计算效率比率 ER 的周期长度。
*   $\text{Direction}_t$: $t$ 时刻的 $n$ 周期方向性变动，即当前价格与 $n$ 周期前价格的差值。
*   $\text{Volatility}_t$: $t$ 时刻的 $n$ 周期波动性，即过去 $n$ 个单周期价格绝对变动之和。
*   $ER_t$: $t$ 时刻的效率比率，衡量价格在特定方向上移动的效率。取值范围为 [0, 1]。
*   $N_{fast}$: 快速指数移动平均线的周期，用于计算 $\alpha_{fast}$。
*   $N_{slow}$: 慢速指数移动平均线的周期，用于计算 $\alpha_{slow}$。
*   $\alpha_{fast}$: 对应 $N_{fast}$ 周期的指数移动平均线的平滑系数。
*   $\alpha_{slow}$: 对应 $N_{slow}$ 周期的指数移动平均线的平滑系数。
*   $SC_t$: $t$ 时刻的中间平滑常数，根据 $ER_t$ 在 $\alpha_{slow}$ 和 $\alpha_{fast}$ 之间调整。
*   $\alpha_{adaptive\_t}$: $t$ 时刻的最终自适应平滑系数，为 $SC_t$ 的平方。
*   $KAMA_t$: $t$ 时刻的考夫曼自适应移动平均值。
*   $KAMA_{t-1}$: $t-1$ 时刻的考夫曼自适应移动平均值。

【4. 函数与方法说明】

*   $|\cdot|$: 绝对值函数。
*   $\sum$: 求和函数。
*   指数移动平均线 (EMA) 的思想：KAMA 的更新公式与 EMA 类似，即当前值是前一个平均值和当前价格的加权平均。权重（平滑系数）是自适应的。

【5. 计算步骤】

1.  **数据准备**:
    获取历史价格序列 $\text{Price}_0, \text{Price}_1, ..., \text{Price}_M$。

2.  **参数设定**:
    *   ER 计算周期 $n$ (对应源码中的 `optInTimePeriod`，默认值为 30)。
    *   快速 EMA 周期 $N_{fast}$ (源码中固定为 2)。
    *   慢速 EMA 周期 $N_{slow}$ (源码中固定为 30)。

3.  **计算基础平滑系数**:
    *   $\alpha_{fast} = \frac{2}{N_{fast} + 1}$
    *   $\alpha_{slow} = \frac{2}{N_{slow} + 1}$

4.  **初始化 KAMA**:
    *   计算第一个 $KAMA$ 值至少需要 $n$ 个周期的价格数据来计算第一个有效的 $ER$。
    *   第一个 $KAMA$ 值 $KAMA_n$ (对应价格 $\text{Price}_n$) 的计算需要 $KAMA_{n-1}$。
    *   源码中的初始化方式较为复杂，涉及到不稳定期的处理。一个简化的实践方法是：
        *   将初始的 $KAMA_{n-1}$ 设定为 $\text{Price}_{n-1}$。
        *   或者，前 $n$ 个周期的 KAMA 值可以设定为对应价格的简单移动平均 (SMA)。例如，$KAMA_n = \text{SMA}(\text{Price}, n)$ for $\text{Price}_0 \dots \text{Price}_n$。然而，更贴近源码的递归思想是使用前一天的价格作为递归的起点。
        *   对于第一个可计算的 $KAMA$ 值 (即 $KAMA_n$，对应 $\text{Price}_n$)：
            *   $KAMA_{n-1}$ (前一个 KAMA 值) 可设为 $\text{Price}_{n-1}$。

5.  **迭代计算 (从 $t = n$ 开始)**:
    对于每个时间点 $t$ (从第 $n$ 个数据点开始，因为计算 $\text{Direction}_t$ 和 $\text{Volatility}_t$ 需要 $n$ 期数据):
    a.  **计算方向性变动 (Direction)**:
        $\text{Direction}_t = \text{Price}_t - \text{Price}_{t-n}$
    b.  **计算波动性 (Volatility)**:
        $\text{Volatility}_t = \sum_{i=t-n+1}^{t} |\text{Price}_i - \text{Price}_{i-1}|$
        (这是一个滑动窗口求和。在每一步，从窗口中移除最旧的 $|\text{Price}_{t-n} - \text{Price}_{t-n-1}|$ 并加入最新的 $|\text{Price}_t - \text{Price}_{t-1}|$)
    c.  **计算效率比率 (ER)**:
        如果 $\text{Volatility}_t = 0$:
        $ER_t = 1$
        (源码中还有一种情况 `sumROC1 <= periodROC` (即 $\text{Volatility}_t \le |\text{Direction}_t|$，因 $\text{Volatility}_t \ge 0$, 所以实际是 $\text{Volatility}_t \le \text{Direction}_t$ 且 $\text{Direction}_t >0$) 时也会设 $ER_t=1$。这种情况通常发生在所有单周期价格变动都同向，此时 $|\text{Direction}_t| = \text{Volatility}_t$，所以 $ER_t=1$。)
        否则:
        $ER_t = \frac{|\text{Direction}_t|}{\text{Volatility}_t}$
    d.  **计算中间平滑常数 (SC)**:
        $SC_t = ER_t \cdot (\alpha_{fast} - \alpha_{slow}) + \alpha_{slow}$
    e.  **计算自适应平滑系数 ($\alpha_{adaptive}$)**:
        $\alpha_{adaptive\_t} = (SC_t)^2$
    f.  **计算 KAMA**:
        $KAMA_t = KAMA_{t-1} + \alpha_{adaptive\_t} \cdot (\text{Price}_t - KAMA_{t-1})$
        其中 $KAMA_{t-1}$ 是上一个计算得到的 KAMA 值。对于 $t=n$ 时的第一个 KAMA 计算， $KAMA_{n-1}$ 采用步骤4中初始化的值。

6.  **不稳定期处理**:
    由于 KAMA 是递归计算的，初始几个值可能不够稳定。源码中会跳过一个 "不稳定周期" 的输出。在实际应用中，这意味着需要比 $n$ 更多的数据点来获得一个可靠的 KAMA 序列起始点。通常，额外需要大约 $N_{slow}$ 个周期来稳定。

【6. 备注与参数说明】

*   **参数选择**:
    *   `optInTimePeriod` ($n$): 这是 KAMA 的主要参数，默认为30。它决定了计算效率比率 ER 的回顾期。较短的周期使 KAMA 更敏感，较长的周期使其更平滑。
    *   `N_{fast}` (快速 EMA 周期): 源码中固定为2。 这是 KAMA 能达到的最快反应速度对应的 EMA 周期。
    *   `N_{slow}` (慢速 EMA 周期): 源码中固定为30。 这是 KAMA 能达到的最慢反应速度对应的 EMA 周期。
    *   Kaufman 建议的 $N_{fast}=2$ 和 $N_{slow}=30$ 是经典参数。可以根据需要调整这些参数以改变 KAMA 的行为特性，尽管 TALib 的实现将它们硬编码为常量。
*   **平滑常数的平方**: 将 $SC_t$ 平方得到 $\alpha_{adaptive\_t}$，这使得 KAMA 在趋势强劲 (ER 接近 1) 时能更快地跟踪价格，而在趋势疲软 (ER 接近 0) 时更慢，放大了 ER 的影响。
*   **数据预处理**: 通常使用收盘价作为输入价格。确保输入数据序列没有缺失值。
*   **用途**: KAMA 旨在提供一个相比传统移动平均线更少滞后且能更好滤除市场噪音的趋势跟踪指标。
*   **初始值问题**: 第一个 KAMA 值的计算依赖于之前的值。TALib 通过一个初始的种子值和一段“不稳定周期”的计算来稳定输出。在自行实现时，需要注意初始值的设定对早期 KAMA 值的影响。通常，经过 $N_{slow}$ 个周期的迭代后，初始值的影响会显著减小。

【因子信息结束】===============================================================