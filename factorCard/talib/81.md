【因子信息开始】===============================================================

【因子编号和名称】
因子编号: FC.MAX.01: 周期最高价 (Period Maximum, MAX)

【1. 因子名称详情】
因子1: 周期最高价 (Period Maximum, MAX)。该因子用于计算在过去指定数量的周期内，某一输入数据序列出现过的最高数值。

【2. 核心公式】
对于时间点 $t$ 和回顾期 $N$，周期最高价 $MAX_t$ 的计算公式如下：
$MAX_t(S, N) = \max(S_{t-N+1}, S_{t-N+2}, \dots, S_t)$

【3. 变量定义】
*   $S_t$: 在时间点 $t$ 的输入数据序列的数值（例如：某资产的收盘价、最高价，或任何其他数值型序列）。
*   $N$: 计算最高价所回顾的周期长度（时间窗口大小）。这是一个正整数，通常 $N \ge 2$。
*   $MAX_t(S, N)$: 在时间点 $t$（包含当前点），向前回顾 $N$ 个周期的数据点 $S_{t-N+1}$ 到 $S_t$ 中所出现的最高值。

【4. 函数与方法说明】
*   $\max(x_1, x_2, \dots, x_k)$: 最大值函数。此函数返回其所有参数 $x_1, x_2, \dots, x_k$ 中的最大值。

【5. 计算步骤】
1.  **数据准备**:
    *   准备一个数值型时间序列 $S = \{S_0, S_1, \dots, S_{M-1}\}$，其中 $M$ 是数据点的总数量。
    *   确定回顾期参数 $N$ (例如, 14, 30 等)。因子值的计算将从输入序列的第 $N-1$ 个索引（即第 $N$ 个数据点）开始。

2.  **初始化**:
    *   设置当前被计算数据点的索引 `current_idx`，其范围为从 $N-1$ 到 $M-1$。
    *   为存储当前滑动窗口内的最高值，初始化 `current_max_value`（例如，可以设为负无穷大）。
    *   为存储当前滑动窗口内最高值在原始序列 $S$ 中的索引，初始化 `current_max_value_idx = -1` (表示尚未确定或已失效)。

3.  **迭代计算**: 遍历 `current_idx` 从 $N-1$ 到 $M-1$ 的每一个值：
    a.  **确定窗口范围**: 当前滑动窗口覆盖的数据点为 $S_{window\_start\_idx}, \dots, S_{current\_idx}$，其中窗口起始索引 `window_start_idx = current_idx - N + 1`。
    b.  获取当前数据点的值 $S_{current\_idx}$。
    c.  **更新窗口期最高值 `current_max_value`**:
        i.  **情况 A：历史最高值已滑出窗口**: 如果 `current_max_value_idx` 小于 `window_start_idx` (这包括了第一次计算，此时 `current_max_value_idx` 为初始值 -1)，则表明之前记录的最高值已失效或不在当前窗口内。此时，需要在当前窗口 $S[window\_start\_idx \dots current\_idx]$ 内重新搜索最高值：
            1.  将 `current_max_value` 初始化为 $S_{window\_start\_idx}$。
            2.  将 `current_max_value_idx` 初始化为 `window_start_idx`。
            3.  从 $j = window\_start\_idx + 1$ 到 `current_idx` 遍历窗口内剩余数据点 $S_j$：如果 $S_j > \text{current\_max\_value}$，则更新 `current_max_value = S_j` 并且 `current_max_value_idx = j`。
        ii. **情况 B：历史最高值仍在窗口内**: 如果 `current_max_value_idx` $\ge$ `window_start_idx`，则只需将当前数据点 $S_{current\_idx}$ 与已记录的 `current_max_value` 进行比较：
            如果 $S_{current\_idx} \ge \text{current\_max\_value}$，则更新 `current_max_value = S_{current\_idx}$，并且 `current_max_value_idx = current_idx`。
            如果 $S_{current\_idx} < \text{current\_max\_value}$，则 `current_max_value` 和 `current_max_value_idx` 保持不变。
    d.  将此步计算得到的 `current_max_value` 作为时间点 `current_idx` 的周期最高价，即 $MAX_{current\_idx} = \text{current\_max\_value}$。

4.  **输出**: 经过上述步骤，将得到一个从索引 $N-1$ 到 $M-1$ 的周期最高价序列 $MAX = \{MAX_{N-1}, MAX_N, \dots, MAX_{M-1}\}$。

【6. 备注与参数说明】
*   **回顾期 (N)**: 参数 $N$ 的选择对因子结果有重要影响。较短的 $N$ (如5, 10) 使因子对近期数据变化更为敏感；较长的 $N$ (如50, 100) 则使因子结果更平滑，反映更长期的极值水平。
*   **输入数据 (S)**: 输入序列 $S_t$ 可以是任何数值型时间序列，如价格、成交量、波动率等。如果输入的是每日最高价序列，则该因子计算的是“N日内最高价的最高值”。
*   **数据预处理**: 为确保计算结果的准确性，输入数据序列应完整，不含缺失值（NaNs）。异常值可能会对极值计算产生较大影响，应酌情处理。
*   **起始延迟**: 由于计算需要至少 $N$ 个数据点，因子序列的第一个有效值将从输入序列的第 $N$ 个数据点（索引 $N-1$）开始。



【7. 关联因子补充说明】

【关联因子信息开始】===============================================================

【因子编号和名称】
因子编号: FC.MAX.01: 周期最高价 (Period Maximum, MAX)

【1. 因子名称详情】
因子1: 周期最高价 (Period Maximum, MAX)。该因子用于计算在过去指定数量的周期内，某一输入数据序列出现过的最高数值。

【2. 核心公式】
对于时间点 $t$ 和回顾期 $N$，周期最高价 $MAX_t$ 的计算公式如下：
$MAX_t(S, N) = \max(S_{t-N+1}, S_{t-N+2}, \dots, S_t)$

【3. 变量定义】
*   $S_t$: 在时间点 $t$ 的输入数据序列的数值（例如：某资产的收盘价、最高价，或任何其他数值型序列）。
*   $N$: 计算最高价所回顾的周期长度（时间窗口大小）。这是一个正整数，通常 $N \ge 2$。
*   $MAX_t(S, N)$: 在时间点 $t$（包含当前点），向前回顾 $N$ 个周期的数据点 $S_{t-N+1}$ 到 $S_t$ 中所出现的最高值。

【4. 函数与方法说明】
*   $\max(x_1, x_2, \dots, x_k)$: 最大值函数。此函数返回其所有参数 $x_1, x_2, \dots, x_k$ 中的最大值。

【5. 计算步骤】
1.  **数据准备**:
    *   准备一个数值型时间序列 $S = \{S_0, S_1, \dots, S_{M-1}\}$，其中 $M$ 是数据点的总数量。
    *   确定回顾期参数 $N$ (例如, 14, 30 等)。因子值的计算将从输入序列的第 $N-1$ 个索引（即第 $N$ 个数据点）开始。

2.  **初始化**:
    *   设置当前被计算数据点的索引 `current_idx`，其范围为从 $N-1$ 到 $M-1$。
    *   为存储当前滑动窗口内的最高值，初始化 `current_max_value`（例如，可以设为负无穷大）。
    *   为存储当前滑动窗口内最高值在原始序列 $S$ 中的索引，初始化 `current_max_value_idx = -1` (表示尚未确定或已失效)。

3.  **迭代计算**: 遍历 `current_idx` 从 $N-1$ 到 $M-1$ 的每一个值：
    a.  **确定窗口范围**: 当前滑动窗口覆盖的数据点为 $S_{window\_start\_idx}, \dots, S_{current\_idx}$，其中窗口起始索引 `window_start_idx = current_idx - N + 1`。
    b.  获取当前数据点的值 $S_{current\_idx}$。
    c.  **更新窗口期最高值 `current_max_value`**:
        i.  **情况 A：历史最高值已滑出窗口**: 如果 `current_max_value_idx` 小于 `window_start_idx` (这包括了第一次计算，此时 `current_max_value_idx` 为初始值 -1)，则表明之前记录的最高值已失效或不在当前窗口内。此时，需要在当前窗口 $S[window\_start\_idx \dots current\_idx]$ 内重新搜索最高值：
            1.  将 `current_max_value` 初始化为 $S_{window\_start\_idx}$。
            2.  将 `current_max_value_idx` 初始化为 `window_start_idx`。
            3.  从 $j = window\_start\_idx + 1$ 到 `current_idx` 遍历窗口内剩余数据点 $S_j$：如果 $S_j > \text{current\_max\_value}$，则更新 `current_max_value = S_j` 并且 `current_max_value_idx = j`。
        ii. **情况 B：历史最高值仍在窗口内**: 如果 `current_max_value_idx` $\ge$ `window_start_idx`，则只需将当前数据点 $S_{current\_idx}$ 与已记录的 `current_max_value` 进行比较：
            如果 $S_{current\_idx} \ge \text{current\_max\_value}$，则更新 `current_max_value = S_{current\_idx}$，并且 `current_max_value_idx = current_idx`。
            如果 $S_{current\_idx} < \text{current\_max\_value}$，则 `current_max_value` 和 `current_max_value_idx` 保持不变。
    d.  将此步计算得到的 `current_max_value` 作为时间点 `current_idx` 的周期最高价，即 $MAX_{current\_idx} = \text{current\_max\_value}$。

4.  **输出**: 经过上述步骤，将得到一个从索引 $N-1$ 到 $M-1$ 的周期最高价序列 $MAX = \{MAX_{N-1}, MAX_N, \dots, MAX_{M-1}\}$。

【6. 备注与参数说明】
*   **回顾期 (N)**: 参数 $N$ 的选择对因子结果有重要影响。较短的 $N$ (如5, 10) 使因子对近期数据变化更为敏感；较长的 $N$ (如50, 100) 则使因子结果更平滑，反映更长期的极值水平。
*   **输入数据 (S)**: 输入序列 $S_t$ 可以是任何数值型时间序列，如价格、成交量、波动率等。如果输入的是每日最高价序列，则该因子计算的是“N日内最高价的最高值”。
*   **数据预处理**: 为确保计算结果的准确性，输入数据序列应完整，不含缺失值（NaNs）。异常值可能会对极值计算产生较大影响，应酌情处理。
*   **起始延迟**: 由于计算需要至少 $N$ 个数据点，因子序列的第一个有效值将从输入序列的第 $N$ 个数据点（索引 $N-1$）开始。

【关联因子信息结束】===============================================================

【关联因子信息开始】===============================================================

【因子编号和名称】
因子编号: FC.MIN.01: 周期最低价 (Period Minimum, MIN)

【1. 因子名称详情】
因子1: 周期最低价 (Period Minimum, MIN)。该因子用于计算在过去指定数量的周期内，某一输入数据序列出现过的最低数值。

【2. 核心公式】
对于时间点 $t$ 和回顾期 $N$，周期最低价 $MIN_t$ 的计算公式如下：
$MIN_t(S, N) = \min(S_{t-N+1}, S_{t-N+2}, \dots, S_t)$

【3. 变量定义】
*   $S_t$: 在时间点 $t$ 的输入数据序列的数值（例如：某资产的收盘价、最低价，或任何其他数值型序列）。
*   $N$: 计算最低价所回顾的周期长度（时间窗口大小）。这是一个正整数，通常 $N \ge 2$。
*   $MIN_t(S, N)$: 在时间点 $t$（包含当前点），向前回顾 $N$ 个周期的数据点 $S_{t-N+1}$ 到 $S_t$ 中所出现的最低值。

【4. 函数与方法说明】
*   $\min(x_1, x_2, \dots, x_k)$: 最小值函数。此函数返回其所有参数 $x_1, x_2, \dots, x_k$ 中的最小值。

【5. 计算步骤】
1.  **数据准备**:
    *   准备一个数值型时间序列 $S = \{S_0, S_1, \dots, S_{M-1}\}$，其中 $M$ 是数据点的总数量。
    *   确定回顾期参数 $N$ (例如, 14, 30 等)。因子值的计算将从输入序列的第 $N-1$ 个索引（即第 $N$ 个数据点）开始。

2.  **初始化**:
    *   设置当前被计算数据点的索引 `current_idx`，其范围为从 $N-1$ 到 $M-1$。
    *   为存储当前滑动窗口内的最低值，初始化 `current_min_value`（例如，可以设为正无穷大）。
    *   为存储当前滑动窗口内最低值在原始序列 $S$ 中的索引，初始化 `current_min_value_idx = -1` (表示尚未确定或已失效)。

3.  **迭代计算**: 遍历 `current_idx` 从 $N-1$ 到 $M-1$ 的每一个值：
    a.  **确定窗口范围**: 当前滑动窗口覆盖的数据点为 $S_{window\_start\_idx}, \dots, S_{current\_idx}$，其中窗口起始索引 `window_start_idx = current_idx - N + 1`。
    b.  获取当前数据点的值 $S_{current\_idx}$。
    c.  **更新窗口期最低值 `current_min_value`**:
        i.  **情况 A：历史最低值已滑出窗口**: 如果 `current_min_value_idx` 小于 `window_start_idx` (这包括了第一次计算，此时 `current_min_value_idx` 为初始值 -1)，则表明之前记录的最低值已失效或不在当前窗口内。此时，需要在当前窗口 $S[window\_start\_idx \dots current\_idx]$ 内重新搜索最低值：
            1.  将 `current_min_value` 初始化为 $S_{window\_start\_idx}$。
            2.  将 `current_min_value_idx` 初始化为 `window_start_idx`。
            3.  从 $j = window\_start\_idx + 1$ 到 `current_idx` 遍历窗口内剩余数据点 $S_j$：如果 $S_j < \text{current\_min\_value}$，则更新 `current_min_value = S_j` 并且 `current_min_value_idx = j`。
        ii. **情况 B：历史最低值仍在窗口内**: 如果 `current_min_value_idx` $\ge$ `window_start_idx`，则只需将当前数据点 $S_{current\_idx}$ 与已记录的 `current_min_value` 进行比较：
            如果 $S_{current\_idx} \le \text{current\_min\_value}$，则更新 `current_min_value = S_{current\_idx}$，并且 `current_min_value_idx = current_idx`。
            如果 $S_{current\_idx} > \text{current\_min\_value}$，则 `current_min_value` 和 `current_min_value_idx` 保持不变。
    d.  将此步计算得到的 `current_min_value` 作为时间点 `current_idx` 的周期最低价，即 $MIN_{current\_idx} = \text{current\_min\_value}$。

4.  **输出**: 经过上述步骤，将得到一个从索引 $N-1$ 到 $M-1$ 的周期最低价序列 $MIN = \{MIN_{N-1}, MIN_N, \dots, MIN_{M-1}\}$。

【6. 备注与参数说明】
*   **回顾期 (N)**: 参数 $N$ 的选择对因子结果有重要影响。较短的 $N$ (如5, 10) 使因子对近期数据变化更为敏感；较长的 $N$ (如50, 100) 则使因子结果更平滑，反映更长期的极值水平。
*   **输入数据 (S)**: 输入序列 $S_t$ 可以是任何数值型时间序列，如价格、成交量、波动率等。如果输入的是每日最低价序列，则该因子计算的是“N日内最低价的最低值”。
*   **数据预处理**: 为确保计算结果的准确性，输入数据序列应完整，不含缺失值（NaNs）。异常值可能会对极值计算产生较大影响，应酌情处理。
*   **起始延迟**: 由于计算需要至少 $N$ 个数据点，因子序列的第一个有效值将从输入序列的第 $N$ 个数据点（索引 $N-1$）开始。

【关联因子信息结束】===============================================================

【因子信息结束】===============================================================