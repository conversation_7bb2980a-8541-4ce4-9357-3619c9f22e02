【因子信息开始】===============================================================  

【因子编号和名称】

因子编号: 001A: MESA自适应移动平均值 (MESA Adaptive Moving Average, MAMA)

【1. 因子名称详情】

因子1: MESA自适应移动平均值 (MESA Adaptive Moving Average, MAMA)

【2. 核心公式】

MAMA的计算涉及多个步骤，核心思想是通过希尔伯特变换估计当前周期的主导周期，并以此动态调整指数移动平均（EMA）的平滑系数。

1.  价格平滑 (Price Smoothing):
    SmoothedPrice_t = (4 * Price_t + 3 * Price_{t-1} + 2 * Price_{t-2} + 1 * Price_{t-3}) / 10

2.  希尔伯特变换分量 (Hilbert Transform Components):
    • 调整因子: Adj_t = (0.075 * Period_{smooth, t-1} + 0.54)
    • 去趋势器 (Detrender): Detrender_t = (0.0962 * SP_t + 0.5769 * SP_{t-2} - 0.5769 * SP_{t-4} - 0.0962 * SP_{t-6}) * Adj_t
      (其中 SP_t 代表 SmoothedPrice_t)
    • 同相分量 (In-phase Component / I1): I1_t = Detrender_{t-3}
    • 正交分量 (Quadrature Component / Q1): Q1_t = (0.0962 * Detrender_t + 0.5769 * Detrender_{t-2} - 0.5769 * Detrender_{t-4} - 0.0962 * Detrender_{t-6}) * Adj_t

3.  相位和周期计算 (Phase and Period Calculation):
    • 瞬时相位 (PhaseLagged_t): PhaseLagged_t = arctan(Q1_t / I1_t) * (180/π) (若 I1_t = 0, 则 PhaseLagged_t = 0)
    • 相位变化 (DeltaPhase_t):
        DeltaPhase_t = PhaseLagged_{t-1} - PhaseLagged_t
        DeltaPhase_t = max(1.0, DeltaPhase_t)
    • 周期相关分量 (jI_t, jQ_t, I2_t, Q2_t, Re_t, Im_t):
        jI_t = (0.0962 * I1_t + 0.5769 * I1_{t-2} - 0.5769 * I1_{t-4} - 0.0962 * I1_{t-6}) * Adj_t
        jQ_t = (0.0962 * Q1_t + 0.5769 * Q1_{t-2} - 0.5769 * Q1_{t-4} - 0.0962 * Q1_{t-6}) * Adj_t
        I2_t = 0.2 * (I1_t - jQ_t) + 0.8 * I2_{t-1}
        Q2_t = 0.2 * (Q1_t + jI_t) + 0.8 * Q2_{t-1}
        Re_t = 0.2 * (I2_t * I2_{t-1} + Q2_t * Q2_{t-1}) + 0.8 * Re_{t-1}
        Im_t = 0.2 * (I2_t * Q2_{t-1} - Q2_t * I2_{t-1}) + 0.8 * Im_{t-1}
    • 计算周期 (Period_{calc,t}):
        如果 Im_t ≠ 0 且 Re_t ≠ 0: Period_{calc,t} = 360 / (arctan(Im_t/Re_t) * (180/π))
        否则 Period_{calc,t} 保持前值或使用默认值。
    • 平滑和限制周期 (Period_{smooth,t}):
        Period_{temp,t} 首先被限制在 [0.67 * Period_{smooth, t-1}, 1.5 * Period_{smooth, t-1}] 范围内。
        然后进一步限制在 [6, 50] 范围内。
        Period_{smooth,t} = 0.2 * Period_{temp,t} + 0.8 * Period_{smooth,t-1}

4.  MAMA 计算:
    • 自适应平滑系数 (α_t):
        如果 DeltaPhase_t > 1:
        α_t = FastLimit / DeltaPhase_t
        α_t = max(SlowLimit, α_t)
        否则 (DeltaPhase_t ≤ 1):
        α_t = FastLimit
    • MAMA 值:
        MAMA_t = α_t * Price_t + (1 - α_t) * MAMA_{t-1}

【3. 变量定义】

• Price_t: t时刻的原始输入价格。
• SmoothedPrice_t (SP_t): t时刻的平滑价格。
• Period_{smooth, t}: t时刻的平滑后主导周期。
• Period_{calc, t}: t时刻根据 Re_t, Im_t 计算出的未平滑周期。
• Period_{temp, t}: t时刻的临时周期值，用于限制和最后平滑。
• Adj_t: t时刻的调整因子，用于希尔伯特变换滤波。
• Detrender_t: t时刻的去趋势值。
• I1_t: t时刻的同相分量，是 Detrender_{t-3}。
• Q1_t: t时刻的正交分量。
• PhaseLagged_t: t时刻根据 I1_t, Q1_t 计算的（滞后）瞬时相位（角度制）。
• DeltaPhase_t: t时刻的相位变化率。
• jI_t: t时刻对 I1 进行希尔伯特变换的结果。
• jQ_t: t时刻对 Q1 进行希尔伯特变换的结果。
• I2_t, Q2_t: t时刻对 I1, Q1, jI, jQ 进行组合和平滑得到的中间分量。
• Re_t, Im_t: t时刻用于计算周期的复数信号的实部和虚部。
• FastLimit: 快速限制参数，用于确定 α_t 的上限或基础值，通常为0.01至0.99。
• SlowLimit: 慢速限制参数，用于确定 α_t 的下限，通常为0.01至0.99，且小于 FastLimit。
• α_t: t时刻的自适应平滑系数。
• MAMA_t: t时刻的MESA自适应移动平均值。
• MAMA_{t-1}: t-1时刻的MESA自适应移动平均值。
• π: 圆周率。
• arctan(): 反正切函数。
• max(): 取最大值函数。
• 带下标 t-k 的变量表示该变量在 k 个周期前的值。

【4. 函数与方法说明】

• 4周期加权移动平均 (4-Period Weighted Moving Average, WMA):
  用于计算 SmoothedPrice_t。计算公式为：
       WMA_N(X) = (∑[i=0 to N-1] (N-i) * X_{t-i}) / (∑[i=0 to N-1] (N-i))
  对于本因子，周期 N=4，权重为 4, 3, 2, 1。
  SmoothedPrice_t = (4 * Price_t + 3 * Price_{t-1} + 2 * Price_{t-2} + 1 * Price_{t-3}) / (4+3+2+1)

• 希尔伯特变换器核心滤波 (Hilbert Transformer Filter Core):
  用于计算 Detrender_t, Q1_t, jI_t, jQ_t。其基本形式为一种 FIR 滤波器：
       Y_t = (c₁ * X_t + c₂ * X_{t-2} - c₂ * X_{t-4} - c₁ * X_{t-6}) * AdjFactor_t
  其中 c₁ = 0.0962, c₂ = 0.5769。X_t 是输入序列（例如 SmoothedPrice_t 用于 Detrender_t, Detrender_t 用于 Q1_t 等），AdjFactor_t 是调整因子 Adj_t。

• 反正切函数 (arctan):
  标准三角函数，用于从 Q1_t/I1_t 或 Im_t/Re_t 计算相位角。结果通常是弧度，需乘以 (180/π) 转换为角度。

• 指数移动平均 (Exponential Moving Average, EMA) 结构:
  MAMA_t 的计算公式 α_t * Price_t + (1 - α_t) * MAMA_{t-1} 是 EMA 的标准形式，但其平滑系数 α_t 是动态自适应的。

【5. 计算步骤】

1. 数据准备:
   获取输入价格序列 Price_t。
   设定参数 FastLimit (默认 0.5) 和 SlowLimit (默认 0.05)。

2. 初始化:
   MAMA 的计算需要一定的历史数据。通常，前 32 个数据点（或更多，取决于不稳定期的设定）用于初始化内部变量。
   MAMA_0, Period_{smooth,0}, PhaseLagged_0, I2_0, Q2_0, Re_0, Im_0 等可设为 0 或根据最初几个价格点进行预估。
   例如，Period_{smooth,0} 可以设为一个合理的初始值（如 20）。

3. 迭代计算 (对每个时间点 t 从第一个有效输出点开始):
   a. 计算平滑价格 (SmoothedPrice_t):
      使用 4 周期 WMA 公式计算 SmoothedPrice_t。
      SmoothedPrice_t = (4 * Price_t + 3 * Price_{t-1} + 2 * Price_{t-2} + 1 * Price_{t-3}) / 10
   b. 计算调整因子 (Adj_t):
      Adj_t = (0.075 * Period_{smooth, t-1} + 0.54)
   c. 计算去趋势器 (Detrender_t):
      Detrender_t = (0.0962 * SP_t + 0.5769 * SP_{t-2} - 0.5769 * SP_{t-4} - 0.0962 * SP_{t-6}) * Adj_t
   d. 计算 I1_t 和 Q1_t:
      I1_t = Detrender_{t-3}
      Q1_t = (0.0962 * Detrender_t + 0.5769 * Detrender_{t-2} - 0.5769 * Detrender_{t-4} - 0.0962 * Detrender_{t-6}) * Adj_t
   e. 计算瞬时相位 (PhaseLagged_t):
      PhaseLagged_t = arctan(Q1_t / I1_t) * (180/π) (若 I1_t = 0, 则 PhaseLagged_t = 0)
   f. 计算相位变化 (DeltaPhase_t):
      DeltaPhase_t = PhaseLagged_{t-1} - PhaseLagged_t
      DeltaPhase_t = max(1.0, DeltaPhase_t)
   g. 计算自适应平滑系数 (α_t):
      如果 DeltaPhase_t > 1:
         α_t = FastLimit / DeltaPhase_t
         α_t = max(SlowLimit, α_t)
      否则 (DeltaPhase_t ≤ 1):
         α_t = FastLimit
   h. 计算 MAMA 值 (MAMA_t):
      MAMA_t = α_t * Price_t + (1 - α_t) * MAMA_{t-1}
   i. 更新周期计算的辅助变量:
      jI_t = (0.0962 * I1_t + 0.5769 * I1_{t-2} - 0.5769 * I1_{t-4} - 0.0962 * I1_{t-6}) * Adj_t
      jQ_t = (0.0962 * Q1_t + 0.5769 * Q1_{t-2} - 0.5769 * Q1_{t-4} - 0.0962 * Q1_{t-6}) * Adj_t
      I2_t = 0.2 * (I1_t - jQ_t) + 0.8 * I2_{t-1}
      Q2_t = 0.2 * (Q1_t + jI_t) + 0.8 * Q2_{t-1}
      Re_t = 0.2 * (I2_t * I2_{t-1} + Q2_t * Q2_{t-1}) + 0.8 * Re_{t-1}
      Im_t = 0.2 * (I2_t * Q2_{t-1} - Q2_t * I2_{t-1}) + 0.8 * Im_{t-1}
   j.  计算并更新主导周期 (Period_{smooth,t}):
      1.  计算 Period_{calc,t}:
          如果 Im_t ≠ 0 且 Re_t ≠ 0: Period_{calc,t} = 360 / (arctan(Im_t/Re_t) * (180/π))
          否则，Period_{calc,t} 可沿用 Period_{smooth,t-1}
      2.  赋值给 Period_{temp,t} = Period_{calc,t}
      3.  限制 Period_{temp,t}:
          UpperLimit = 1.5 * Period_{smooth,t-1}
          LowerLimit = 0.67 * Period_{smooth,t-1}
          Period_{temp,t} = min(max(Period_{temp,t}, LowerLimit), UpperLimit)
      4.  再次限制 Period_{temp,t}:
          Period_{temp,t} = min(max(Period_{temp,t}, 6), 50)
      5.  平滑周期:
          Period_{smooth,t} = 0.2 * Period_{temp,t} + 0.8 * Period_{smooth,t-1}
   k.  保存 MAMA_t, Period_{smooth,t}, PhaseLagged_t, I2_t, Q2_t, Re_t, Im_t 等值供下一周期计算使用.

【6. 备注与参数说明】

• 参数:
   • optInFastLimit: α 的快速限制，调整 MAMA 对价格变化的敏感度上限。默认值为 0.5。取值范围 (0.01, 0.99)。
   • optInSlowLimit: α 的慢速限制，调整 MAMA 对价格变化的敏感度下限。默认值为 0.05。取值范围 (0.01, 0.99) 且小于 optInFastLimit。
• 窗口期/Lookback: MAMA 指标有固定的 32 周期 Lookback，用于滤波器初始化以及各种滞后项的计算。此外，还需加上 TA-Lib 全局设定的不稳定周期（TA_GLOBALS_UNSTABLE_PERIOD for TA_FUNC_UNST_MAMA），这部分通常用于确保指标输出稳定。
• 数据预处理: 建议使用经过清洗的价格数据。
• 初始化敏感性: 由于 MAMA 包含多个递归计算和平滑过程，其初始值对早期指标结果有一定影响，经过不稳定期后趋于稳定。
• 用途: MAMA 旨在提供一个能够根据市场周期性快速调整的移动平均线，以减少滞后性并适应市场变化。



【7. 关联因子补充说明】

【关联因子信息开始】===============================================================

【因子编号和名称】

因子编号: 001A: MESA自适应移动平均值 (MESA Adaptive Moving Average, MAMA)
因子编号: 001B: 跟随自适应移动平均值 (Following Adaptive Moving Average, FAMA)

---

**因子001A: MESA自适应移动平均值 (MESA Adaptive Moving Average, MAMA)**

【1. 因子名称详情】

因子1: MESA自适应移动平均值 (MESA Adaptive Moving Average, MAMA)

【2. 核心公式】

MAMA的计算涉及多个步骤，核心思想是通过希尔伯特变换估计当前周期的主导周期，并以此动态调整指数移动平均（EMA）的平滑系数。

1.  **价格平滑 (Price Smoothing):**
    $SmoothedPrice_t = \frac{4 \cdot Price_t + 3 \cdot Price_{t-1} + 2 \cdot Price_{t-2} + 1 \cdot Price_{t-3}}{10}$

2.  **希尔伯特变换分量 (Hilbert Transform Components):**
    *   调整因子: $Adj_t = (0.075 \cdot Period_{smooth, t-1} + 0.54)$
    *   去趋势器 (Detrender): $Detrender_t = (0.0962 \cdot SP_t + 0.5769 \cdot SP_{t-2} - 0.5769 \cdot SP_{t-4} - 0.0962 \cdot SP_{t-6}) \cdot Adj_t$
        (其中 $SP_t$ 代表 $SmoothedPrice_t$)
    *   同相分量 (In-phase Component / I1): $I1_t = Detrender_{t-3}$
    *   正交分量 (Quadrature Component / Q1): $Q1_t = (0.0962 \cdot Detrender_t + 0.5769 \cdot Detrender_{t-2} - 0.5769 \cdot Detrender_{t-4} - 0.0962 \cdot Detrender_{t-6}) \cdot Adj_t$

3.  **相位和周期计算 (Phase and Period Calculation):**
    *   瞬时相位 ($PhaseLagged_t$): $PhaseLagged_t = \arctan\left(\frac{Q1_t}{I1_t}\right) \cdot \frac{180}{\pi}$ (若 $I1_t = 0$, 则 $PhaseLagged_t = 0$)
    *   相位变化 ($DeltaPhase_t$):
        $\Delta Phase_t = PhaseLagged_{t-1} - PhaseLagged_t$
        $DeltaPhase_t = \max(1.0, DeltaPhase_t)$
    *   周期相关分量($jI_t, jQ_t, I2_t, Q2_t, Re_t, Im_t$):
        $jI_t = (0.0962 \cdot I1_t + 0.5769 \cdot I1_{t-2} - 0.5769 \cdot I1_{t-4} - 0.0962 \cdot I1_{t-6}) \cdot Adj_t$
        $jQ_t = (0.0962 \cdot Q1_t + 0.5769 \cdot Q1_{t-2} - 0.5769 \cdot Q1_{t-4} - 0.0962 \cdot Q1_{t-6}) \cdot Adj_t$
        $I2_t = 0.2 \cdot (I1_t - jQ_t) + 0.8 \cdot I2_{t-1}$
        $Q2_t = 0.2 \cdot (Q1_t + jI_t) + 0.8 \cdot Q2_{t-1}$
        $Re_t = 0.2 \cdot (I2_t \cdot I2_{t-1} + Q2_t \cdot Q2_{t-1}) + 0.8 \cdot Re_{t-1}$
        $Im_t = 0.2 \cdot (I2_t \cdot Q2_{t-1} - Q2_t \cdot I2_{t-1}) + 0.8 \cdot Im_{t-1}$
    *   计算周期 ($Period_{calc,t}$):
        如果 $Im_t \neq 0$ 且 $Re_t \neq 0$: $Period_{calc,t} = \frac{360}{\arctan\left(\frac{Im_t}{Re_t}\right) \cdot \frac{180}{\pi}}$
        否则 $Period_{calc,t}$ 保持前值或使用默认值。
    *   平滑和限制周期 ($Period_{smooth,t}$):
        $Period_{temp,t}$ 首先被限制在 $[0.67 \cdot Period_{smooth, t-1}, 1.5 \cdot Period_{smooth, t-1}]$ 范围内。
        然后进一步限制在 $[6, 50]$ 范围内。
        $Period_{smooth,t} = 0.2 \cdot Period_{temp,t} + 0.8 \cdot Period_{smooth,t-1}$

4.  **MAMA 计算:**
    *   自适应平滑系数 ($\alpha_t$):
        If $DeltaPhase_t > 1$:
        $\alpha_t = \frac{FastLimit}{DeltaPhase_t}$
        $\alpha_t = \max(SlowLimit, \alpha_t)$
        Else ($DeltaPhase_t \le 1$):
        $\alpha_t = FastLimit$
    *   MAMA 值:
        $MAMA_t = \alpha_t \cdot Price_t + (1 - \alpha_t) \cdot MAMA_{t-1}$

【3. 变量定义】

*   $Price_t$: $t$时刻的原始输入价格。
*   $SmoothedPrice_t$ ($SP_t$): $t$时刻的平滑价格。
*   $Period_{smooth, t}$: $t$时刻的平滑后主导周期。
*   $Period_{calc, t}$: $t$时刻根据 $Re_t, Im_t$ 计算出的未平滑周期。
*   $Period_{temp, t}$: $t$时刻的临时周期值，用于限制和最后平滑。
*   $Adj_t$: $t$时刻的调整因子，用于希尔伯特变换滤波。
*   $Detrender_t$: $t$时刻的去趋势值。
*   $I1_t$: $t$时刻的同相分量，是 $Detrender_{t-3}$。
*   $Q1_t$: $t$时刻的正交分量。
*   $PhaseLagged_t$: $t$时刻根据 $I1_t, Q1_t$ 计算的（滞后）瞬时相位（角度制）。
*   $DeltaPhase_t$: $t$时刻的相位变化率。
*   $jI_t$: $t$时刻对 $I1$ 进行希尔伯特变换的结果。
*   $jQ_t$: $t$时刻对 $Q1$ 进行希尔伯特变换的结果。
*   $I2_t, Q2_t$: $t$时刻对 $I1, Q1, jI, jQ$ 进行组合和平滑得到的中间分量。
*   $Re_t, Im_t$: $t$时刻用于计算周期的复数信号的实部和虚部。
*   $FastLimit$: 快速限制参数，用于确定 $\alpha_t$ 的上限或基础值，通常为0.01至0.99。
*   $SlowLimit$: 慢速限制参数，用于确定 $\alpha_t$ 的下限，通常为0.01至0.99，且小于$FastLimit$。
*   $\alpha_t$: $t$时刻的自适应平滑系数。
*   $MAMA_t$: $t$时刻的MESA自适应移动平均值。
*   $MAMA_{t-1}$: $t-1$时刻的MESA自适应移动平均值。
*   $\pi$: 圆周率。
*   $\arctan()$: 反正切函数。
*   $\max()$: 取最大值函数。
*   带下标 $t-k$ 的变量表示该变量在 $k$ 个周期前的值。

【4. 函数与方法说明】

*   **4周期加权移动平均 (4-Period Weighted Moving Average, WMA):**
    用于计算 $SmoothedPrice_t$。计算公式为：$WMA_N(X) = \frac{\sum_{i=0}^{N-1} (N-i) \cdot X_{t-i}}{\sum_{i=0}^{N-1} (N-i)}$。对于本因子，周期 $N=4$，权重为4, 3, 2, 1。
    $SmoothedPrice_t = \frac{4 \cdot Price_t + 3 \cdot Price_{t-1} + 2 \cdot Price_{t-2} + 1 \cdot Price_{t-3}}{4+3+2+1}$
*   **希尔伯特变换器核心滤波 (Hilbert Transformer Filter Core):**
    用于计算 $Detrender_t, Q1_t, jI_t, jQ_t$。其基本形式为一种FIR滤波器：
    $Y_t = (c_1 \cdot X_t + c_2 \cdot X_{t-2} - c_2 \cdot X_{t-4} - c_1 \cdot X_{t-6}) \cdot AdjFactor_t$
    其中 $c_1 = 0.0962$, $c_2 = 0.5769$。$X_t$ 是输入序列（例如 $SmoothedPrice_t$ 用于 $Detrender_t$, $Detrender_t$ 用于 $Q1_t$ 等），$AdjFactor_t$ 是调整因子 $Adj_t$。
*   **反正切函数 ($\arctan$):**
    标准三角函数，用于从 $Q1_t/I1_t$ 或 $Im_t/Re_t$ 计算相位角。结果通常是弧度，需乘以 $180/\pi$ 转换为角度。
*   **指数移动平均 (Exponential Moving Average, EMA) 结构:**
    $MAMA_t$ 的计算公式 $\alpha_t \cdot Price_t + (1 - \alpha_t) \cdot MAMA_{t-1}$ 是EMA的标准形式，但其平滑系数 $\alpha_t$ 是动态自适应的。

【5. 计算步骤】

1.  **数据准备:**
    获取输入价格序列 $Price_t$。
    设定参数 $FastLimit$ (默认0.5) 和 $SlowLimit$ (默认0.05)。
2.  **初始化:**
    MAMA的计算需要一定的历史数据。通常，前32个数据点（或更多，取决于不稳定期的设定）用于初始化内部变量。
    $MAMA_0$, $Period_{smooth,0}$, $PhaseLagged_0$, $I2_0, Q2_0, Re_0, Im_0$ 等可设为0或根据最初几个价格点进行预估。
    例如， $Period_{smooth,0}$ 可以设为一个合理的初始值（如20）。
3.  **迭代计算 (对每个时间点 $t$ 从第一个有效输出点开始):**
    a.  **计算平滑价格 ($SmoothedPrice_t$):**
        使用4周期WMA公式计算 $SmoothedPrice_t$。
        $SmoothedPrice_t = (4 \cdot Price_t + 3 \cdot Price_{t-1} + 2 \cdot Price_{t-2} + 1 \cdot Price_{t-3}) / 10$
    b.  **计算调整因子 ($Adj_t$):**
        $Adj_t = (0.075 \cdot Period_{smooth, t-1} + 0.54)$
    c.  **计算去趋势器 ($Detrender_t$):**
        $Detrender_t = (0.0962 \cdot SP_t + 0.5769 \cdot SP_{t-2} - 0.5769 \cdot SP_{t-4} - 0.0962 \cdot SP_{t-6}) \cdot Adj_t$
    d.  **计算$I1_t$和$Q1_t$:**
        $I1_t = Detrender_{t-3}$
        $Q1_t = (0.0962 \cdot Detrender_t + 0.5769 \cdot Detrender_{t-2} - 0.5769 \cdot Detrender_{t-4} - 0.0962 \cdot Detrender_{t-6}) \cdot Adj_t$
    e.  **计算瞬时相位 ($PhaseLagged_t$):**
        $PhaseLagged_t = \arctan(Q1_t / I1_t) \cdot (180/\pi)$ (若 $I1_t = 0$, 则 $PhaseLagged_t = 0$)
    f.  **计算相位变化 ($DeltaPhase_t$):**
        $\Delta Phase_t = PhaseLagged_{t-1} - PhaseLagged_t$
        $DeltaPhase_t = \max(1.0, DeltaPhase_t)$
    g.  **计算自适应平滑系数 ($\alpha_t$):**
        If $DeltaPhase_t > 1$:
        $\alpha_t = FastLimit / DeltaPhase_t$
        $\alpha_t = \max(SlowLimit, \alpha_t)$
        Else ($DeltaPhase_t \le 1$):
        $\alpha_t = FastLimit$
    h.  **计算MAMA值 ($MAMA_t$):**
        $MAMA_t = \alpha_t \cdot Price_t + (1 - \alpha_t) \cdot MAMA_{t-1}$
    i.  **更新周期计算的辅助变量:**
        $jI_t = (0.0962 \cdot I1_t + 0.5769 \cdot I1_{t-2} - 0.5769 \cdot I1_{t-4} - 0.0962 \cdot I1_{t-6}) \cdot Adj_t$
        $jQ_t = (0.0962 \cdot Q1_t + 0.5769 \cdot Q1_{t-2} - 0.5769 \cdot Q1_{t-4} - 0.0962 \cdot Q1_{t-6}) \cdot Adj_t$
        $I2_t = 0.2 \cdot (I1_t - jQ_t) + 0.8 \cdot I2_{t-1}$
        $Q2_t = 0.2 \cdot (Q1_t + jI_t) + 0.8 \cdot Q2_{t-1}$
        $Re_t = 0.2 \cdot (I2_t \cdot I2_{t-1} + Q2_t \cdot Q2_{t-1}) + 0.8 \cdot Re_{t-1}$
        $Im_t = 0.2 \cdot (I2_t \cdot Q2_{t-1} - Q2_t \cdot I2_{t-1}) + 0.8 \cdot Im_{t-1}$
    j.  **计算并更新主导周期 ($Period_{smooth,t}$):**
        1.  计算 $Period_{calc,t}$:
            If $Im_t \neq 0$ and $Re_t \neq 0$: $Period_{calc,t} = \frac{360}{\arctan(Im_t/Re_t) \cdot (180/\pi)}$.
            Else, $Period_{calc,t}$ 可沿用 $Period_{smooth,t-1}$。
        2.  赋值给 $Period_{temp,t} = Period_{calc,t}$。
        3.  限制 $Period_{temp,t}$:
            $UpperLimit = 1.5 \cdot Period_{smooth,t-1}$
            $LowerLimit = 0.67 \cdot Period_{smooth,t-1}$
            $Period_{temp,t} = \min(\max(Period_{temp,t}, LowerLimit), UpperLimit)$
        4.  再次限制 $Period_{temp,t}$:
            $Period_{temp,t} = \min(\max(Period_{temp,t}, 6), 50)$
        5.  平滑周期:
            $Period_{smooth,t} = 0.2 \cdot Period_{temp,t} + 0.8 \cdot Period_{smooth,t-1}$
    k.  保存 $MAMA_t$, $Period_{smooth,t}$, $PhaseLagged_t$, $I2_t, Q2_t, Re_t, Im_t$ 等值供下一周期计算使用。

【6. 备注与参数说明】

*   **参数:**
    *   `optInFastLimit`: α的快速限制，调整MAMA对价格变化的敏感度上限。默认值为0.5。取值范围 (0.01, 0.99)。
    *   `optInSlowLimit`: α的慢速限制，调整MAMA对价格变化的敏感度下限。默认值为0.05。取值范围 (0.01, 0.99)。
*   **窗口期/Lookback:** MAMA指标有固定的32周期Lookback，用于滤波器初始化以及各种滞后项的计算。此外，还需加上TA-Lib全局设定的不稳定周期（`TA_GLOBALS_UNSTABLE_PERIOD` for `TA_FUNC_UNST_MAMA`），这部分通常用于确保指标输出稳定。
*   **数据预处理:** 建议使用经过清洗的价格数据。
*   **初始化敏感性:** 由于MAMA包含多个递归计算和平滑过程，其初始值对早期指标结果有一定影响，经过不稳定期后趋于稳定。
*   **用途:** MAMA旨在提供一个能够根据市场周期性快速调整的移动平均线，以减少滞后性并适应市场变化。

---

**因子001B: 跟随自适应移动平均值 (Following Adaptive Moving Average, FAMA)**

【1. 因子名称详情】

因子2: 跟随自adaptive移动平均值 (Following Adaptive Moving Average, FAMA)

【2. 核心公式】

FAMA 是 MAMA 的进一步平滑。

1.  **FAMA 平滑系数 ($\alpha_{FAMA,t}$):**
    $\alpha_{FAMA,t} = 0.5 \cdot \alpha_t$
    (其中 $\alpha_t$ 是为计算 $MAMA_t$ 而得到的自适应平滑系数)

2.  **FAMA 值:**
    $FAMA_t = \alpha_{FAMA,t} \cdot MAMA_t + (1 - \alpha_{FAMA,t}) \cdot FAMA_{t-1}$

【3. 变量定义】

*   $FAMA_t$: $t$时刻的跟随自适应移动平均值。
*   $FAMA_{t-1}$: $t-1$时刻的跟随自适应移动平均值。
*   $MAMA_t$: $t$时刻的MESA自适应移动平均值 (其计算过程依赖于因子001A)。
*   $\alpha_{FAMA,t}$: $t$时刻用于计算FAMA的平滑系数。
*   $\alpha_t$: $t$时刻用于计算MAMA的自适应平滑系数 (其计算过程依赖于因子001A)。

【4. 函数与方法说明】

*   **指数移动平均 (Exponential Moving Average, EMA) 结构:**
    $FAMA_t$ 的计算公式 $\alpha_{FAMA,t} \cdot MAMA_t + (1 - \alpha_{FAMA,t}) \cdot FAMA_{t-1}$ 是EMA的标准形式。其平滑系数 $\alpha_{FAMA,t}$ 间接自适应，因为它依赖于MAMA的平滑系数 $\alpha_t$。

【5. 计算步骤】

1.  **数据准备:**
    获取输入价格序列 $Price_t$。
    设定参数 $FastLimit$ (默认0.5) 和 $SlowLimit$ (默认0.05)，这些参数与MAMA共享。
2.  **计算MAMA及其相关组件 (因子001A的完整计算过程):**
    a.  **初始化MAMA相关变量:**
        $MAMA_0$, $Period_{smooth,0}$, $PhaseLagged_0$, $I2_0, Q2_0, Re_0, Im_0$ 等可设为0或根据最初几个价格点进行预估。
    b.  **迭代计算MAMA (对每个时间点 $t$ 从第一个有效输出点开始):**
        i.   计算平滑价格 $SmoothedPrice_t = (4 \cdot Price_t + 3 \cdot Price_{t-1} + 2 \cdot Price_{t-2} + 1 \cdot Price_{t-3}) / 10$。
        ii.  计算调整因子 $Adj_t = (0.075 \cdot Period_{smooth, t-1} + 0.54)$。
        iii. 计算去趋势器 $Detrender_t = (0.0962 \cdot SP_t + 0.5769 \cdot SP_{t-2} - 0.5769 \cdot SP_{t-4} - 0.0962 \cdot SP_{t-6}) \cdot Adj_t$。
        iv.  计算 $I1_t = Detrender_{t-3}$ 和 $Q1_t = (0.0962 \cdot Detrender_t + 0.5769 \cdot Detrender_{t-2} - 0.5769 \cdot Detrender_{t-4} - 0.0962 \cdot Detrender_{t-6}) \cdot Adj_t$。
        v.   计算瞬时相位 $PhaseLagged_t = \arctan(Q1_t / I1_t) \cdot (180/\pi)$。
        vi.  计算相位变化 $\Delta Phase_t = \max(1.0, PhaseLagged_{t-1} - PhaseLagged_t)$。
        vii. 计算自适应平滑系数 $\alpha_t$:
             If $DeltaPhase_t > 1$: $\alpha_t = \max(SlowLimit, FastLimit / DeltaPhase_t)$
             Else: $\alpha_t = FastLimit$
        viii.计算MAMA值 $MAMA_t = \alpha_t \cdot Price_t + (1 - \alpha_t) \cdot MAMA_{t-1}$。
        ix.  更新周期计算的辅助变量 $jI_t, jQ_t, I2_t, Q2_t, Re_t, Im_t$。
             $jI_t = (0.0962 \cdot I1_t + 0.5769 \cdot I1_{t-2} - \dots) \cdot Adj_t$
             $jQ_t = (0.0962 \cdot Q1_t + 0.5769 \cdot Q1_{t-2} - \dots) \cdot Adj_t$
             $I2_t = 0.2 \cdot (I1_t - jQ_t) + 0.8 \cdot I2_{t-1}$
             $Q2_t = 0.2 \cdot (Q1_t + jI_t) + 0.8 \cdot Q2_{t-1}$
             $Re_t = 0.2 \cdot (I2_t \cdot I2_{t-1} + Q2_t \cdot Q2_{t-1}) + 0.8 \cdot Re_{t-1}$
             $Im_t = 0.2 \cdot (I2_t \cdot Q2_{t-1} - Q2_t \cdot I2_{t-1}) + 0.8 \cdot Im_{t-1}$
        x.   计算并更新主导周期 $Period_{smooth,t}$ (经过限制和平滑)。
             $Period_{calc,t} = \frac{360}{\arctan(Im_t/Re_t) \cdot (180/\pi)}$
             $Period_{temp,t}$ 被限制在 $[0.67 \cdot Period_{smooth, t-1}, 1.5 \cdot Period_{smooth, t-1}]$ 和 $[6, 50]$ 范围内。
             $Period_{smooth,t} = 0.2 \cdot Period_{temp,t} + 0.8 \cdot Period_{smooth,t-1}$
3.  **初始化FAMA:**
    $FAMA_0$ 可以设为 $MAMA_0$ 或 0。
4.  **迭代计算FAMA (在得到 $MAMA_t$ 和 $\alpha_t$ 之后):**
    a.  **计算FAMA平滑系数 ($\alpha_{FAMA,t}$):**
        $\alpha_{FAMA,t} = 0.5 \cdot \alpha_t$
    b.  **计算FAMA值 ($FAMA_t$):**
        $FAMA_t = \alpha_{FAMA,t} \cdot MAMA_t + (1 - \alpha_{FAMA,t}) \cdot FAMA_{t-1}$
    c.  保存 $FAMA_t$ 供下一周期使用。

【6. 备注与参数说明】

*   **参数:** FAMA本身不引入新参数，它使用MAMA的`optInFastLimit`和`optInSlowLimit`参数。
*   **依赖性:** FAMA的计算完全依赖于MAMA的输出值 ($MAMA_t$) 和MAMA的自适应平滑系数 ($\alpha_t$)。
*   **特性:** FAMA是MAMA的进一步平滑版本，通常比MAMA线更平滑，滞后性也相应更大一些，但仍然保持了自适应特性。
*   **用途:** FAMA通常与MAMA一起使用，形成交易系统。例如，MAMA线上穿/下穿FAMA线可能被视为交易信号。

【关联因子信息结束】===============================================================

【因子信息结束】===============================================================