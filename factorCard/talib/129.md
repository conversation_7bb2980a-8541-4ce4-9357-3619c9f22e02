【因子信息开始】===============================================================

【因子编号和名称】

因子编号: WMA_001
因子中文名称: 加权移动平均线 (Weighted Moving Average, WMA)

【1. 因子名称详情】

因子全称：加权移动平均线 (Weighted Moving Average)，简称 WMA。它是一种移动平均计算方法，对时间序列中近期的观测值赋予较大的权重，而对较早的观测值赋予较小的权重。权重呈线性递减。

【2. 核心公式】

对于一个给定的时间窗口长度 `N`，在 `t` 时刻的加权移动平均值 `WMA_t` 计算如下：

`WMA_t = ( \sum_{i=1}^{N} w_i \cdot P_{t-N+i} ) / ( \sum_{i=1}^{N} w_i )`

其中，权重 `w_i` 是线性分配的，即 `w_i = i`。因此，最近的数据点 `P_t` 获得权重 `N`，次近的数据点 `P_{t-1}` 获得权重 `N-1`，以此类推，最早的数据点 `P_{t-N+1}` 获得权重 `1`。

公式可以具体写为：

`WMA_t = (1 \cdot P_{t-N+1} + 2 \cdot P_{t-N+2} + \dots + (N-1) \cdot P_{t-1} + N \cdot P_t) / (1 + 2 + \dots + N)`

分母的权重之和为 `\sum_{i=1}^{N} i = N(N+1)/2`。

所以，最终公式为：

`WMA_t = ( \sum_{i=1}^{N} i \cdot P_{t-N+i} ) / ( N(N+1)/2 )`

或者展开为：

`WMA_t = \frac{P_{t-N+1} \cdot 1 + P_{t-N+2} \cdot 2 + \dots + P_{t-1} \cdot (N-1) + P_t \cdot N}{N(N+1)/2}`

【3. 变量定义】

*   `WMA_t`: 在 `t` 时刻计算得到的加权移动平均值。
*   `P_t`: 在 `t` 时刻的输入数据值（例如：收盘价）。
*   `P_{t-k}`: 在 `t-k` 时刻的输入数据值。
*   `N`: 计算移动平均的时间窗口长度（周期数）。
*   `i`: 用于分配权重的计数变量，从1到N。

【4. 函数与方法说明】

该因子主要使用加权求和与算术平均。
*   **加权求和**: `\sum_{i=1}^{N} i \cdot P_{t-N+i}`，表示将时间窗口 `N` 内的每个价格 `P` 乘以其对应的线性权重 `i` (近期价格权重高，远期价格权重低)，然后相加。
*   **权重之和**: `\sum_{i=1}^{N} i = N(N+1)/2`，这是等差数列求和公式，用于计算总权重，作为归一化的分母。

【5. 计算步骤】

计算WMA可以采用一种高效的迭代方法，避免在每个时间步都重新计算所有加权项。假设我们有价格序列 `P_0, P_1, P_2, \dots`。

1.  **数据准备与初始化**:
    *   确定时间窗口长度 `N`。
    *   计算权重之和（分母）：`Divider = N(N+1)/2`。
    *   至少需要 `N` 个数据点才能计算第一个WMA值。第一个WMA值 (记为 `WMA_{N-1}`) 将使用数据 `P_0, P_1, \dots, P_{N-1}`。

2.  **计算第一个 WMA 值** (例如，对于数据点 `P_0, \dots, P_{N-1}`):
    *   分子 `Numerator_{N-1} = P_0 \cdot 1 + P_1 \cdot 2 + \dots + P_{N-1} \cdot N`。
    *   `WMA_{N-1} = Numerator_{N-1} / Divider`。
    *   为了后续迭代计算，同时计算当前窗口内价格的简单和：
        `PriceSum_{N-1} = P_0 + P_1 + \dots + P_{N-1}`。

3.  **迭代计算后续 WMA 值** (例如，计算 `WMA_N` 使用数据 `P_1, \dots, P_N`):
    假设我们已经得到了 `t-1` 时刻的分子 `Numerator_{t-1}` (即 `\sum_{i=1}^{N} i \cdot P_{t-N+i-1}`) 和该窗口的价格简单和 `PriceSum_{t-1}` (即 `\sum_{i=1}^{N} P_{t-N+i-1}`)。
    新的数据点是 `P_t` (新进入窗口的值)。
    离开窗口的数据点是 `P_{t-N}` (最旧的值)。

    a.  **预备分子计算**: 从 `Numerator_{t-1}` 中减去 `PriceSum_{t-1}`。
        `IntermediateNumerator = Numerator_{t-1} - PriceSum_{t-1}`
        *   这一步的效果是将 `Numerator_{t-1}` 中的每个价格项的权重减1。例如，`P_{t-N}` 的权重从1变为0（被移除），`P_{t-N+1}` 的权重从2变为1，以此类推，`P_{t-1}` 的权重从 `N` 变为 `N-1`。
        *   `IntermediateNumerator = P_{t-N+1} \cdot 1 + P_{t-N+2} \cdot 2 + \dots + P_{t-1} \cdot (N-1)`

    b.  **最终分子计算**: 将新的价格 `P_t` 乘以最大权重 `N` 并加到 `IntermediateNumerator` 上。
        `Numerator_t = IntermediateNumerator + P_t \cdot N`
        *   `Numerator_t = P_{t-N+1} \cdot 1 + P_{t-N+2} \cdot 2 + \dots + P_{t-1} \cdot (N-1) + P_t \cdot N`

    c.  **计算 `WMA_t`**:
        `WMA_t = Numerator_t / Divider`

    d.  **更新 `PriceSum` 为下一次迭代准备**:
        `PriceSum_t = PriceSum_{t-1} - P_{t-N} + P_t`

    e.  将 `Numerator_t` 和 `PriceSum_t` 用于计算 `WMA_{t+1}`。重复步骤 a-d。

**特殊情况**: 如果 `N=1`，则 `WMA_t = P_t`。

【6. 备注与参数说明】

*   **时间窗口 (optInTimePeriod, N)**: 这是WMA的核心参数。窗口越小，WMA对价格变化的反应越灵敏；窗口越大，WMA越平滑。常见取值有5, 10, 20, 30, 50, 100, 200等，具体取决于分析周期和资产特性。
*   **数据有效性**: 计算WMA需要至少 `N` 个连续的数据点。输出的WMA序列通常比输入的价格序列短 `N-1` 个点。
*   **权重特性**: WMA赋予近期数据更高的权重，这意味着它比简单移动平均线(SMA)能更快地反映价格趋势的变化。
*   **与其他移动平均线的比较**:
    *   **SMA (Simple Moving Average)**: 所有数据点权重相同。
    *   **EMA (Exponential Moving Average)**: 权重呈指数衰减，近期数据权重也较高，但衰减方式与WMA的线性衰减不同。
    *   WMA 对近期价格的敏感度通常介于 SMA 和 EMA 之间，或者说比 SMA 更敏感。
*   **数据预处理**: 通常使用收盘价作为输入数据。确保数据序列没有缺失值，否则可能需要插值或调整计算起始点。

【因子信息结束】===============================================================