【因子信息开始】===============================================================

【因子编号和名称】

因子编号: MA003
因子中文名称: 双指数移动平均线 (Double Exponential Moving Average, DEMA)

【1. 因子名称详情】

因子1: 双指数移动平均线 (Double Exponential Moving Average, DEMA)。该指标由Patrick G. Mulloy提出，旨在通过减少传统指数移动平均线(EMA)的滞后性来提供更平滑且反应更迅速的移动平均线。

【2. 核心公式】

DEMA的计算基于两次指数移动平均（EMA）的应用：
1.  计算原始数据序列（例如价格）的N周期指数移动平均，记为 `EMA1`。
2.  计算 `EMA1` 序列的N周期指数移动平均，记为 `EMA2`。
3.  DEMA 的计算公式如下：

    $DEMA_t = 2 \cdot EMA1_t - EMA2_t$

其中：
$EMA1_t = EMA_t(\text{Price}, N)$
$EMA2_t = EMA_t(EMA1, N)$

指数移动平均 (EMA) 的计算公式为：
$EMA_t(X, N) = \alpha \cdot X_t + (1 - \alpha) \cdot EMA_{t-1}(X, N)$
其中，平滑系数 $\alpha = \frac{2}{N+1}$。
序列的第一个EMA值 $EMA_0(X,N)$ (或 $EMA_{\text{initial}}(X,N)$) 通常采用该序列前 $N$ 个数据点的简单移动平均 (SMA)，或者直接使用序列的第一个数据点 $X_0$（当N=1或作为一种简化）。

【3. 变量定义】

*   $DEMA_t$: 在时间点 $t$ 的双指数移动平均值。
*   $Price_t$: 在时间点 $t$ 的原始输入数据（通常是收盘价）。
*   $EMA1_t$: 对原始价格序列 $Price$ 在时间点 $t$ 计算的 $N$ 周期指数移动平均值。
*   $EMA2_t$: 对 $EMA1$ 序列在时间点 $t$ 计算的 $N$ 周期指数移动平均值。
*   $N$: 计算EMA时使用的时间周期长度 (例如，10天，30天)。
*   $\alpha$: 指数移动平均的平滑系数。
*   $X_t$: 在时间点 $t$ 的某个输入序列的值 (可以是 $Price_t$ 或 $EMA1_t$）。
*   $EMA_{t-1}(X, N)$: 输入序列 $X$ 在时间点 $t-1$ 的 $N$ 周期指数移动平均值。
*   $SMA(Y, N)$: 对序列 $Y$ 计算的 $N$ 周期简单移动平均值。

【4. 函数与方法说明】

*   **指数移动平均 (EMA - Exponential Moving Average)**:
    EMA是一种加权移动平均，它给予近期数据点比远期数据点更高的权重。其计算方法如下：
    $EMA_t = \alpha \cdot X_t + (1 - \alpha) \cdot EMA_{t-1}$
    其中：
    *   $EMA_t$ 是当前周期的EMA值。
    *   $X_t$ 是当前周期的输入值（例如价格）。
    *   $EMA_{t-1}$ 是前一个周期的EMA值。
    *   $\alpha$ 是平滑系数，计算公式为 $\alpha = \frac{2}{N+1}$，$N$ 是时间周期。
    初始EMA值的计算：第一个EMA值 ($EMA_{\text{initial}}$) 没有前一个EMA值可供计算。通常，它被初始化为输入序列 $X$ 的前 $N$ 个周期的简单移动平均值 (SMA)。即：
    $EMA_{\text{initial}} = \text{SMA}(X_{1 \ldots N}, N) = \frac{X_1 + X_2 + \dots + X_N}{N}$
    在完成初始化后，后续的EMA值均采用递归公式计算。

*   **简单移动平均 (SMA - Simple Moving Average)**:
    SMA是在指定周期 $N$ 内所有数据点的算术平均值。
    $SMA_t(X, N) = \frac{X_t + X_{t-1} + \dots + X_{t-N+1}}{N}$

【5. 计算步骤】

1.  **数据准备**: 获取输入的时间序列数据 $P$ (例如，每日收盘价)。
2.  **设定参数**: 选择时间周期 $N$ (例如，DEMA常用的参数如 `TimePeriod = 30`)。
3.  **计算平滑系数**: 根据选定的周期 $N$，计算平滑系数 $\alpha = \frac{2}{N+1}$。
4.  **计算第一次EMA (EMA1)**:
    *   对原始价格序列 $P$ 计算 $N$ 周期的指数移动平均 $EMA1$。
    *   **初始化**: $EMA1$ 序列的第一个值 $EMA1_{\text{initial}}$ (对应原始价格序列 $P$ 的第 $N$ 个数据点，即 $P_N$)，计算为 $P_1, \dots, P_N$ 的简单移动平均值: $EMA1_N = \text{SMA}(P_{1 \dots N}, N)$。
    *   **递归计算**: 对于 $P$ 序列中第 $N+1$ 个数据点及之后 ($t > N$):
        $EMA1_t = \alpha \cdot P_t + (1 - \alpha) \cdot EMA1_{t-1}$
5.  **计算第二次EMA (EMA2)**:
    *   将步骤4中得到的 $EMA1$ 序列作为新的输入序列，对其计算 $N$ 周期的指数移动平均 $EMA2$。使用相同的平滑系数 $\alpha$。
    *   **初始化**: $EMA2$ 序列的第一个值 $EMA2_{\text{initial}}$ (对应 $EMA1$ 序列的第 $N$ 个有效值，即 $EMA1_{2N-1}$ 对应的原始价格点)，计算为 $EMA1$ 序列的前 $N$ 个值的简单移动平均值。例如，如果 $EMA1$ 序列从 $EMA1_N$ 开始，则 $EMA2_{2N-1} = \text{SMA}(EMA1_{N \dots 2N-1}, N) $。
    *   **递归计算**: 对于 $EMA1$ 序列中第 $N+1$ 个有效值及之后 (即 $t > 2N-1$ 对应的原始价格点):
        $EMA2_t = \alpha \cdot EMA1_t + (1 - \alpha) \cdot EMA2_{t-1}$
        (注意：这里的 $EMA1_t$ 和 $EMA2_{t-1}$ 是指在 $EMA2$ 计算序列中的对应项)
6.  **计算DEMA**:
    *   一旦获得了对应同一时间点的 $EMA1_t$ 和 $EMA2_t$ 值，就可以计算DEMA值。
    *   $DEMA_t = 2 \cdot EMA1_t - EMA2_t$
    *   DEMA的第一个有效值将从原始价格序列的第 $2N-1$ 个数据点开始。例如，如果 $N=10$，则第一个DEMA值对应原始数据的第19个点。

【6. 备注与参数说明】

*   **时间周期 (N)**: 这是DEMA计算中最重要的参数。一个较短的周期（如10）会使DEMA线对价格变化更敏感，产生更多的交易信号，但也可能包含更多噪音。一个较长的周期（如30或50）会使DEMA线更平滑，滞后性相对较小但仍然存在，产生的交易信号较少，但可能更可靠。默认值通常是30。
*   **数据预处理**: 通常使用收盘价作为输入。确保输入数据序列没有缺失值，或者对缺失值进行了合理填充。
*   **起始有效数据点 (Lookback Period)**: 由于DEMA涉及两次EMA计算，且每次EMA计算（若使用SMA初始化）都需要 $N$ 个周期的数据来计算其第一个值，因此DEMA的第一个有效输出值需要 $N + (N-1) = 2N-1$ 个原始数据点。所以其有效起始位置相对于原始数据序列有 $2N-2$ 个周期的延迟。
*   **与EMA2的区别**: DEMA不同于简单地对EMA再进行一次EMA平滑（有时被称为EMA的EMA，或EMA2）。DEMA的公式 ($2 \cdot EMA1 - EMA2$) 是特意设计用来减少滞后性的。
*   **用途**: DEMA常用于趋势识别和产生交易信号，其设计目标是比传统的EMA或SMA更快地响应价格变化。

【因子信息结束】===============================================================