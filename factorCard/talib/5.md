【因子信息开始】===============================================================

【因子编号和名称】

因子编号: AD001: 蔡金累积/派发线 (Chaikin Accumulation/Distribution Line, ADL)

【1. 因子名称详情】

因子AD001: 蔡金累积/派发线 (Chaikin Accumulation/Distribution Line, ADL)。该指标通过累积每日的资金流量来衡量资金是流入还是流出证券。

【2. 核心公式】

该因子的核心思想是计算每个周期的“资金流量乘数”(Money Flow Multiplier, MFM)，然后将其乘以该周期的成交量得到“资金流量”(Money Flow Volume, MFV)。最后，将这些资金流量进行累积得到累积/派发线 (ADL)。

1.  **资金流量乘数 (Money Flow Multiplier, MFM)**:
    $MFM_t = \frac{(C_t - L_t) - (H_t - C_t)}{H_t - L_t}$
    其中，如果 $H_t - L_t = 0$，则该周期的资金流量贡献视为0（即 $MFM_t \times V_t = 0$ for ADL calculation）。

2.  **资金流量 (Money Flow Volume, MFV)**:
    $MFV_t = MFM_t \times V_t$

3.  **累积/派发线 (Accumulation/Distribution Line, ADL)**:
    $ADL_t = ADL_{t-1} + MFV_t$
    初始值 $ADL_0$ 通常设为0，然后开始累积。所以，在实际编码中，可以理解为 $ADL_t$ 是从序列开始到 $t$ 时刻的 $MFV$ 的累积和。

    $ADL_t = \sum_{i=1}^{t} MFV_i = \sum_{i=1}^{t} \left( \frac{(C_i - L_i) - (H_i - C_i)}{H_i - L_i} \times V_i \right)$
    (对于 $H_i - L_i = 0$ 的情况，该项 $MFV_i$ 为 0)。

【3. 变量定义】

*   $ADL_t$: $t$ 时刻的蔡金累积/派发线值。
*   $ADL_{t-1}$: $t-1$ 时刻的蔡金累积/派发线值。
*   $MFM_t$: $t$ 时刻的资金流量乘数。
*   $MFV_t$: $t$ 时刻的资金流量。
*   $C_t$: $t$ 时刻的收盘价 (Close Price)。
*   $L_t$: $t$ 时刻的最低价 (Low Price)。
*   $H_t$: $t$ 时刻的最高价 (High Price)。
*   $V_t$: $t$ 时刻的成交量 (Volume)。
*   $i$: 时间序列中的特定周期索引。

【4. 函数与方法说明】

*   **资金流量乘数 (Money Flow Multiplier, MFM)**:
    *   计算逻辑: 该乘数衡量收盘价在当日价格区间 (最高价与最低价之间) 的位置。
        *   分子 $(C_t - L_t) - (H_t - C_t)$ 可以化简为 $2C_t - L_t - H_t$。
        *   分母是当日价格振幅 $H_t - L_t$。
    *   取值范围: MFM 的值域为 \[-1, 1]。
        *   当 $C_t = H_t$ 时 (收盘价为最高价)，$MFM_t = \frac{(H_t - L_t) - (H_t - H_t)}{H_t - L_t} = \frac{H_t - L_t}{H_t - L_t} = 1$ (假设 $H_t \neq L_t$)。
        *   当 $C_t = L_t$ 时 (收盘价为最低价)，$MFM_t = \frac{(L_t - L_t) - (H_t - L_t)}{H_t - L_t} = \frac{-(H_t - L_t)}{H_t - L_t} = -1$ (假设 $H_t \neq L_t$)。
        *   当 $C_t = \frac{H_t + L_t}{2}$ 时 (收盘价为区间中点)，$MFM_t = 0$。
    *   特殊情况处理: 当 $H_t = L_t$ (即日内价格无波动) 时，分母为0。在这种情况下，该周期的资金流量贡献被视为0，即 $ADL_t$ 保持与 $ADL_{t-1}$ 相同。

*   **资金流量 (Money Flow Volume, MFV)**:
    *   计算逻辑: 将资金流量乘数 $MFM_t$ 乘以当日成交量 $V_t$。这代表了当日基于价格位置加权的成交量，反映了资金的流入或流出强度。

*   **累积和 (Cumulative Sum)**:
    *   计算逻辑: ADL 是通过将每个周期的资金流量 (MFV) 逐期累加得到的。它显示了从某个起始点开始，资金是持续流入还是流出该证券。

【5. 计算步骤】

1.  **数据准备**: 获取时间序列数据，包括每个周期 $t$ 的最高价 $H_t$、最低价 $L_t$、收盘价 $C_t$ 和成交量 $V_t$。
2.  **初始化**: 设置累积/派发线的初始值。通常，在计算序列的第一个点之前，可以认为 $ADL_{\text{initial}} = 0.0$。
3.  **迭代计算**: 从数据序列的第一个周期 $(t=1)$ 开始，到最后一个周期 $(t=N)$，依次执行以下操作：
    a.  获取当前周期的 $H_t, L_t, C_t, V_t$。
    b.  计算当日价格区间振幅: $Range_t = H_t - L_t$。
    c.  **条件判断与MFV计算**:
        i.  如果 $Range_t > 0$:
            计算资金流量乘数: $MFM_t = \frac{(C_t - L_t) - (H_t - C_t)}{Range_t}$。
            计算当日资金流量: $MFV_t = MFM_t \times V_t$。
        ii. 如果 $Range_t \le 0$ (即最高价等于最低价，或数据异常):
            当日资金流量视为零: $MFV_t = 0$。
    d.  **更新累积/派发线**:
        如果这是第一个周期 ($t=1$): $ADL_1 = ADL_{\text{initial}} + MFV_1 = 0 + MFV_1 = MFV_1$。
        如果 $t > 1$: $ADL_t = ADL_{t-1} + MFV_t$。
4.  **输出**: 得到的 $ADL_t$ 序列即为蔡金累积/派发线。

【6. 备注与参数说明】

*   **无显式参数**: 该因子本身没有可调的窗口期参数。它是一个纯粹的累积指标。
*   **起始点依赖性**: ADL的绝对数值会因计算起始点和初始值的设定而异。然而，指标的趋势和形态（例如，是上升、下降还是震荡，以及与价格走势的背离）比其绝对值更具分析意义。通常从可获得数据的最早点开始计算。
*   **数据精度**: 计算过程中涉及浮点数运算和累积。使用双精度浮点数（double）进行计算可以获得更高的精度，尤其是在长序列数据上，可以减少累积误差。
*   **$H_t = L_t$ 的处理**: 当周期内最高价等于最低价时，价格振幅为零。此时，资金流量乘数的分母为零，无法直接计算。根据源码逻辑，在这种情况下，当期的资金流量贡献被视为0，即ADL值不发生变化，继承前一期的值。
*   **解释与应用**:
    *   ADL上升通常表示资金流入（累积），利好价格。
    *   ADL下降通常表示资金流出（派发），利空价格。
    *   ADL与价格走势的背离是重要的交易信号。例如，价格创新高而ADL未能创新高，可能预示顶部；价格创新低而ADL未能创新低，可能预示底部。

【因子信息结束】===============================================================